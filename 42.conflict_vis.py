import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import matplotlib.animation as animation
from matplotlib.patches import Ellipse
import pickle
import os
from pathlib import Path
from tqdm import tqdm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class ManeuverVisualizationEngine:
    """
    机动检测可视化引擎
    统一管理所有可视化功能，包括真实地理比例、航道显示、统一控制接口
    """

    def __init__(self):
        """初始化可视化引擎"""
        self.base_map = None
        self.base_map_extent = [121.050, 121.350, 31.516, 31.784]  # 底图地理范围
        self._load_base_map()

    def _load_base_map(self):
        """加载底图"""
        map_path = 'data/map0.png'
        if os.path.exists(map_path):
            try:
                self.base_map = mpimg.imread(map_path)
                print(f"✅ 底图加载成功")
            except Exception as e:
                print(f"❌ 底图加载失败: {e}")
                self.base_map = None
        else:
            print("❌ 底图文件不存在")
            self.base_map = None

    def _draw_channel_boundaries(self, ax, show_channel=True):
        """绘制航道边界线"""
        if not show_channel:
            return False

        boundary_drawn = False
        try:
            with open('data/geo_info.pkl', 'rb') as f:
                geo_info = pickle.load(f)

            # 绘制航道边界1
            if 'channel_side1' in geo_info:
                side1_data = geo_info['channel_side1']
                if side1_data and len(side1_data) > 0:
                    side1_lons = [p[0] for p in side1_data]
                    side1_lats = [p[1] for p in side1_data]
                    ax.plot(side1_lons, side1_lats, 'green', linewidth=3, alpha=0.8, linestyle='--')
                    boundary_drawn = True

            # 绘制航道边界2
            if 'channel_side2' in geo_info:
                side2_data = geo_info['channel_side2']
                if side2_data and len(side2_data) > 0:
                    side2_lons = [p[0] for p in side2_data]
                    side2_lats = [p[1] for p in side2_data]
                    ax.plot(side2_lons, side2_lats, 'green', linewidth=3, alpha=0.8, linestyle='--')
                    boundary_drawn = True

            # 绘制航道中心线
            if 'channel_centerline' in geo_info:
                centerline_data = geo_info['channel_centerline']
                if centerline_data and len(centerline_data) > 0:
                    center_lons = [p[0] for p in centerline_data]
                    center_lats = [p[1] for p in centerline_data]
                    ax.plot(center_lons, center_lats, 'green', linewidth=2,
                            alpha=0.6, linestyle='--')
                    boundary_drawn = True

        except Exception as e:
            print(f"航道边界线处理失败: {e}")

        return boundary_drawn

    def _calculate_display_extent(self, trajectory_df, expand_ratio=0.3):
        """根据轨迹数据计算显示范围"""
        lon_min, lon_max = trajectory_df['Lon'].min(), trajectory_df['Lon'].max()
        lat_min, lat_max = trajectory_df['Lat'].min(), trajectory_df['Lat'].max()

        lon_range = lon_max - lon_min
        lat_range = lat_max - lat_min

        lon_margin = max(lon_range * expand_ratio, 0.01)
        lat_margin = max(lat_range * expand_ratio, 0.01)

        display_extent = [
            lon_min - lon_margin,
            lon_max + lon_margin,
            lat_min - lat_margin,
            lat_max + lat_margin
        ]

        return display_extent

    def _calculate_figure_size(self, display_extent, base_width=12):
        """根据显示范围计算合适的图片尺寸，保持真实的地理比例"""
        lon_range = display_extent[1] - display_extent[0]
        lat_range = display_extent[3] - display_extent[2]

        center_lat = (display_extent[2] + display_extent[3]) / 2
        lat_km_per_degree = 111.0
        lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))

        actual_lon_distance = lon_range * lon_km_per_degree
        actual_lat_distance = lat_range * lat_km_per_degree
        geographic_aspect_ratio = actual_lat_distance / actual_lon_distance

        height = base_width * geographic_aspect_ratio
        height = max(4, min(height, 20))

        return (base_width, height)

    def _setup_map_with_trajectory_extent(self, ax, trajectory_df, expand_ratio=0.3):
        """根据轨迹范围设置底图显示，保持真实地理比例"""
        if self.base_map is None:
            return None

        display_extent = self._calculate_display_extent(trajectory_df, expand_ratio)

        display_extent[0] = max(display_extent[0], self.base_map_extent[0])
        display_extent[1] = min(display_extent[1], self.base_map_extent[1])
        display_extent[2] = max(display_extent[2], self.base_map_extent[2])
        display_extent[3] = min(display_extent[3], self.base_map_extent[3])

        ax.imshow(self.base_map, extent=self.base_map_extent, aspect='auto', alpha=0.7)
        ax.set_xlim(display_extent[0], display_extent[1])
        ax.set_ylim(display_extent[2], display_extent[3])

        center_lat = (display_extent[2] + display_extent[3]) / 2
        lon_km_per_degree = 111.0 * np.cos(np.radians(center_lat))
        lat_km_per_degree = 111.0
        aspect_ratio = lon_km_per_degree / lat_km_per_degree
        ax.set_aspect(aspect_ratio)

        return display_extent

    def visualize_conflict_trajectory(self, segment_data, expand_ratio=0.3, figsize_base_width=12,
                                      show_channel=True, output_dir='vis/conflict_animations'):
        """
        可视化冲突段的船舶位置和对应的船舶领域，生成动图

        Args:
            segment_data: 冲突段数据，包含两船的位置和冲突信息
            expand_ratio: 扩展比例
            figsize_base_width: 图片基础宽度
            show_channel: 是否显示航道边界线
            output_dir: 输出目录
        """

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 提取数据
        man_data = segment_data['maneuvering_ship']['full_segment_data']
        other_data = segment_data['other_ship']['full_segment_data']

        # 提取船舶长度和场景类型
        man_length = segment_data['maneuvering_ship']['length']
        other_length = segment_data['other_ship']['length']
        scene_type = segment_data['scene_type']

        # 获取所有位置点用于计算显示范围
        all_lons = list(man_data['longitudes']) + list(other_data['longitudes'])
        all_lats = list(man_data['latitudes']) + list(other_data['latitudes'])

        # 计算显示范围
        lon_min, lon_max = min(all_lons), max(all_lons)
        lat_min, lat_max = min(all_lats), max(all_lats)

        lon_range = lon_max - lon_min
        lat_range = lat_max - lat_min

        # 扩展显示范围
        lon_expand = lon_range * expand_ratio
        lat_expand = lat_range * expand_ratio

        display_extent = [
            lon_min - lon_expand, lon_max + lon_expand,
            lat_min - lat_expand, lat_max + lat_expand
        ]

        # 计算图片尺寸
        figsize = self._calculate_figure_size(display_extent, base_width=figsize_base_width)

        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=figsize)

        def animate(frame):
            ax.clear()

            # 设置底图
            if self.base_map is not None:
                ax.imshow(self.base_map, extent=self.base_map_extent, aspect='auto', alpha=0.7)

            # 设置显示范围
            ax.set_xlim(display_extent[0], display_extent[1])
            ax.set_ylim(display_extent[2], display_extent[3])

            # 绘制航道边界线
            self._draw_channel_boundaries(ax, show_channel)

            # 获取当前帧的数据
            if frame < len(man_data['times']) and frame < len(other_data['times']):
                # 确保索引在有效范围内
                if (frame < len(man_data['longitudes']) and frame < len(man_data['latitudes']) and
                        frame < len(man_data['courses']) and frame < len(other_data['longitudes']) and
                        frame < len(other_data['latitudes']) and frame < len(other_data['courses'])):
                    # 机动船位置和航向
                    man_lon = man_data['longitudes'][frame]
                    man_lat = man_data['latitudes'][frame]
                    man_cog = man_data['courses'][frame]

                    # 其他船位置和航向
                    other_lon = other_data['longitudes'][frame]
                    other_lat = other_data['latitudes'][frame]
                    other_cog = other_data['courses'][frame]

                    # 绘制船舶位置
                    ax.plot(man_lon, man_lat, 'ro', markersize=8)
                    ax.plot(other_lon, other_lat, 'bo', markersize=8)

                    # 绘制船舶领域椭圆
                    self._draw_ship_domain(ax, man_lon, man_lat, man_cog, 'red', scene_type, man_length)
                    self._draw_ship_domain(ax, other_lon, other_lat, other_cog, 'blue', scene_type, other_length)

            ax.set_xlabel('经度')
            ax.set_ylabel('纬度')
            ax.grid(True, alpha=0.3)

        # 创建动画 - 使用最小的数据长度确保安全
        frames = min(len(man_data['times']), len(other_data['times']),
                     len(man_data['longitudes']), len(man_data['latitudes']),
                     len(other_data['longitudes']), len(other_data['latitudes']),
                     len(man_data['courses']), len(other_data['courses']))

        if frames == 0:
            raise ValueError("没有有效的动画帧数据")

        # print(f"创建动画，帧数: {frames}")
        ani = animation.FuncAnimation(fig, animate, frames=frames, interval=500, repeat=True, blit=False)

        # 保存动画
        output_file = os.path.join(output_dir, f"conflict_segment_{segment_data['trajectory_index']}.gif")
        ani.save(output_file, writer='pillow', fps=2, dpi=150)

        plt.close(fig)
        # print(f"✅ 冲突段动画已保存: {output_file}")

    def _draw_ship_domain(self, ax, lon, lat, cog, color, scene_type, ship_length):
        """
        绘制船舶领域椭圆

        Args:
            ax: matplotlib轴对象
            lon: 船舶经度
            lat: 船舶纬度
            cog: 船舶航向（正北为0，顺时针增加）
            color: 椭圆颜色
            scene_type: 场景类型
            ship_length: 船舶长度
        """
        # 根据场景类型和船舶长度获取椭圆参数（单位：米）
        # 直接在这里实现 get_ab 的逻辑
        if scene_type == 'crossing':
            if ship_length <= 100:
                a_meters, b_meters = 271, 192
            else:
                a_meters, b_meters = 375, 210
        else:  # overtaking
            if ship_length <= 100:
                a_meters, b_meters = 180, 85
            else:
                a_meters, b_meters = 290, 120

        # 将米转换为度（近似转换，适用于小范围）
        # 1度经度 ≈ 111320米 * cos(纬度)，1度纬度 ≈ 111320米
        lat_rad = np.radians(lat)
        meters_per_degree_lon = 111320 * np.cos(lat_rad)
        meters_per_degree_lat = 111320

        a_degrees = a_meters / meters_per_degree_lon  # 长半轴（经度方向）
        b_degrees = b_meters / meters_per_degree_lat  # 短半轴（纬度方向）

        # 航向转换：正北为0顺时针 -> 数学角度（东为0逆时针）
        angle = 90 - cog

        # 创建椭圆
        ellipse = Ellipse((lon, lat), width=2 * a_degrees, height=2 * b_degrees, angle=angle,
                          facecolor=color, alpha=0.3, edgecolor=color, linewidth=1)
        ax.add_patch(ellipse)

    def batch_visualize_conflict_segments(self, segments_data, scene_type, output_dir='vis/conflict_animations'):
        """
        批量可视化所有冲突段

        Args:
            segments_data: 冲突段数据列表
            scene_type: 场景类型（用于子目录命名）
            output_dir: 输出目录
        """
        if not segments_data:
            print(f"无{scene_type}冲突段数据可视化")
            return

        # 创建场景类型子目录
        scene_output_dir = os.path.join(output_dir, scene_type)
        os.makedirs(scene_output_dir, exist_ok=True)

        print(f"开始生成{scene_type}场景冲突段动画...")

        for i, segment in enumerate(tqdm(segments_data)):
            try:
                self.visualize_conflict_trajectory(segment, output_dir=scene_output_dir)
            except Exception as e:
                print(f"❌ 生成第{i + 1}个冲突段动画失败: {e}")
                continue

        print(f"✅ {scene_type}场景冲突段动画生成完成，共{len(segments_data)}个")


if __name__ == '__main__':
    import pickle

    # with open('result/trajectories/2024_1/crossing_valid_segments.pkl', 'rb') as f:
    #     crossing_segments_data = pickle.load(f)
    # with open('result/trajectories/2024_1/overtaking_valid_segments.pkl', 'rb') as f:
    #     overtaking_segments_data = pickle.load(f)

    with open('result/trajectories/2024_1/crossing_valid_full_segments.pkl', 'rb') as f:
        crossing_segments_data = pickle.load(f)

    # 创建可视化引擎并生成冲突段动画
    print("\n🎬 开始生成冲突段动画...")
    viz_engine = ManeuverVisualizationEngine()

    # 生成交叉场景冲突段动画
    viz_engine.batch_visualize_conflict_segments(crossing_segments_data, "crossing")

    # # 生成追越场景冲突段动画
    # viz_engine.batch_visualize_conflict_segments(overtaking_segments_data, "overtaking")

    print("\n🎬 所有冲突段动画生成完成！")
