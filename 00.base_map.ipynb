#%%
# basemap-首选1
import cartopy.crs as ccrs
import contextily as ctx
import matplotlib.pyplot as plt

# 创建地图
ax = plt.axes(projection=ccrs.PlateCarree())
# ax.set_extent([121.867, 122.367, 29.717, 30.033], crs=ccrs.PlateCarree())  # 宁波舟山
ax.set_extent([121.050, 121.350, 31.516, 31.784], crs=ccrs.PlateCarree())    # 交叉1
# 转换为Web墨卡托坐标系（与在线瓦片对齐）
ax_wm = ax.figure.add_subplot(111, projection=ccrs.epsg(3857))
ax_wm.set_extent(ax.get_extent(ccrs.PlateCarree()), crs=ccrs.PlateCarree())

ctx.add_basemap(
    ax_wm,
    source='https://abcd.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png',
    attribution='',  # 取消版权信息   OpenStreetMap contributors & USGS
    zoom=14
)
# ctx.add_basemap(
#     ax_wm,
#     source=ctx.providers.CartoDB.PositronNoLabels,
#     attribution='',  # 取消版权信息
#     zoom=14
# )

# 'MicrosoftImagery', 'MicrosoftBaseDarkGrey', 'MicrosoftBaseRoad', 'MicrosoftBaseHybridRoad', 'MicrosoftTerraMain', 'MicrosoftWeatherInfraredMain', 'MicrosoftWeatherRadarMain'
# 移除框线和白边
ax_wm.set_axis_off()
plt.subplots_adjust(top=1, bottom=0, right=0.6, left=0, hspace=0, wspace=0)
plt.margins(0, 0)

# 保存为图片
# plt.savefig('vis/map1.png', dpi=900, bbox_inches='tight', pad_inches=0)
plt.savefig('data/map0.png', dpi=600, bbox_inches='tight', pad_inches=0)
plt.show()
"""
OpenStreetMap 相关
ctx.providers.OpenStreetMap.Mapnik：标准的 OpenStreetMap 地图。
ctx.providers.OpenStreetMap.DE：德国的 OpenStreetMap 地图。

CartoDB 地图
ctx.providers.CartoDB.Positron：浅色风格的地图，适合数据可视化。
ctx.providers.CartoDB.PositronNoLabels：无标签的浅色风格地图。

print(list(ctx.providers.keys()))
print(ctx.providers['CartoDB'].keys())
"""