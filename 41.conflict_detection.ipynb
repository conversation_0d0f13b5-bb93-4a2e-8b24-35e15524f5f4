#%% md
### 1.会遇场景轨迹提取
#%%
"""
加载crossing_avoidance_scenes.pkl和overtaking_avoidance_scenes.pkl
提取每种场景中会遇船舶在会遇时间范围内的轨迹

数据结构说明：
- 每个避让场景：[机动船信息, 非机动船信息]
- 船舶信息字典：{'mmsi': int, 'lon': float, 'lat': float, 'cog': float, 'sog': float,
                'length': float, 'width': float, 'time_point': int}
"""

import pickle
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
from tqdm import tqdm


class EncounterTrajectoryExtractor:
    """会遇场景轨迹提取器"""

    def __init__(self, data_time='2024_1'):
        """
        初始化轨迹提取器

        Args:
            data_time: 数据时间标识，如 '2024_1', '2024_2', '2024_3'
        """
        self.data_time = data_time
        self.crossing_scenes = []
        self.overtaking_scenes = []
        self.tras_df = None

        # 加载基础数据
        self._load_basic_data()

    def _load_basic_data(self):
        """加载基础轨迹数据"""
        print(f"正在加载 {self.data_time} 的基础数据...")

        # 加载轨迹数据
        tras_file = f'data/tras_{self.data_time}_inter.parquet'
        if Path(tras_file).exists():
            self.tras_df = pd.read_parquet(tras_file)
            print(f"✅ 已加载轨迹数据: {len(self.tras_df)} 条记录")
        else:
            raise FileNotFoundError(f"未找到轨迹数据文件: {tras_file}")

    def load_avoidance_scenes(self):
        """加载避让场景数据"""
        print(f"正在加载 {self.data_time} 的避让场景...")

        # 加载交叉避让场景
        crossing_file = f'result/{self.data_time}/crossing_avoidance_scenes.pkl'
        if Path(crossing_file).exists():
            with open(crossing_file, 'rb') as f:
                self.crossing_scenes = pickle.load(f)
            print(f"✅ 已加载交叉避让场景: {len(self.crossing_scenes)} 个")
        else:
            print(f"⚠️  未找到交叉避让场景文件: {crossing_file}")

        # 加载追越避让场景
        overtaking_file = f'result/{self.data_time}/overtaking_avoidance_scenes.pkl'
        if Path(overtaking_file).exists():
            with open(overtaking_file, 'rb') as f:
                self.overtaking_scenes = pickle.load(f)
            print(f"✅ 已加载追越避让场景: {len(self.overtaking_scenes)} 个")
        else:
            print(f"⚠️  未找到追越避让场景文件: {overtaking_file}")

    def extract_encounter_trajectories(self, time_window_minutes=5):
        """
        提取会遇船舶在会遇时间范围内的轨迹

        Args:
            time_window_minutes: 时间窗口大小（分钟），在会遇时刻前后各扩展的时间

        Returns:
            dict: 包含交叉和追越场景轨迹的字典
        """
        print(f"正在提取会遇轨迹（时间窗口: ±{time_window_minutes}分钟）...")

        # 时间窗口转换为秒
        time_window_seconds = time_window_minutes * 60

        results = {
            'crossing_trajectories': [],
            'overtaking_trajectories': []
        }

        # 处理交叉避让场景
        if self.crossing_scenes:
            print("处理交叉避让场景...")
            crossing_trajectories = self._extract_trajectories_from_scenes(
                self.crossing_scenes, 'crossing', time_window_seconds
            )
            results['crossing_trajectories'] = crossing_trajectories

        # 处理追越避让场景
        if self.overtaking_scenes:
            print("处理追越避让场景...")
            overtaking_trajectories = self._extract_trajectories_from_scenes(
                self.overtaking_scenes, 'overtaking', time_window_seconds
            )
            results['overtaking_trajectories'] = overtaking_trajectories

        return results

    def _extract_trajectories_from_scenes(self, scenes, scene_type, time_window_seconds):
        """
        从场景列表中提取轨迹

        Args:
            scenes: 场景列表
            scene_type: 场景类型 ('crossing' 或 'overtaking')
            time_window_seconds: 时间窗口（秒）

        Returns:
            list: 轨迹数据列表
        """
        trajectories = []

        for i, scene in enumerate(tqdm(scenes, desc=f"提取{scene_type}轨迹")):
            try:
                # 解析场景数据
                maneuvering_ship = scene[0]  # 机动船信息
                other_ship = scene[1]  # 非机动船信息

                encounter_time = maneuvering_ship['time_point']

                # 计算时间范围
                start_time = encounter_time - time_window_seconds
                end_time = encounter_time + time_window_seconds

                # 提取两船的轨迹
                maneuvering_trajectory = self._get_ship_trajectory_in_timerange(
                    maneuvering_ship['mmsi'], start_time, end_time
                )
                other_trajectory = self._get_ship_trajectory_in_timerange(
                    other_ship['mmsi'], start_time, end_time
                )

                # 构建轨迹数据
                trajectory_data = {
                    'scene_id': f"{scene_type}_{i}",
                    'scene_type': scene_type,
                    'encounter_time': encounter_time,
                    'time_window': {
                        'start': start_time,
                        'end': end_time
                    },
                    'maneuvering_ship': {
                        'mmsi': maneuvering_ship['mmsi'],
                        'encounter_state': maneuvering_ship,
                        'trajectory': maneuvering_trajectory
                    },
                    'other_ship': {
                        'mmsi': other_ship['mmsi'],
                        'encounter_state': other_ship,
                        'trajectory': other_trajectory
                    }
                }

                trajectories.append(trajectory_data)

            except Exception as e:
                print(f"⚠️  处理场景 {i} 时出错: {e}")
                continue

        return trajectories

    def _get_ship_trajectory_in_timerange(self, mmsi, start_time, end_time):
        """
        获取指定船舶在时间范围内的轨迹

        Args:
            mmsi: 船舶MMSI
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            pd.DataFrame: 轨迹数据
        """
        # 筛选指定船舶和时间范围的数据
        trajectory = self.tras_df[
            (self.tras_df['MMSI'] == mmsi) &
            (self.tras_df['PosTime'] >= start_time) &
            (self.tras_df['PosTime'] <= end_time)
            ].copy()

        # 按时间排序
        trajectory = trajectory.sort_values('PosTime')

        return trajectory

    def save_trajectories(self, trajectories, output_dir='result/trajectories'):
        """
        保存提取的轨迹数据

        Args:
            trajectories: 轨迹数据字典
            output_dir: 输出目录
        """
        output_path = Path(output_dir) / self.data_time
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存交叉避让轨迹
        if trajectories['crossing_trajectories']:
            crossing_file = output_path / 'crossing_encounter_trajectories.pkl'
            with open(crossing_file, 'wb') as f:
                pickle.dump(trajectories['crossing_trajectories'], f)
            print(f"✅ 交叉避让轨迹已保存: {crossing_file}")
            print(f"   包含 {len(trajectories['crossing_trajectories'])} 个场景")

        # 保存追越避让轨迹
        if trajectories['overtaking_trajectories']:
            overtaking_file = output_path / 'overtaking_encounter_trajectories.pkl'
            with open(overtaking_file, 'wb') as f:
                pickle.dump(trajectories['overtaking_trajectories'], f)
            print(f"✅ 追越避让轨迹已保存: {overtaking_file}")
            print(f"   包含 {len(trajectories['overtaking_trajectories'])} 个场景")

    def print_summary(self, trajectories):
        """打印提取结果摘要"""
        print(f"\n📊 轨迹提取摘要 ({self.data_time}):")
        print("=" * 50)

        crossing_count = len(trajectories['crossing_trajectories'])
        overtaking_count = len(trajectories['overtaking_trajectories'])
        total_count = crossing_count + overtaking_count

        print(f"总场景数: {total_count}")
        print(f"├─ 交叉避让场景: {crossing_count}")
        print(f"└─ 追越避让场景: {overtaking_count}")

        # 统计轨迹点数
        if crossing_count > 0:
            crossing_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['crossing_trajectories']
            )
            print(f"\n交叉避让轨迹点数: {crossing_points}")

        if overtaking_count > 0:
            overtaking_points = sum(
                len(traj['maneuvering_ship']['trajectory']) + len(traj['other_ship']['trajectory'])
                for traj in trajectories['overtaking_trajectories']
            )
            print(f"追越避让轨迹点数: {overtaking_points}")


def main():
    """主函数"""
    # 可以处理多个月份的数据
    data_times = ['2024_1', '2024_2', '2024_3']

    for data_time in data_times:
        print(f"\n{'=' * 60}")
        print(f"处理 {data_time} 数据")
        print(f"{'=' * 60}")

        try:
            # 创建提取器
            extractor = EncounterTrajectoryExtractor(data_time)

            # 加载避让场景
            extractor.load_avoidance_scenes()

            # 提取轨迹（时间窗口可调整）
            trajectories = extractor.extract_encounter_trajectories(time_window_minutes=10)

            # 保存结果
            extractor.save_trajectories(trajectories)

            # 打印摘要
            extractor.print_summary(trajectories)

        except Exception as e:
            print(f"❌ 处理 {data_time} 时出错: {e}")
            continue


if __name__ == '__main__':
    main()

#%% md
### 2.批量冲突计算
#%%
import pickle

with open('result/trajectories/2024_1/crossing_encounter_trajectories.pkl', 'rb') as f:
    crossing_trajectories = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_encounter_trajectories.pkl', 'rb') as f:
    overtaking_trajectories = pickle.load(f)
#%%
import pandas as pd
import numpy as np
from shapely.geometry import Point
from shapely.affinity import scale, rotate, translate
from tqdm import tqdm
from methods.trans import Trans

trans = Trans()


def overlap_ratios_shapely(m1, n1, A1, B1, phi1,
                           m2, n2, A2, B2, phi2,
                           circle_resolution=256):
    """
    计算两椭圆交集面积分别占各自面积的比值，使用 Shapely。

    参数:
      - m1,n1,A1,B1,phi1: 椭圆1 的中心、长短半轴及旋转角（度）
      - m2,n2,A2,B2,phi2: 椭圆2 同上
      - circle_resolution: 用于近似圆的分段数，越大结果越精确

    返回:
      (ratio1, ratio2)，其中
        ratio1 = area(intersection) / (π A1 B1)
        ratio2 = area(intersection) / (π A2 B2)
    """
    # 构造单位圆
    unit_circle = Point(0, 0).buffer(1, resolution=circle_resolution)

    # 椭圆1：先缩放，再旋转，最后平移
    e1 = scale(unit_circle, A1, B1)
    e1 = rotate(e1, phi1, origin=(0, 0), use_radians=False)
    e1 = translate(e1, m1, n1)

    # 椭圆2
    e2 = scale(unit_circle, A2, B2)
    e2 = rotate(e2, phi2, origin=(0, 0), use_radians=False)
    e2 = translate(e2, m2, n2)

    # 求交集
    inter = e1.intersection(e2)
    area_inter = inter.area

    # 各自面积
    area1 = np.pi * A1 * B1
    area2 = np.pi * A2 * B2

    return max(area_inter / area1, area_inter / area2)


def get_ab(scence_type, length):
    if scence_type == 'crossing':
        if length <= 100:
            return 271, 192
        else:
            return 375, 210
    else:
        if length <= 100:
            return 180, 85
        else:
            return 290, 120


def conflict_detection(trajectories, scene_type='crossing'):
    Conflicts = []
    for tras in tqdm(trajectories):
        df1 = tras['maneuvering_ship']['trajectory']
        df2 = tras['other_ship']['trajectory']

        a1, b1 = get_ab(scene_type, df1['Length'].values[0])
        a2, b2 = get_ab(scene_type, df2['Length'].values[0])
        mmsi1 = df1['MMSI'].values[0]
        mmsi2 = df2['MMSI'].values[0]

        common_times = np.intersect1d(df1['PosTime'].values, df2['PosTime'].values)
        df1_aligned = df1[df1['PosTime'].isin(common_times)].reset_index(drop=True)
        df2_aligned = df2[df2['PosTime'].isin(common_times)].reset_index(drop=True)

        X1, Y1 = trans.LonLat2Gauss(df1_aligned['Lon'].values, df1_aligned['Lat'].values)
        X2, Y2 = trans.LonLat2Gauss(df2_aligned['Lon'].values, df2_aligned['Lat'].values)

        Cog1 = df1_aligned['Cog'].values
        Cog2 = df2_aligned['Cog'].values

        conflicts = [overlap_ratios_shapely(X1[i], Y1[i], a1, b1, Cog1[i],
                                            X2[i], Y2[i], a2, b2, Cog2[i])
                     for i in range(len(common_times))]
        Conflicts.append(pd.DataFrame(
            {'MMSI': [(mmsi1, mmsi2) for _ in range(len(common_times))], 'PosTime': common_times,
             'Conflict': conflicts}))
    return Conflicts


Conflicts_crossing = conflict_detection(crossing_trajectories, scene_type='crossing')
Conflicts_overtaking = conflict_detection(overtaking_trajectories, scene_type='overtaking')
with open('result/trajectories/2024_1/crossing_conflicts.pkl', 'wb') as f:
    pickle.dump(Conflicts_crossing, f)
with open('result/trajectories/2024_1/overtaking_conflicts.pkl', 'wb') as f:
    pickle.dump(Conflicts_overtaking, f)
#%% md
### 3.改进的冲突趋势检测（保存MMSI、经纬度、冲突值）
#%%
import pickle
import numpy as np


def filter_pattern_trajectories(conflicts_list, trajectories_list, min_conflict_threshold=0.5):
    """
    筛选符合条件的轨迹：冲突从0开始，逐渐增大到最大值，然后逐渐减小回到0
    保存符合条件段的船舶MMSI、经度、纬度、冲突值

    参数:
    - conflicts_list: 冲突数据列表，每个元素是包含['MMSI', 'PosTime', 'Conflict']的DataFrame
    - trajectories_list: 原始轨迹数据列表，每个元素包含船舶轨迹信息
    - min_conflict_threshold: 最大冲突值的最小阈值

    返回:
    - valid_indices: 符合条件的轨迹索引列表
    - valid_segments_data: 符合条件的0-to-0子段详细数据
    - valid_full_segments_data: 符合条件的整段详细数据
    """
    valid_indices = []
    valid_segments_data = []
    valid_full_segments_data = []

    for traj_idx, conflict_df in enumerate(conflicts_list):
        if len(conflict_df) < 5:  # 至少需要5个点
            continue

        conflicts = conflict_df['Conflict'].values
        times = conflict_df['PosTime'].values
        mmsi_pair = conflict_df['MMSI'].values[0]

        # 检查最大冲突值是否满足阈值
        max_conflict = np.max(conflicts)
        if max_conflict < min_conflict_threshold:
            continue

        # 找到冲突段：从0开始，到0结束
        conflict_segment = _find_zero_to_zero_segment(conflicts, times)

        if conflict_segment is None:
            continue

        segment_conflicts, segment_times, start_idx, end_idx = conflict_segment

        # 检查段内最大冲突值是否满足阈值
        if np.max(segment_conflicts) < min_conflict_threshold:
            continue

        # 找到最大值位置
        max_idx = np.argmax(segment_conflicts)

        # 最大值不能在两端
        if max_idx == 0 or max_idx == len(segment_conflicts) - 1:
            continue

        # 检查上升段和下降段
        rising_phase = segment_conflicts[:max_idx + 1]
        falling_phase = segment_conflicts[max_idx:]

        # 检查上升趋势：至少30%的点呈上升
        rising_diffs = np.diff(rising_phase)
        rising_ratio = np.sum(rising_diffs > 0) / len(rising_diffs) if len(rising_diffs) > 0 else 0

        # 检查下降趋势：至少30%的点呈下降
        falling_diffs = np.diff(falling_phase)
        falling_ratio = np.sum(falling_diffs < 0) / len(falling_diffs) if len(falling_diffs) > 0 else 0

        # 符合条件：上升趋势明显 且 下降趋势明显
        if rising_ratio >= 0.3 and falling_ratio >= 0.3:
            valid_indices.append(traj_idx)

            # 提取详细的段数据
            if traj_idx < len(trajectories_list):
                # 提取0-to-0子段数据
                segment_data = _extract_segment_data(
                    trajectories_list[traj_idx],
                    segment_times,
                    segment_conflicts,
                    mmsi_pair,
                    traj_idx
                )
                valid_segments_data.append(segment_data)

                # 提取整段数据
                full_segment_data = _extract_full_segment_data(
                    trajectories_list[traj_idx],
                    times,
                    conflicts,
                    mmsi_pair,
                    traj_idx
                )
                valid_full_segments_data.append(full_segment_data)

    return valid_indices, valid_segments_data, valid_full_segments_data


def _find_zero_to_zero_segment(conflicts, times, zero_threshold=0.001):
    """
    找到从0开始到0结束的冲突段，只保留两端的0值点

    参数:
    - conflicts: 冲突值数组
    - times: 时间数组
    - zero_threshold: 认为是0的阈值

    返回:
    - (segment_conflicts, segment_times, start_idx, end_idx) 或 None
    """
    # 找到所有非零点
    nonzero_points = conflicts > zero_threshold

    if not np.any(nonzero_points):
        return None

    # 找到第一个和最后一个非零点
    nonzero_indices = np.where(nonzero_points)[0]

    if len(nonzero_indices) < 3:  # 至少需要3个非零点才能形成有效的冲突模式
        return None

    first_nonzero_idx = nonzero_indices[0]
    last_nonzero_idx = nonzero_indices[-1]

    # 找到第一个非零点之前的0点作为起始
    start_idx = 0
    for i in range(first_nonzero_idx - 1, -1, -1):
        if conflicts[i] <= zero_threshold:
            start_idx = i
            break

    # 找到最后一个非零点之后的0点作为结束
    end_idx = len(conflicts) - 1
    for i in range(last_nonzero_idx + 1, len(conflicts)):
        if conflicts[i] <= zero_threshold:
            end_idx = i
            break

    # 确保段长度足够
    if end_idx - start_idx < 4:
        return None

    # 确保起始和结束都是0值
    if conflicts[start_idx] > zero_threshold or conflicts[end_idx] > zero_threshold:
        return None

    # 提取段数据
    segment_conflicts = conflicts[start_idx:end_idx + 1]
    segment_times = times[start_idx:end_idx + 1]

    return segment_conflicts, segment_times, start_idx, end_idx


def _extract_full_segment_data(trajectory_info, full_times, full_conflicts, mmsi_pair, traj_idx):
    """
    从轨迹信息中提取符合冲突模式要求的整段数据

    参数:
    - trajectory_info: 单个轨迹的完整信息
    - full_times: 整段的时间点
    - full_conflicts: 整段的冲突值
    - mmsi_pair: 船舶MMSI对
    - traj_idx: 轨迹索引

    返回:
    - 包含MMSI、经纬度、冲突值的整段数据
    """
    # 提取两船的轨迹数据
    maneuvering_trajectory = trajectory_info['maneuvering_ship']['trajectory']
    other_trajectory = trajectory_info['other_ship']['trajectory']

    # 筛选整段时间的数据
    maneuvering_full = maneuvering_trajectory[
        maneuvering_trajectory['PosTime'].isin(full_times)
    ].copy().sort_values('PosTime')

    other_full = other_trajectory[
        other_trajectory['PosTime'].isin(full_times)
    ].copy().sort_values('PosTime')

    # 确保时间点对齐
    common_times = np.intersect1d(maneuvering_full['PosTime'].values,
                                  other_full['PosTime'].values)
    common_times = np.intersect1d(common_times, full_times)

    # 重新筛选确保数据一致性
    maneuvering_full = maneuvering_full[
        maneuvering_full['PosTime'].isin(common_times)
    ].sort_values('PosTime').reset_index(drop=True)

    other_full = other_full[
        other_full['PosTime'].isin(common_times)
    ].sort_values('PosTime').reset_index(drop=True)

    # 确保两个轨迹段的时间点完全一致
    if len(maneuvering_full) != len(other_full):
        return None

    # 验证时间点是否完全一致
    man_times = maneuvering_full['PosTime'].values
    other_times = other_full['PosTime'].values
    if not np.array_equal(man_times, other_times):
        return None

    # 筛选对应的冲突值
    time_to_conflict = dict(zip(full_times, full_conflicts))
    full_segment_conflicts = [time_to_conflict[t] for t in common_times if t in time_to_conflict]

    # 获取船舶长度
    man_length = maneuvering_full['Length'].iloc[0] if len(maneuvering_full) > 0 else 100
    other_length = other_full['Length'].iloc[0] if len(other_full) > 0 else 100

    # 构建返回数据 - 整段数据
    full_segment_data = {
        'trajectory_index': traj_idx,
        'scene_id': trajectory_info.get('scene_id', f'scene_{traj_idx}'),
        'scene_type': trajectory_info.get('scene_type', 'unknown'),
        'mmsi_pair': mmsi_pair,
        'full_segment': {
            'start_time': man_times.min(),
            'end_time': man_times.max(),
            'duration_seconds': man_times.max() - man_times.min(),
            'total_points': len(man_times),
            'max_conflict': max(full_segment_conflicts) if full_segment_conflicts else 0,
            'min_conflict': min(full_segment_conflicts) if full_segment_conflicts else 0,
            'mean_conflict': np.mean(full_segment_conflicts) if full_segment_conflicts else 0,
            'conflict_pattern_verified': True
        },
        'maneuvering_ship': {
            'mmsi': trajectory_info['maneuvering_ship']['mmsi'],
            'length': man_length,
            'full_segment_data': {
                'times': man_times.tolist(),
                'longitudes': maneuvering_full['Lon'].tolist(),
                'latitudes': maneuvering_full['Lat'].tolist(),
                'courses': maneuvering_full['Cog'].tolist(),
                'speeds': maneuvering_full['Sog'].tolist(),
                'conflicts': full_segment_conflicts
            }
        },
        'other_ship': {
            'mmsi': trajectory_info['other_ship']['mmsi'],
            'length': other_length,
            'full_segment_data': {
                'times': other_times.tolist(),
                'longitudes': other_full['Lon'].tolist(),
                'latitudes': other_full['Lat'].tolist(),
                'courses': other_full['Cog'].tolist(),
                'speeds': other_full['Sog'].tolist(),
                'conflicts': full_segment_conflicts
            }
        }
    }

    return full_segment_data


def _extract_segment_data(trajectory_info, valid_times, valid_conflicts, mmsi_pair, traj_idx):
    """
    从轨迹信息中提取符合冲突模式要求的段数据
    只保存符合要求的冲突段，而不是整个轨迹过程

    参数:
    - trajectory_info: 单个轨迹的完整信息
    - valid_times: 符合条件的时间点（已经过滤了冲突阈值）
    - valid_conflicts: 对应的冲突值（已经过滤了冲突阈值）
    - mmsi_pair: 船舶MMSI对
    - traj_idx: 轨迹索引

    返回:
    - 包含MMSI、经纬度、冲突值的符合要求的冲突段数据
    """
    # 提取两船的轨迹数据
    maneuvering_trajectory = trajectory_info['maneuvering_ship']['trajectory']
    other_trajectory = trajectory_info['other_ship']['trajectory']

    # 只筛选符合冲突模式的时间段数据（valid_times已经是筛选后的）
    maneuvering_segment = maneuvering_trajectory[
        maneuvering_trajectory['PosTime'].isin(valid_times)
    ].copy().sort_values('PosTime')

    other_segment = other_trajectory[
        other_trajectory['PosTime'].isin(valid_times)
    ].copy().sort_values('PosTime')

    # 确保时间点对齐
    common_times = np.intersect1d(maneuvering_segment['PosTime'].values,
                                  other_segment['PosTime'].values)
    common_times = np.intersect1d(common_times, valid_times)

    # 重新筛选确保数据一致性，并确保时间点完全对齐
    maneuvering_segment = maneuvering_segment[
        maneuvering_segment['PosTime'].isin(common_times)
    ].sort_values('PosTime').reset_index(drop=True)

    other_segment = other_segment[
        other_segment['PosTime'].isin(common_times)
    ].sort_values('PosTime').reset_index(drop=True)

    # 确保两个轨迹段的时间点完全一致
    if len(maneuvering_segment) != len(other_segment):
        return None

    # 验证时间点是否完全一致
    man_times = maneuvering_segment['PosTime'].values
    other_times = other_segment['PosTime'].values
    if not np.array_equal(man_times, other_times):
        return None

    # 筛选对应的冲突值
    time_to_conflict = dict(zip(valid_times, valid_conflicts))
    segment_conflicts = [time_to_conflict[t] for t in common_times if t in time_to_conflict]

    # 获取船舶长度（应该在整个轨迹中保持一致）
    man_length = maneuvering_segment['Length'].iloc[0] if len(maneuvering_segment) > 0 else 100
    other_length = other_segment['Length'].iloc[0] if len(other_segment) > 0 else 100

    # 构建返回数据 - 只包含符合冲突模式的段
    segment_data = {
        'trajectory_index': traj_idx,
        'scene_id': trajectory_info.get('scene_id', f'scene_{traj_idx}'),
        'scene_type': trajectory_info.get('scene_type', 'unknown'),
        'mmsi_pair': mmsi_pair,
        'conflict_segment': {
            'start_time': man_times.min(),
            'end_time': man_times.max(),
            'duration_seconds': man_times.max() - man_times.min(),
            'total_points': len(man_times),
            'max_conflict': max(segment_conflicts) if segment_conflicts else 0,
            'min_conflict': min(segment_conflicts) if segment_conflicts else 0,
            'mean_conflict': np.mean(segment_conflicts) if segment_conflicts else 0,
            'conflict_pattern_verified': True  # 标记这是符合模式的段
        },
        'maneuvering_ship': {
            'mmsi': trajectory_info['maneuvering_ship']['mmsi'],
            'length': man_length,  # 船舶长度
            'conflict_segment_data': {
                'times': man_times.tolist(),  # 使用对齐后的时间点
                'longitudes': maneuvering_segment['Lon'].tolist(),
                'latitudes': maneuvering_segment['Lat'].tolist(),
                'courses': maneuvering_segment['Cog'].tolist(),
                'speeds': maneuvering_segment['Sog'].tolist(),
                'conflicts': segment_conflicts  # 每个时间点对应的冲突值
            }
        },
        'other_ship': {
            'mmsi': trajectory_info['other_ship']['mmsi'],
            'length': other_length,  # 船舶长度
            'conflict_segment_data': {
                'times': other_times.tolist(),  # 使用对齐后的时间点
                'longitudes': other_segment['Lon'].tolist(),
                'latitudes': other_segment['Lat'].tolist(),
                'courses': other_segment['Cog'].tolist(),
                'speeds': other_segment['Sog'].tolist(),
                'conflicts': segment_conflicts  # 每个时间点对应的冲突值
            }
        }
    }

    return segment_data


#%%
# 加载冲突数据和原始轨迹数据
with open('result/trajectories/2024_1/crossing_conflicts.pkl', 'rb') as f:
    Conflicts_crossing = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_conflicts.pkl', 'rb') as f:
    Conflicts_overtaking = pickle.load(f)

with open('result/trajectories/2024_1/crossing_encounter_trajectories.pkl', 'rb') as f:
    crossing_trajectories = pickle.load(f)
with open('result/trajectories/2024_1/overtaking_encounter_trajectories.pkl', 'rb') as f:
    overtaking_trajectories = pickle.load(f)

# 筛选符合条件的轨迹（使用改进的函数）
print("正在筛选交叉场景的符合条件轨迹...")
crossing_valid_indices, crossing_segments_data, crossing_full_segments_data = filter_pattern_trajectories(
    Conflicts_crossing, crossing_trajectories
)

print("正在筛选追越场景的符合条件轨迹...")
overtaking_valid_indices, overtaking_segments_data, overtaking_full_segments_data = filter_pattern_trajectories(
    Conflicts_overtaking, overtaking_trajectories
)

# 保存0-to-0子段数据
with open('result/trajectories/2024_1/crossing_valid_segments.pkl', 'wb') as f:
    pickle.dump(crossing_segments_data, f)
with open('result/trajectories/2024_1/overtaking_valid_segments.pkl', 'wb') as f:
    pickle.dump(overtaking_segments_data, f)

# 保存整段数据
with open('result/trajectories/2024_1/crossing_valid_full_segments.pkl', 'wb') as f:
    pickle.dump(crossing_full_segments_data, f)
with open('result/trajectories/2024_1/overtaking_valid_full_segments.pkl', 'wb') as f:
    pickle.dump(overtaking_full_segments_data, f)

print(f"\n✅ 筛选结果:")
print(f"交叉场景: {len(crossing_valid_indices)}/{len(Conflicts_crossing)} 个轨迹符合条件")
print(f"追越场景: {len(overtaking_valid_indices)}/{len(Conflicts_overtaking)} 个轨迹符合条件")
print(f"\n📁 保存的文件:")
print(f"  - crossing_valid_segments.pkl: 交叉场景0-to-0子段数据")
print(f"  - overtaking_valid_segments.pkl: 追越场景0-to-0子段数据")
print(f"  - crossing_valid_full_segments.pkl: 交叉场景整段数据")
print(f"  - overtaking_valid_full_segments.pkl: 追越场景整段数据")

