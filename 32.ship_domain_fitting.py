"""
船舶领域拟合系统 - 基于扇区边界的椭圆拟合
基于概率密度分析的扇区边界结果，拟合船舶领域椭圆

核心功能：
1. 加载扇区边界结果
2. 基于扇区边界点拟合椭圆
3. 评估拟合质量
4. 可视化拟合结果
5. 保存椭圆参数

特殊处理：
- 追越避让场景：仅使用Y轴上半部分的边界点进行拟合（Y >= 0）

输入：sector_boundaries_results.pkl
输出：ship_domain_ellipse_params.pkl

使用方法：
python ship_domain_fitting.py
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from scipy.optimize import leastsq, minimize
from scipy.stats import median_abs_deviation

# 高级拟合方法所需的库
try:
    from sklearn.linear_model import LinearRegression, RANSACRegressor
    from sklearn.neighbors import NearestNeighbors
    SKLEARN_AVAILABLE = True
except ImportError:
    print("⚠️  sklearn未安装，将使用基础拟合方法")
    SKLEARN_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ShipDomainFitter:
    """船舶领域拟合器"""

    def __init__(self, debug=False):
        """
        初始化船舶领域拟合器
        
        :param debug: 是否开启调试模式
        """
        self.debug = debug

        # 拟合结果存储
        self.ellipse_params = {}
        self.fitting_quality = {}

        # 输入数据
        self.sector_boundaries = {}
        self.length_intervals = []

        print(f"🎯 船舶领域拟合器初始化完成")

    def load_sector_boundaries(self):
        """加载扇区边界结果"""
        print("\n=== 加载扇区边界结果 ===")

        result_file = Path("result/probability_density/sector_boundaries_results.pkl")

        if not result_file.exists():
            print(f"❌ 未找到扇区边界结果文件: {result_file}")
            print("请先运行 probability_density_analysis.py 生成扇区边界结果")
            return False

        with open(result_file, 'rb') as f:
            results = pickle.load(f)

        self.sector_boundaries = results['sector_boundaries']
        self.length_intervals = results['length_intervals']

        print(f"✅ 加载扇区边界结果: {result_file}")
        print(f"   场景数量: {len(self.sector_boundaries)}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")

        return True

    def fit_ship_domains(self):
        """拟合船舶领域 - 追越仅使用Y轴上半部分边界点"""
        print("\n=== 拟合船舶领域 ===")

        success_count = 0
        total_count = len(self.sector_boundaries)

        for key, boundaries in self.sector_boundaries.items():
            scenario_type, length_interval = key.split('_', 1)

            print(f"\n🎯 拟合 {scenario_type}-{length_interval} 的船舶领域...")

            # 转换边界点为坐标
            boundary_points = self._convert_boundaries_to_points(boundaries)

            # 追越避让的特殊处理：过滤Y轴上半部分边界点
            if scenario_type == "追越":
                boundary_points = self._filter_upper_half_boundary_points(boundary_points)
                print(f"   追越避让过滤后上半部分边界点: {len(boundary_points)}个")

            # 根据场景类型调整最小边界点要求
            min_points = 2 if scenario_type == "追越" else 3

            if len(boundary_points) < min_points:
                print(f"⚠️  {scenario_type}-{length_interval}: 边界点不足({len(boundary_points)}个)，跳过拟合")
                continue

            # 根据场景类型选择拟合方法
            if scenario_type == "追越":
                ellipse_result = self._fit_ellipse_for_overtaking(boundary_points)
            else:
                ellipse_result = self._fit_ellipse_from_boundaries(boundary_points)

            if ellipse_result is None:
                print(f"❌ {scenario_type}-{length_interval}: 椭圆拟合失败")
                continue

            # 评估拟合质量
            quality_metrics = self._evaluate_fitting_quality(boundary_points, ellipse_result)

            # 存储结果
            self.ellipse_params[key] = ellipse_result
            self.fitting_quality[key] = quality_metrics

            print(f"✅ {scenario_type}-{length_interval}: 椭圆拟合完成")
            print(f"   长半轴: {ellipse_result['a']:.1f}m, 短半轴: {ellipse_result['b']:.1f}m")
            print(f"   拟合质量: R²={quality_metrics['r_squared']:.3f}")
            print(f"   边界点数: {len(boundary_points)}, 平均误差: {quality_metrics['mean_error']:.3f}")

            success_count += 1

        print(f"\n✅ 船舶领域拟合完成: {success_count}/{total_count} 个场景拟合成功")

    def _convert_boundaries_to_points(self, boundaries):
        """将扇区边界转换为坐标点"""
        boundary_points = []

        for sector_name, boundary_info in boundaries.items():
            distance = boundary_info['boundary_distance']
            angle = boundary_info['boundary_angle']

            # 转换为坐标
            x = distance * np.cos(angle)
            y = distance * np.sin(angle)

            boundary_points.append((x, y, sector_name))

        return boundary_points

    def _filter_upper_half_boundary_points(self, boundary_points):
        """过滤Y轴上半部分边界点（仅用于追越避让）"""
        upper_half_points = []

        for x, y, sector_name in boundary_points:
            if y >= 0:  # 只保留Y轴上半部分的边界点（Y >= 0）
                upper_half_points.append((x, y, sector_name))
            else:
                print(f"   过滤掉Y轴下半部分边界点: {sector_name} ({x:.1f}, {y:.1f})")

        return upper_half_points

    def _fit_ellipse_for_overtaking(self, boundary_points):
        """追越避让的特殊椭圆拟合方法"""
        try:
            # 提取坐标
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            if len(x_coords) < 2:
                return None

            # 追越避让的特殊处理：基于前方边界点的分布特点
            if len(x_coords) >= 3:
                # 如果有足够的前方边界点，使用代数方法拟合
                result = self._fit_ellipse_algebraic(x_coords, y_coords)
                if result is not None:
                    result['fitting_method'] = 'overtaking_algebraic'
                    return result

            return None

        except Exception as e:
            if self.debug:
                print(f"追越椭圆拟合失败: {e}")
            return None

    def _fit_ellipse_forward_estimation(self, x_coords, y_coords):
        """基于前方分布的椭圆估计（追越专用）"""
        try:
            # 基于前方边界点估计椭圆参数
            x_range = np.max(x_coords) - np.min(x_coords)
            y_max = np.max(y_coords)
            y_min = max(0, np.min(y_coords))  # 确保不小于0
            y_range = y_max - y_min

            # 追越避让的椭圆特点：纵向较长，主要在前方
            # 长半轴（纵向）：基于前方最远点
            a = max(y_max * 1.2, 100.0)  # 长半轴，最小100米

            # 短半轴（横向）：基于横向分布
            b = max(x_range * 0.8, 50.0)  # 短半轴，最小50米

            # 确保椭圆形状合理（追越通常是纵向长椭圆）
            if a < b:  # 如果短半轴比长半轴大，调整
                a = max(b * 1.5, 150.0)

            return {
                'a': float(a),
                'b': float(b),
                'data_count': len(x_coords),
                'cleaned_count': len(x_coords),
                'forward_only': True
            }

        except Exception as e:
            if self.debug:
                print(f"前方估计失败: {e}")
            return None

    def _fit_ellipse_from_boundaries(self, boundary_points):
        """基于边界点拟合椭圆"""
        try:
            # 提取坐标
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            if len(x_coords) < 3:
                return None

            ellipse_result = self._fit_ellipse_algebraic(x_coords, y_coords)

            return ellipse_result

        except Exception as e:
            if self.debug:
                print(f"椭圆拟合失败: {e}")
            return None

    def _fit_ellipse_algebraic(self, x_coords, y_coords):
        """改进的最小二乘法椭圆拟合"""

        # 数据预处理：只移除重复点
        cleaned_x, cleaned_y = self._preprocess_boundary_data(x_coords, y_coords)

        if len(cleaned_x) < 3:
            return None

        # 使用最小二乘法拟合椭圆
        result = self._least_squares_ellipse_fit(cleaned_x, cleaned_y)

        if result is not None:
            result['fitting_method'] = 'least_squares'

        return result

    def _least_squares_ellipse_fit(self, x_coords, y_coords):
        """最小二乘法椭圆拟合 - 最小化几何距离"""
        try:
            # 数据中心化
            x_mean, y_mean = np.mean(x_coords), np.mean(y_coords)
            x_centered = x_coords - x_mean
            y_centered = y_coords - y_mean

            # 方法1：代数距离最小二乘法
            result1 = self._algebraic_least_squares(x_centered, y_centered, x_mean, y_mean)

            # 方法2：几何距离最小二乘法（非线性优化）
            result2 = self._geometric_least_squares(x_centered, y_centered, x_mean, y_mean, result1)
            #
            # # 选择更好的结果
            # if result2 is not None:
            #     return result2
            # else:
            #     return result1
            return result2

        except Exception as e:
            if self.debug:
                print(f"最小二乘法拟合失败: {e}")
            return None

    def _algebraic_least_squares(self, x_centered, y_centered, x_mean, y_mean):
        """代数距离最小二乘法"""
        try:
            # 构建设计矩阵 [x², xy, y², x, y, 1]
            # 椭圆一般方程: Ax² + Bxy + Cy² + Dx + Ey + F = 0
            X = np.column_stack([
                x_centered**2,           # A
                x_centered * y_centered, # B
                y_centered**2,           # C
                x_centered,              # D
                y_centered,              # E
                np.ones(len(x_centered)) # F
            ])

            # 约束条件：B² - 4AC < 0 (确保是椭圆)
            # 使用广义特征值问题求解
            # 构建约束矩阵
            C_matrix = np.array([
                [0, 0, 2, 0, 0, 0],
                [0, -1, 0, 0, 0, 0],
                [2, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0, 0]
            ])

            # 计算散布矩阵
            S = X.T @ X

            # 求解广义特征值问题
            eigenvals, eigenvecs = np.linalg.eig(np.linalg.inv(S) @ C_matrix)

            # 选择正特征值对应的特征向量
            pos_idx = np.where(eigenvals > 1e-8)[0]
            if len(pos_idx) == 0:
                # 如果没有正特征值，使用普通最小二乘法
                return self._ordinary_least_squares(x_centered, y_centered, x_mean, y_mean)

            # 选择最小正特征值对应的特征向量
            min_pos_idx = pos_idx[np.argmin(eigenvals[pos_idx])]
            coeffs = eigenvecs[:, min_pos_idx].real

            # 提取椭圆参数
            a, b, theta, cx, cy = self._extract_ellipse_params(coeffs, x_mean, y_mean)

            if self._validate_ellipse_parameters(a, b, x_centered, y_centered):
                return {
                    'a': float(a),
                    'b': float(b),
                    'theta': float(theta),
                    'center_x': float(cx),
                    'center_y': float(cy)
                }
            else:
                return self._ordinary_least_squares(x_centered, y_centered, x_mean, y_mean)

        except Exception as e:
            if self.debug:
                print(f"代数最小二乘法失败: {e}")
            return self._ordinary_least_squares(x_centered, y_centered, x_mean, y_mean)

    def _ordinary_least_squares(self, x_centered, y_centered, x_mean, y_mean):
        """普通最小二乘法（无约束）"""
        try:
            # 构建设计矩阵
            X = np.column_stack([
                x_centered**2,
                x_centered * y_centered,
                y_centered**2,
                x_centered,
                y_centered
            ])
            y = np.ones(len(x_centered))

            # 最小二乘求解
            coeffs, residuals, rank, s = np.linalg.lstsq(X, y, rcond=None)

            # 添加F=-1
            full_coeffs = np.append(coeffs, -1)

            # 提取椭圆参数
            a, b, theta, cx, cy = self._extract_ellipse_params(full_coeffs, x_mean, y_mean)

            if self._validate_ellipse_parameters(a, b, x_centered, y_centered):
                return {
                    'a': float(a),
                    'b': float(b),
                    'theta': float(theta),
                    'center_x': float(cx),
                    'center_y': float(cy)
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"普通最小二乘法失败: {e}")
            return None

    def _geometric_least_squares(self, x_centered, y_centered, x_mean, y_mean, initial_guess):
        """几何距离最小二乘法（非线性优化）"""
        try:
            if initial_guess is None:
                return None

            # 初始参数
            a0 = initial_guess['a']
            b0 = initial_guess['b']
            theta0 = initial_guess.get('theta', 0)
            cx0 = initial_guess.get('center_x', x_mean) - x_mean
            cy0 = initial_guess.get('center_y', y_mean) - y_mean

            # 参数向量 [a, b, theta, cx, cy]
            params0 = np.array([a0, b0, theta0, cx0, cy0])

            # 目标函数：最小化几何距离平方和
            def objective(params):
                a, b, theta, cx, cy = params
                if a <= 0 or b <= 0:
                    return 1e10

                # 计算每个点到椭圆的几何距离
                distances = []
                for xi, yi in zip(x_centered, y_centered):
                    dist = self._point_to_ellipse_distance(xi, yi, a, b, theta, cx, cy)
                    distances.append(dist**2)

                return np.sum(distances)

            # 约束条件
            bounds = [
                (5, 2000),      # a > 0
                (5, 2000),      # b > 0
                (-np.pi, np.pi), # theta
                (-500, 500),     # cx
                (-500, 500)      # cy
            ]

            # 非线性优化
            from scipy.optimize import minimize
            result = minimize(objective, params0, method='L-BFGS-B', bounds=bounds)

            if result.success:
                a, b, theta, cx, cy = result.x

                # 确保a >= b
                if a < b:
                    a, b = b, a
                    theta += np.pi/2

                if self._validate_ellipse_parameters(a, b, x_centered, y_centered):
                    return {
                        'a': float(abs(a)),
                        'b': float(abs(b)),
                        'theta': float(theta),
                        'center_x': float(cx + x_mean),
                        'center_y': float(cy + y_mean),
                        'optimization_success': True,
                        'final_cost': float(result.fun)
                    }

            return None

        except Exception as e:
            if self.debug:
                print(f"几何最小二乘法失败: {e}")
            return None

    def _point_to_ellipse_distance(self, x, y, a, b, theta, cx, cy):
        """计算点到椭圆的几何距离"""
        try:
            # 将点转换到椭圆坐标系
            cos_t, sin_t = np.cos(theta), np.sin(theta)
            dx, dy = x - cx, y - cy

            # 旋转到椭圆主轴坐标系
            u = cos_t * dx + sin_t * dy
            v = -sin_t * dx + cos_t * dy

            # 椭圆方程: (u/b)² + (v/a)² = 1
            # 使用迭代方法计算最近点
            if abs(u) < 1e-10 and abs(v) < 1e-10:
                return 0

            # 简化计算：使用椭圆上的点到给定点的距离
            # 参数化椭圆: u = b*cos(t), v = a*sin(t)
            t_values = np.linspace(0, 2*np.pi, 100)
            min_dist = float('inf')

            for t in t_values:
                u_ellipse = b * np.cos(t)
                v_ellipse = a * np.sin(t)
                dist = np.sqrt((u - u_ellipse)**2 + (v - v_ellipse)**2)
                min_dist = min(min_dist, dist)

            return min_dist

        except:
            return np.sqrt(x**2 + y**2)  # 退化到原点距离

    def _extract_ellipse_params(self, coeffs, x_mean, y_mean):
        """从椭圆系数中提取参数"""
        try:
            # 椭圆一般方程: Ax² + Bxy + Cy² + Dx + Ey + F = 0
            A, B, C, D, E, F = coeffs

            # 计算椭圆中心
            denom = B*B - 4*A*C
            if abs(denom) < 1e-10:
                # 退化情况，使用数据中心
                return 50, 50, 0, x_mean, y_mean

            cx = (2*C*D - B*E) / denom
            cy = (2*A*E - B*D) / denom

            # 计算旋转角度
            if abs(B) < 1e-10:
                theta = 0 if A < C else np.pi/2
            else:
                theta = 0.5 * np.arctan2(B, A - C)

            # 计算半轴长度
            cos_t, sin_t = np.cos(theta), np.sin(theta)
            a_coeff = A*cos_t*cos_t + B*cos_t*sin_t + C*sin_t*sin_t
            b_coeff = A*sin_t*sin_t - B*cos_t*sin_t + C*cos_t*cos_t

            # 计算常数项
            const = A*cx*cx + B*cx*cy + C*cy*cy + D*cx + E*cy + F

            if a_coeff > 0 and b_coeff > 0 and const < 0:
                a = np.sqrt(-const / a_coeff)
                b = np.sqrt(-const / b_coeff)

                # 确保a >= b (长半轴 >= 短半轴)
                if a < b:
                    a, b = b, a
                    theta += np.pi/2

                return abs(a), abs(b), theta, cx + x_mean, cy + y_mean
            else:
                # 使用简单估计
                return 50, 50, 0, x_mean, y_mean

        except Exception as e:
            if self.debug:
                print(f"参数提取失败: {e}")
            return 50, 50, 0, x_mean, y_mean

    def _validate_ellipse_parameters(self, a, b, x_coords, y_coords):
        """验证椭圆参数的合理性 - 更宽松的验证"""
        try:
            # 基本尺寸检查 - 更宽松
            if a <= 0 or b <= 0:
                return False

            if a < 5 or b < 5:  # 最小尺寸降低
                return False

            if a > 5000 or b > 5000:  # 最大尺寸放宽
                return False

            # 长短轴比例检查 - 更宽松
            ratio = max(a, b) / min(a, b)
            if ratio > 50:  # 比例限制放宽
                return False

            # 与数据分布的一致性检查 - 更宽松
            if len(x_coords) > 0 and len(y_coords) > 0:
                distances = np.sqrt(x_coords ** 2 + y_coords ** 2)
                max_data_distance = np.max(distances)
                ellipse_max_radius = max(a, b)

                # 椭圆尺寸检查更宽松
                if ellipse_max_radius < max_data_distance * 0.1:  # 从0.3降到0.1
                    return False
                if ellipse_max_radius > max_data_distance * 10.0:  # 从3.0增到10.0
                    return False

            return True

        except Exception:
            return False

    def _preprocess_boundary_data(self, x_coords, y_coords):
        """预处理边界数据"""
        try:
            # 只移除重复点，不进行异常值过滤
            points = np.column_stack([x_coords, y_coords])
            unique_points = np.unique(points, axis=0)

            if len(unique_points) < len(points):
                x_coords = unique_points[:, 0]
                y_coords = unique_points[:, 1]
                if self.debug:
                    print(f"   移除重复点: {len(points)} -> {len(unique_points)}")

            # 取消异常值过滤，保留所有数据点
            if self.debug:
                print(f"   保留所有边界点: {len(x_coords)}个")

            return x_coords, y_coords

        except Exception as e:
            if self.debug:
                print(f"数据预处理失败: {e}")
            return x_coords, y_coords





    def _evaluate_fitting_quality(self, boundary_points, ellipse_result):
        """基于几何距离的拟合质量评估"""
        try:
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            a, b = ellipse_result['a'], ellipse_result['b']

            # 计算椭圆方程值 (x/b)² + (y/a)²
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2

            # 几何误差：点到椭圆的距离
            geometric_errors = np.abs(ellipse_values - 1)

            # R²计算：基于椭圆方程的拟合度
            mean_ellipse_value = np.mean(ellipse_values)
            ss_res = np.sum((ellipse_values - 1) ** 2)
            ss_tot = np.sum((ellipse_values - mean_ellipse_value) ** 2)

            if ss_tot > 1e-10:
                r_squared = 1 - (ss_res / ss_tot)
            else:
                r_squared = 1.0

            r_squared = max(0, min(1, r_squared))

            return {
                'r_squared': float(r_squared),
                'mean_error': float(np.mean(geometric_errors)),
                'max_error': float(np.max(geometric_errors)),
                'std_error': float(np.std(geometric_errors)),
                'point_count': len(boundary_points),
                'ellipse_ratio': float(max(a, b) / min(a, b)),
                'fitting_method': ellipse_result.get('fitting_method', 'least_squares')
            }

        except Exception as e:
            if self.debug:
                print(f"质量评估失败: {e}")
            return {
                'r_squared': 0.0,
                'mean_error': float('inf'),
                'max_error': float('inf'),
                'std_error': float('inf'),
                'point_count': len(boundary_points),
                'ellipse_ratio': 0.0,
                'fitting_method': 'failed'
            }



    def visualize_fitting_results(self):
        """可视化拟合结果"""
        print("\n=== 生成可视化结果 ===")

        vis_dir = Path("vis/test/ship_domain_fitting")
        vis_dir.mkdir(parents=True, exist_ok=True)

        for key, ellipse_params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)

            # 创建拟合结果图
            self._create_fitting_plot(key, scenario_type, length_interval, vis_dir)

        # 创建对比分析图
        self._create_comparison_plot(vis_dir)

        print(f"✅ 可视化结果保存至: {vis_dir}")

    def _create_fitting_plot(self, key, scenario_type, length_interval, vis_dir):
        """创建单个场景的拟合结果图"""
        ellipse_params = self.ellipse_params[key]
        quality_metrics = self.fitting_quality[key]
        boundaries = self.sector_boundaries[key]

        # 创建图形
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(f'{scenario_type}避让 - {length_interval} 船舶领域拟合结果', fontsize=16)

        # 左图：拟合结果
        ax1 = axes[0]

        # 绘制边界点
        boundary_points = self._convert_boundaries_to_points(boundaries)
        x_coords = [p[0] for p in boundary_points]
        y_coords = [p[1] for p in boundary_points]

        ax1.scatter(x_coords, y_coords, c='red', s=50, alpha=0.8, label='扇区边界点')

        # 绘制拟合椭圆
        self._plot_ellipse(ax1, ellipse_params['a'], ellipse_params['b'])

        ax1.set_title('椭圆拟合结果')
        ax1.set_xlabel('横向距离 (m)')
        ax1.set_ylabel('纵向距离 (m)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        ax1.legend()

        # 右图：质量指标
        ax2 = axes[1]
        metrics = ['R²', '平均误差', '最大误差', '标准差']
        values = [
            quality_metrics['r_squared'],
            quality_metrics['mean_error'],
            quality_metrics['max_error'],
            quality_metrics['std_error']
        ]

        bars = ax2.bar(metrics, values, color=['green', 'orange', 'red', 'blue'])
        ax2.set_title('拟合质量指标')
        ax2.set_ylabel('数值')

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width() / 2., height,
                     f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        # 保存图片
        filename = f"{scenario_type}_{length_interval}_fitting_result.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {key} 拟合图保存至: {save_path}")

    def _plot_ellipse(self, ax, a, b, center=(0, 0), color='blue', label='拟合椭圆'):
        """绘制椭圆"""
        theta = np.linspace(0, 2 * np.pi, 100)
        x = center[0] + b * np.cos(theta)  # 横向半轴
        y = center[1] + a * np.sin(theta)  # 纵向半轴

        ax.plot(x, y, color=color, linewidth=2, label=label)

    def _create_comparison_plot(self, vis_dir):
        """创建对比分析图"""
        if len(self.ellipse_params) < 2:
            return

        # 收集数据
        comparison_data = []

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            quality = self.fitting_quality[key]

            comparison_data.append({
                'scenario_type': scenario_type,
                'length_interval': length_interval,
                'a': params['a'],
                'b': params['b'],
                'ratio': params['a'] / params['b'],
                'r_squared': quality['r_squared'],
                'point_count': quality['point_count']
            })

        df = pd.DataFrame(comparison_data)

        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('船舶领域椭圆参数对比分析', fontsize=16, fontweight='bold')

        # 1. 椭圆参数散点图
        ax1 = axes[0, 0]
        for scenario in df['scenario_type'].unique():
            scenario_data = df[df['scenario_type'] == scenario]
            ax1.scatter(scenario_data['b'], scenario_data['a'],
                        label=f'{scenario}避让', s=100, alpha=0.7)

        ax1.set_xlabel('短半轴 b (m)')
        ax1.set_ylabel('长半轴 a (m)')
        ax1.set_title('椭圆参数分布')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 长短轴比对比
        ax2 = axes[0, 1]
        if len(df['scenario_type'].unique()) > 1:
            df.boxplot(column='ratio', by='scenario_type', ax=ax2)
            ax2.set_title('长短轴比分布')
            ax2.set_xlabel('场景类型')
            ax2.set_ylabel('长短轴比')

        # 3. 不同船长区间的长半轴对比
        ax3 = axes[1, 0]
        if len(df) > 0:
            pivot_a = df.pivot_table(values='a', index='length_interval',
                                     columns='scenario_type', aggfunc='mean')
            pivot_a.plot(kind='bar', ax=ax3, rot=45)
            ax3.set_title('不同船长区间的长半轴对比')
            ax3.set_xlabel('船长区间')
            ax3.set_ylabel('长半轴 a (m)')
            ax3.legend(title='场景类型')

        # 4. 拟合质量对比
        ax4 = axes[1, 1]
        if len(df) > 0:
            pivot_r2 = df.pivot_table(values='r_squared', index='length_interval',
                                      columns='scenario_type', aggfunc='mean')
            pivot_r2.plot(kind='bar', ax=ax4, rot=45)
            ax4.set_title('拟合质量(R²)对比')
            ax4.set_xlabel('船长区间')
            ax4.set_ylabel('R²')
            ax4.legend(title='场景类型')

        plt.tight_layout()

        # 保存对比图
        save_path = vis_dir / "ellipse_parameters_comparison.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 对比分析图保存至: {save_path}")

    def save_results(self):
        """保存拟合结果"""
        print("\n=== 保存拟合结果 ===")

        result_dir = Path("result/ship_domain_fitting")
        result_dir.mkdir(parents=True, exist_ok=True)

        # 按场景类型分组
        crossing_params = {}
        overtaking_params = {}

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            if scenario_type == "交叉":
                crossing_params[length_interval] = params
            elif scenario_type == "追越":
                overtaking_params[length_interval] = params

        # 保存主结果文件
        results = {
            'crossing_ellipse_params': crossing_params,
            'overtaking_ellipse_params': overtaking_params,
            'length_intervals': self.length_intervals,
            'fitting_quality': self.fitting_quality,
            'all_ellipse_params': self.ellipse_params
        }

        result_file = result_dir / "ship_domain_ellipse_params.pkl"
        with open(result_file, 'wb') as f:
            pickle.dump(results, f)

        # 保存CSV结果
        self._save_csv_results(result_dir)

        print(f"✅ 拟合结果保存至: {result_file}")

        return result_file

    def _save_csv_results(self, result_dir):
        """保存CSV结果"""
        data = []

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            quality = self.fitting_quality[key]

            data.append({
                'scenario_type': f'{scenario_type}避让',
                'length_interval': length_interval,
                'a_longitudinal_m': round(params['a'], 1),
                'b_lateral_m': round(params['b'], 1),
                'ratio_a_b': round(params['a'] / params['b'], 2),
                'r_squared': round(quality['r_squared'], 3),
                'mean_error': round(quality['mean_error'], 3),
                'boundary_points': quality['point_count'],
                'fitting_method': params.get('fitting_method', 'unknown')
            })

        if data:
            df = pd.DataFrame(data)
            csv_file = result_dir / "ship_domain_ellipse_parameters.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"   CSV文件: {csv_file}")

    def run_full_fitting(self):
        """运行完整的船舶领域拟合"""
        print("🎯 开始船舶领域拟合...")
        print("=" * 60)

        try:
            # 执行拟合流程
            if not self.load_sector_boundaries():
                return False

            self.fit_ship_domains()
            self.visualize_fitting_results()
            result_file = self.save_results()

            print("\n" + "=" * 60)
            print("🎉 船舶领域拟合完成！")
            print(f"📁 主要输出:")
            print(f"   椭圆参数: {result_file}")
            print(f"   可视化图: vis/ship_domain_fitting/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 船舶领域拟合失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数"""
    print(f"🎯 船舶领域拟合系统")
    print(f"   输入文件: sector_boundaries_results.pkl")
    print(f"   输出文件: ship_domain_ellipse_params.pkl")

    # 创建拟合器
    fitter = ShipDomainFitter(debug=False)

    # 运行完整拟合
    success = fitter.run_full_fitting()

    if success:
        print(f"\n📁 主要输出文件:")
        print(f"   椭圆参数: result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        print(f"   参数表格: result/ship_domain_fitting/ship_domain_ellipse_parameters.csv")
        print(f"   可视化图: vis/ship_domain_fitting/")
    else:
        print(f"\n❌ 拟合失败")


if __name__ == '__main__':
    main()
