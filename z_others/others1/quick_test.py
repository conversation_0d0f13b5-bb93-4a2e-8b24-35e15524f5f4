import numpy as np

def method_1_reference(dx, dy, heading):
    """参考.py的实现"""
    target_heading_rad = np.deg2rad(-heading)
    cos_angle = np.cos(target_heading_rad)
    sin_angle = np.sin(target_heading_rad)
    new_x = cos_angle * dx + sin_angle * dy
    new_y = -sin_angle * dx + cos_angle * dy
    return new_x, new_y

def method_3_statistics(dx, dy, heading):
    """03.统计.py的实现"""
    theta = np.deg2rad(90 - heading)
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    new_x = cos_theta * dx - sin_theta * dy
    new_y = sin_theta * dx + cos_theta * dy
    return new_x, new_y

def method_4_notebook(dx, dy, heading):
    """z_others/test.ipynb的实现"""
    heading_rad = np.deg2rad(heading)
    cos_h = np.cos(heading_rad)
    sin_h = np.sin(heading_rad)
    new_x = dx * cos_h - dy * sin_h
    new_y = dx * sin_h + dy * cos_h
    return new_x, new_y

# 测试用例
print("测试用例：船舶航向0度（正北），他船在正东100米处")
print("预期结果：在船舶坐标系中，他船应该在右舷，即X=100, Y=0")
print("-" * 60)

dx, dy, heading = 100, 0, 0

result1 = method_1_reference(dx, dy, heading)
result3 = method_3_statistics(dx, dy, heading)
result4 = method_4_notebook(dx, dy, heading)

print(f"参考.py实现:     X={result1[0]:8.1f}, Y={result1[1]:8.1f}")
print(f"03.统计.py实现:  X={result3[0]:8.1f}, Y={result3[1]:8.1f}")
print(f"test.ipynb实现:  X={result4[0]:8.1f}, Y={result4[1]:8.1f}")

print("\n" + "=" * 60)
print("测试用例：船舶航向0度（正北），他船在正北100米处")
print("预期结果：在船舶坐标系中，他船应该在船头，即X=0, Y=100")
print("-" * 60)

dx, dy, heading = 0, 100, 0

result1 = method_1_reference(dx, dy, heading)
result3 = method_3_statistics(dx, dy, heading)
result4 = method_4_notebook(dx, dy, heading)

print(f"参考.py实现:     X={result1[0]:8.1f}, Y={result1[1]:8.1f}")
print(f"03.统计.py实现:  X={result3[0]:8.1f}, Y={result3[1]:8.1f}")
print(f"test.ipynb实现:  X={result4[0]:8.1f}, Y={result4[1]:8.1f}")

print("\n" + "=" * 60)
print("测试用例：船舶航向90度（正东），他船在正北100米处")
print("预期结果：在船舶坐标系中，他船应该在左舷，即X=-100, Y=0")
print("-" * 60)

dx, dy, heading = 0, 100, 90

result1 = method_1_reference(dx, dy, heading)
result3 = method_3_statistics(dx, dy, heading)
result4 = method_4_notebook(dx, dy, heading)

print(f"参考.py实现:     X={result1[0]:8.1f}, Y={result1[1]:8.1f}")
print(f"03.统计.py实现:  X={result3[0]:8.1f}, Y={result3[1]:8.1f}")
print(f"test.ipynb实现:  X={result4[0]:8.1f}, Y={result4[1]:8.1f}") 