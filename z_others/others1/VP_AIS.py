import pickle
from dataclasses import dataclass
from typing import Tuple, List

import matplotlib
import numpy as np
import pandas as pd
from tqdm import tqdm

matplotlib.use('TkAgg')
import matplotlib.pyplot as plt

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
from matplotlib.animation import FuncAnimation
from datetime import datetime
import matplotlib.lines as mlines

# 简化的航道数据
channel_polygon = [
    (121.073141, 31.764529), (121.064957, 31.755378), (121.063715, 31.753956), (121.072114, 31.750114),
    (121.077344, 31.746898), (121.080174, 31.745144), (121.085449, 31.741783), (121.088838, 31.739957),
    (121.092785, 31.737365), (121.095359, 31.735868), (121.096089, 31.735357), (121.096947, 31.734737),
    (121.097977, 31.733751), (121.100553, 31.731416), (121.106692, 31.72576), (121.110385, 31.72233),
    (121.116613, 31.716677), (121.118461, 31.715327), (121.119363, 31.714489), (121.124218, 31.710441),
    (121.126882, 31.708473), (121.129633, 31.706176), (121.134146, 31.702567), (121.135436, 31.701474),
    (121.136295, 31.700781), (121.168588, 31.682797), (121.182048, 31.675445), (121.184069, 31.673768),
    (121.246666, 31.62809), (121.256662, 31.62045), (121.266311, 31.611307), (121.269998, 31.607686),
    (121.274156, 31.603881), (121.277242, 31.600405), (121.280284, 31.596892), (121.286412, 31.590085),
    (121.293995, 31.580825), (121.297593, 31.575884), (121.305731, 31.564864), (121.309029, 31.561202),
    (121.322732, 31.546549), (121.333179, 31.554428), (121.319005, 31.569337), (121.316692, 31.569337),
    (121.311553, 31.57904), (121.305215, 31.588373), (121.302645, 31.592143), (121.277197, 31.615536),
    (121.263908, 31.627535), (121.223739, 31.656763), (121.20767, 31.667851), (121.198042, 31.674558),
    (121.188755, 31.680898), (121.172156, 31.691314), (121.164932, 31.69539), (121.151342, 31.703618),
    (121.145839, 31.70631), (121.13767, 31.714038), (121.119877, 31.730735), (121.110255, 31.740291),
    (121.10493, 31.745106), (121.104157, 31.746274)]


@dataclass
class VOCollision:
    """VO碰撞风险数据"""
    timestamp: float
    ship_a_id: int
    ship_b_id: int
    current_distance: float


class VelocityObstacleDetector:
    """纯速度障碍法检测器 - 使用真实船舶尺寸"""

    def __init__(self):
        self.earth_radius = 6371000

    def calculate_ship_radius(self, length: float, width: float) -> float:
        """
        根据船舶真实尺寸计算碰撞半径
        使用外接圆半径作为VO计算的船舶半径
        """
        if pd.isna(length) or pd.isna(width) or length <= 0 or width <= 0:
            return 50.0  # 默认半径50米
        
        # 计算矩形船舶的外接圆半径
        radius = np.sqrt((length/2)**2 + (width/2)**2)
        return radius

    def lonlat_to_meters(self, lon1: float, lat1: float, lon2: float, lat2: float) -> Tuple[float, float]:
        """经纬度转米制坐标"""
        lat1_rad = np.radians(lat1)
        lat2_rad = np.radians(lat2)
        dlon_rad = np.radians(lon2 - lon1)
        dlat_rad = np.radians(lat2 - lat1)
        avg_lat = (lat1_rad + lat2_rad) / 2
        dx = dlon_rad * self.earth_radius * np.cos(avg_lat)
        dy = dlat_rad * self.earth_radius
        return dx, dy

    def speed_course_to_velocity(self, sog: float, cog: float) -> Tuple[float, float]:
        """SOG和COG转速度分量"""
        speed_ms = sog * 0.514444  # knots to m/s
        cog_rad = np.radians(90 - cog)  # 转换为数学坐标系
        vx = speed_ms * np.cos(cog_rad)
        vy = speed_ms * np.sin(cog_rad)
        return vx, vy

    def is_in_velocity_obstacle(self, ship_a_vel: np.ndarray, rel_pos: np.ndarray, 
                               ship_b_vel: np.ndarray, combined_radius: float) -> bool:
        """
        纯速度障碍法：检查船舶A的速度是否在相对于船舶B的VO锥形内
        
        Args:
            ship_a_vel: 船舶A的速度向量
            rel_pos: 相对位置向量 (ship_b_pos - ship_a_pos)  
            ship_b_vel: 船舶B的速度向量
            combined_radius: 两船的组合碰撞半径
        """
        distance = np.linalg.norm(rel_pos)
        
        if distance <= combined_radius:
            return True  # 已经在碰撞范围内

        # 计算VO锥形的半角
        sin_theta = combined_radius / distance
        cos_theta = np.sqrt(1 - sin_theta ** 2)

        # 相对位置的单位向量
        rel_pos_unit = rel_pos / distance

        # 相对速度
        rel_vel = ship_a_vel - ship_b_vel

        # 检查相对速度是否指向目标
        if np.dot(rel_vel, rel_pos) <= 0:
            return False

        # 计算VO锥形的两条边界
        left_boundary = np.array([
            rel_pos_unit[0] * cos_theta - rel_pos_unit[1] * sin_theta,
            rel_pos_unit[0] * sin_theta + rel_pos_unit[1] * cos_theta
        ])

        right_boundary = np.array([
            rel_pos_unit[0] * cos_theta + rel_pos_unit[1] * sin_theta,
            -rel_pos_unit[0] * sin_theta + rel_pos_unit[1] * cos_theta
        ])

        # 使用叉积判断相对速度是否在VO锥形内
        cross_left = np.cross(left_boundary, rel_vel)
        cross_right = np.cross(rel_vel, right_boundary)

        return cross_left >= 0 and cross_right >= 0

    def analyze_all_pairs(self, all_trajectories: List[pd.DataFrame], timestamp: float) -> List[VOCollision]:
        """分析所有船舶对之间的VO碰撞风险"""

        def find_closest_data(trajectory, timestamp):
            time_diff = np.abs(trajectory['PosTime'] - timestamp)
            closest_idx = time_diff.argmin()
            return trajectory.iloc[closest_idx]

        results = []
        n_ships = len(all_trajectories)

        # 获取所有船舶状态
        ship_states = []
        for i, trajectory in enumerate(all_trajectories):
            try:
                data = find_closest_data(trajectory, timestamp)
                lon, lat = data['Lon'], data['Lat']
                sog, cog = data['Sog'], data['Cog']
                length, width = data['Length'], data['Width']
                
                vel = np.array(self.speed_course_to_velocity(sog, cog))
                radius = self.calculate_ship_radius(length, width)
                
                ship_states.append({
                    'id': i,
                    'pos': (lon, lat),
                    'vel': vel,
                    'radius': radius,
                    'length': length,
                    'width': width,
                    'valid': True
                })
            except:
                ship_states.append({'valid': False})

        # 两两计算VO碰撞风险
        for i in range(n_ships):
            for j in range(i + 1, n_ships):
                if not ship_states[i]['valid'] or not ship_states[j]['valid']:
                    continue

                ship_a = ship_states[i]
                ship_b = ship_states[j]

                # 计算组合碰撞半径（两船半径之和）
                combined_radius = ship_a['radius'] + ship_b['radius']

                # 计算相对位置
                dx_ab, dy_ab = self.lonlat_to_meters(ship_a['pos'][0], ship_a['pos'][1],
                                                     ship_b['pos'][0], ship_b['pos'][1])
                rel_pos_ab = np.array([dx_ab, dy_ab])
                rel_pos_ba = -rel_pos_ab

                # 检查双向VO碰撞
                collision_a = self.is_in_velocity_obstacle(ship_a['vel'], rel_pos_ab, 
                                                         ship_b['vel'], combined_radius)
                collision_b = self.is_in_velocity_obstacle(ship_b['vel'], rel_pos_ba, 
                                                         ship_a['vel'], combined_radius)

                # 任一方向有碰撞风险就记录
                if collision_a or collision_b:
                    current_distance = np.linalg.norm(rel_pos_ab)
                    results.append(VOCollision(
                        timestamp=timestamp,
                        ship_a_id=i,
                        ship_b_id=j,
                        current_distance=current_distance
                    ))

        return results


class VOVisualizer:
    """VO可视化器"""

    def __init__(self, detector: VelocityObstacleDetector):
        self.detector = detector

    def create_animation(self, all_trajectories: List[pd.DataFrame],
                         save_path: str = "vo_animation.gif", interval: int = 500):
        """创建VO动态可视化"""

        total_frames = min(len(traj) for traj in all_trajectories)

        # 计算显示范围
        all_lons = []
        all_lats = []

        channel_lons = [p[0] for p in channel_polygon]
        channel_lats = [p[1] for p in channel_polygon]
        all_lons.extend(channel_lons)
        all_lats.extend(channel_lats)

        for trajectory in all_trajectories:
            all_lons.extend(trajectory['Lon'].tolist())
            all_lats.extend(trajectory['Lat'].tolist())

        lon_margin = (max(all_lons) - min(all_lons)) * 0.1
        lat_margin = (max(all_lats) - min(all_lats)) * 0.1

        FIXED_LON_MIN = min(all_lons) - lon_margin
        FIXED_LON_MAX = max(all_lons) + lon_margin
        FIXED_LAT_MIN = min(all_lats) - lat_margin
        FIXED_LAT_MAX = max(all_lats) + lat_margin

        fig, ax = plt.subplots(figsize=(15, 10))

        def draw_course_arrow(ax, lon, lat, cog, color='blue', scale=0.003):
            if pd.isna(cog):
                return
            math_angle = np.radians(90 - cog)
            dx = scale * np.cos(math_angle)
            dy = scale * np.sin(math_angle)
            ax.arrow(lon, lat, dx, dy, head_width=0.0008, head_length=0.0012,
                     fc=color, ec=color, linewidth=2, alpha=0.8)

        def animate(frame):
            ax.clear()

            if frame >= total_frames:
                return

            current_time = all_trajectories[0].iloc[frame]['PosTime']
            collisions = self.detector.analyze_all_pairs(all_trajectories, current_time)

            ax.set_xlim(FIXED_LON_MIN, FIXED_LON_MAX)
            ax.set_ylim(FIXED_LAT_MIN, FIXED_LAT_MAX)

            # 绘制航道
            polygon = channel_polygon + [channel_polygon[0]]
            lons = [p[0] for p in polygon]
            lats = [p[1] for p in polygon]
            ax.plot(lons, lats, 'b--', alpha=0.5, linewidth=2)
            ax.fill(lons, lats, color='lightblue', alpha=0.1)

            # 碰撞船舶集合
            collision_ships = set()
            for collision in collisions:
                collision_ships.add(collision.ship_a_id)
                collision_ships.add(collision.ship_b_id)

            # 绘制船舶
            ship_positions = {}
            ship_info = {}
            for ship_id, trajectory in enumerate(all_trajectories):
                if frame >= len(trajectory):
                    continue

                ship_data = trajectory.iloc[frame]
                ship_lon, ship_lat = ship_data['Lon'], ship_data['Lat']
                ship_cog = ship_data['Cog']
                ship_length, ship_width = ship_data['Length'], ship_data['Width']

                ship_positions[ship_id] = (ship_lon, ship_lat)
                ship_info[ship_id] = (ship_length, ship_width)

                if ship_id in collision_ships:
                    color = 'red'
                    size = 20
                else:
                    color = 'green'
                    size = 20

                ax.scatter(ship_lon, ship_lat, c=color, s=size, marker='o',
                           alpha=0.8, edgecolor='black', linewidth=2)
                draw_course_arrow(ax, ship_lon, ship_lat, ship_cog, color, 0.002)

                # # 显示船舶尺寸信息
                # if ship_id < 5:  # 只显示前几艘船的信息避免太乱
                #     radius = self.detector.calculate_ship_radius(ship_length, ship_width)
                #     ax.text(ship_lon + 0.002, ship_lat - 0.002,
                #            f'船{ship_id}\nL:{ship_length:.0f}m W:{ship_width:.0f}m\nR:{radius:.0f}m',
                #            fontsize=7, ha='left', va='top',
                #            bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.7))

            # 绘制碰撞连线
            for collision in collisions:
                ship_a_id = collision.ship_a_id
                ship_b_id = collision.ship_b_id

                if ship_a_id in ship_positions and ship_b_id in ship_positions:
                    pos_a = ship_positions[ship_a_id]
                    pos_b = ship_positions[ship_b_id]

                    ax.plot([pos_a[0], pos_b[0]], [pos_a[1], pos_b[1]],
                            color='red', linewidth=3, alpha=0.8, zorder=5)

                    # mid_lon = (pos_a[0] + pos_b[0]) / 2
                    # mid_lat = (pos_a[1] + pos_b[1]) / 2

                    # # 显示距离和组合半径信息
                    # if ship_a_id in ship_info and ship_b_id in ship_info:
                    #     length_a, width_a = ship_info[ship_a_id]
                    #     length_b, width_b = ship_info[ship_b_id]
                    #     radius_a = self.detector.calculate_ship_radius(length_a, width_a)
                    #     radius_b = self.detector.calculate_ship_radius(length_b, width_b)
                    #     combined_radius = radius_a + radius_b
                    #
                    #     ax.text(mid_lon, mid_lat,
                    #            f'距离:{collision.current_distance:.0f}m\n组合半径:{combined_radius:.0f}m',
                    #            fontsize=8, ha='center', va='center',
                    #            bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.8))

            collision_count = len(collisions)
            total_ships = len([i for i, traj in enumerate(all_trajectories) if frame < len(traj)])

            ax.set_title(
                f'速度障碍法碰撞检测（真实船舶尺寸） - {datetime.fromtimestamp(current_time).strftime("%H:%M:%S")}\n'
                f'船舶总数: {total_ships}艘 | 碰撞风险对: {collision_count}对',
                fontsize=14, fontweight='bold')
            ax.set_xlabel('经度')
            ax.set_ylabel('纬度')
            ax.grid(True, alpha=0.3)

            status = "🚨碰撞风险🚨" if collision_count > 0 else "✅安全"
            fig.suptitle(f'VO碰撞监测（基于真实尺寸） - {status}', fontsize=16, fontweight='bold')

        frames_to_show = min(total_frames, 100)
        step = max(1, total_frames // frames_to_show)
        frame_indices = range(0, total_frames, step)

        anim = FuncAnimation(fig, animate, frames=frame_indices, interval=interval, repeat=True)

        if save_path:
            anim.save(save_path, writer='pillow', fps=2)
            print(f"VO动画保存到: {save_path}")

        plt.show()
        return anim


# 使用示例
if __name__ == "__main__":
    trajectory_list = pickle.load(open("data/2024_3_inter.pkl", "rb"))
    tras_df = pd.read_parquet('data/tras_2024_3_inter.parquet')

    reference_ship = trajectory_list[8878]
    ts, te = reference_ship['PosTime'].values[0], reference_ship['PosTime'].values[-1]

    other_ships = tras_df.loc[(tras_df['PosTime'] >= ts) & (tras_df['PosTime'] <= te)]
    all_ships_list = [reference_ship]

    for ms in tqdm(other_ships['MMSI'].unique()):
        if ms == reference_ship['MMSI'].iloc[0]:
            continue

        data_ms = other_ships.loc[other_ships['MMSI'] == ms].sort_values(by="PosTime", ascending=True)
        data_ms = data_ms[['MMSI', 'PosTime', 'Lon', 'Lat', 'Sog', 'Cog', 'Length', 'Width', 'Type']]
        data_ms.reset_index(drop=True, inplace=True)

        data_ms['TimeDiff'] = data_ms['PosTime'].diff()
        data_ms['SplitPoint'] = data_ms['TimeDiff'] >= 5 * 60
        data_ms['SegmentID'] = data_ms['SplitPoint'].cumsum()

        grouped = data_ms.groupby('SegmentID')
        for _, group in grouped:
            tra_segment = group
            track1 = [(Lon, lat) for Lon, lat in zip(tra_segment['Lon'], tra_segment['Lat'])]
            if len(track1) >= 10:
                tra_segment = tra_segment.drop(['TimeDiff', 'SplitPoint', 'SegmentID'], axis=1)
                tra_segment = tra_segment.reset_index(drop=True)
                all_ships_list.append(tra_segment)

    print(f"总共分析 {len(all_ships_list)} 艘船舶")

    detector = VelocityObstacleDetector()
    visualizer = VOVisualizer(detector)

    mid_time = (ts + te) / 2
    results = detector.analyze_all_pairs(all_ships_list, mid_time)

    print(f"检测到 {len(results)} 对船舶存在VO碰撞风险")
    for collision in results:
        print(f"船舶{collision.ship_a_id} <-> 船舶{collision.ship_b_id}: "
              f"距离={collision.current_distance:.0f}m")

    print("正在创建基于真实船舶尺寸的VO动态可视化...")
    visualizer.create_animation(all_ships_list, save_path="vo_realistic_animation.gif", interval=200)
