"""
船舶安全域建模 - 密度梯度分析法
核心思想：通过分析距离分布的密度变化，找到避让行为的临界点

主要功能：
1. 自适应分位数选择：每个扇区根据数据分布特征自动确定最优分位数
2. 可调扇区数量：支持6-120个扇区，精度可调
3. 双重可视化：梯度分析过程 + 最终安全域
4. 详细报告：包含统计分析和方法对比

使用方法：
    # 交互模式（用户选择扇区数量）
    python gradient_safety_domain.py
    
    # 程序化模式（指定扇区数量）
    from gradient_safety_domain import main
    main(num_sectors=36)  # 使用36个扇区
    
    # 创建模型对象（可自定义扇区数量）
    model = GradientBasedSafetyDomainModel(num_sectors=24)
"""

import os
import pickle
import warnings

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import leastsq
from tqdm import tqdm

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class GradientBasedSafetyDomainModel:
    """
    基于密度梯度分析的船舶安全域建模
    """

    def __init__(self, num_sectors=24):
        self.encounter_data = []
        self.safety_domain = {}
        self.optimal_percentiles = {}  # 存储每个扇区的最优分位数
        self.num_sectors = num_sectors  # 扇区数量
        self.sector_angle = 360 / num_sectors  # 每个扇区的角度
        
    def extract_encounter_data(self, force_rebuild=False):
        """提取会遇数据"""
        encounter_cache_file = 'result/encounter_data_cache.pkl'
        
        if not force_rebuild and os.path.exists(encounter_cache_file):
            print("检测到已存在的会遇数据缓存，正在加载...")
            try:
                with open(encounter_cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                
                # 合并所有情境的数据
                all_encounters = []
                for context_data in cached_data['context_data'].values():
                    all_encounters.extend(context_data)
                
                self.encounter_data = all_encounters
                print(f"✅ 成功加载缓存: {len(self.encounter_data)} 个会遇记录")
                return
                
            except Exception as e:
                print(f"❌ 缓存文件损坏，将重新提取数据: {e}")
        
        print("正在加载避让场景数据...")
        with open('../result/avoid_results.pkl', 'rb') as f:
            avoid_results = pickle.load(f)

        tras_df = pd.read_parquet('../../data/tras_2024_1_6_inter.parquet')
        
        print("正在建立MMSI索引...")
        mmsi_groups = {}
        for mmsi, group in tqdm(tras_df.groupby('MMSI'), desc="建立索引"):
            mmsi_groups[mmsi] = group
        
        print("正在提取避让会遇数据...")
        encounters = []
        
        for s_pair, pos_time in tqdm(zip(avoid_results['s_pair'], avoid_results['PosTime']),
                                     total=len(avoid_results['s_pair']),
                                     desc="处理避让场景"):
            try:
                ship1_id, ship2_id = s_pair
                
                if ship1_id in mmsi_groups and ship2_id in mmsi_groups:
                    ship1_data = self._get_ship_state(mmsi_groups[ship1_id], pos_time)
                    ship2_data = self._get_ship_state(mmsi_groups[ship2_id], pos_time)
                    
                    if ship1_data is not None and ship2_data is not None:
                        if self._validate_ship_data(ship1_data, ship2_data):
                            encounters.append(self._create_encounter_record(ship1_data, ship2_data))
                            encounters.append(self._create_encounter_record(ship2_data, ship1_data))
                            
            except Exception as e:
                continue
        
        self.encounter_data = encounters
        print(f"成功提取 {len(encounters)} 个会遇记录")

    def find_optimal_percentile_by_gradient(self, sector_data):
        """
        密度梯度分析法：找到距离分布中密度变化最剧烈的位置
        """
        distances = sector_data['Distance'].values
        
        if len(distances) < 10:  # 数据量太少
            return 0.05, np.median(distances)
        
        try:
            # 1. 数据预处理：移除极端异常值
            q1, q99 = np.percentile(distances, [1, 99])
            filtered_distances = distances[(distances >= q1) & (distances <= q99)]
            
            if len(filtered_distances) < 5:
                return 0.05, np.median(distances)
            
            # 2. 核密度估计
            kde = stats.gaussian_kde(filtered_distances)
            
            # 3. 创建距离采样点
            distance_min, distance_max = filtered_distances.min(), filtered_distances.max()
            distance_range = np.linspace(distance_min, distance_max, 500)
            
            # 4. 计算密度值
            density = kde(distance_range)
            
            # 5. 计算密度的一阶导数（梯度）
            gradient = np.gradient(density, distance_range)
            
            # 6. 寻找梯度的极值点
            # 我们寻找负梯度最大的点（密度下降最快的点）
            # 这可能表示从"高风险密集区"向"低风险稀疏区"的转折点
            negative_gradient_mask = gradient < 0
            if np.any(negative_gradient_mask):
                max_negative_gradient_idx = np.argmin(gradient[negative_gradient_mask])
                # 获取在原数组中的索引
                negative_indices = np.where(negative_gradient_mask)[0]
                critical_distance = distance_range[negative_indices[max_negative_gradient_idx]]
            else:
                # 如果没有负梯度，使用梯度绝对值最大的点
                critical_distance = distance_range[np.argmax(np.abs(gradient))]
            
            # 7. 计算对应的分位数
            optimal_percentile = np.mean(distances <= critical_distance)
            
            # 8. 合理性检查和调整
            optimal_percentile = np.clip(optimal_percentile, 0.01, 0.30)
            
            return optimal_percentile, critical_distance
            
        except Exception as e:
            print(f"密度梯度分析失败: {e}，使用默认5%分位数")
            return 0.05, np.percentile(distances, 5)

    def build_safety_domain(self):
        """构建基于密度梯度分析的安全域"""
        print("正在构建基于密度梯度分析的安全域...")
        print(f"扇区设置: {self.num_sectors}个扇区，每个扇区{self.sector_angle:.1f}°")
        
        if len(self.encounter_data) < 100:
            print("数据量不足，无法构建安全域")
            return
        
        # 转换为DataFrame
        df = self._encounters_to_dataframe()
        print(f"数据准备完成，共{len(df)}个避让记录")
        
        # 计算角度
        df['Angle'] = (np.degrees(np.arctan2(df['RelativeY'], df['RelativeX'])) + 360) % 360
        
        # 使用密度梯度分析计算安全域参数
        domain_params = self._calculate_safety_domain_with_gradient(df)
        
        if domain_params is not None:
            self.safety_domain = {
                'method': 'gradient_based_ellipse',
                'sample_size': len(df),
                'ellipse_params': domain_params,
                'raw_data': df,
                'optimal_percentiles': self.optimal_percentiles,
                'length_range': (df['OwnLength'].min(), df['OwnLength'].max()),
                'speed_range': (df['OwnSpeed'].min(), df['OwnSpeed'].max())
            }
            
            print(f"✅ 安全域拟合完成:")
            print(f"   椭圆参数: a={domain_params['a']:.0f}m, b={domain_params['b']:.0f}m")
            print(f"   椭圆面积: {np.pi * domain_params['a'] * domain_params['b']:.0f}m²")
            print(f"   边界点数: {len(domain_params['boundary_points'])}")
            print(f"   分位数范围: {min(self.optimal_percentiles.values()):.3f} - {max(self.optimal_percentiles.values()):.3f}")
        else:
            print("❌ 安全域拟合失败")

    def _calculate_safety_domain_with_gradient(self, df):
        """使用密度梯度分析计算安全域参数"""
        try:
            boundary_points = []
            gradient_analysis_results = []
            
            print("正在进行扇区密度梯度分析...")
            
            # 按角度扇区分析
            for i in tqdm(range(self.num_sectors), desc="扇区分析"):
                angle = i * self.sector_angle
                angle_end = (i + 1) * self.sector_angle
                
                # 创建扇区掩码
                if angle_end <= 360:
                    mask = (df['Angle'] >= angle) & (df['Angle'] < angle_end)
                else:
                    mask = (df['Angle'] >= angle) | (df['Angle'] < angle_end - 360)
                
                sector_data = df[mask]
                
                if len(sector_data) > 0:
                    # 使用密度梯度分析找到最优分位数
                    optimal_percentile, critical_distance = self.find_optimal_percentile_by_gradient(sector_data)
                    
                    # 存储分析结果
                    self.optimal_percentiles[f"sector_{i}"] = optimal_percentile
                    gradient_analysis_results.append({
                        'sector': f"{angle}°-{angle_end}°",
                        'data_count': len(sector_data),
                        'optimal_percentile': optimal_percentile,
                        'critical_distance': critical_distance
                    })
                    
                    # 根据最优分位数提取边界点
                    threshold_distance = sector_data['Distance'].quantile(optimal_percentile)
                    
                    # 找到距离最接近阈值距离的点
                    closest_idx = (sector_data['Distance'] - threshold_distance).abs().idxmin()
                    closest_point = sector_data.loc[closest_idx]
                    
                    boundary_points.append((closest_point['RelativeX'], closest_point['RelativeY']))
            
            # 输出分析结果
            print("\n密度梯度分析结果:")
            for result in gradient_analysis_results[:6]:  # 显示前6个扇区的结果
                print(f"  {result['sector']}: 最优分位数={result['optimal_percentile']:.3f}, "
                      f"临界距离={result['critical_distance']:.0f}m, 数据量={result['data_count']}")
            print(f"  ... (共{len(gradient_analysis_results)}个扇区)")
            
            if len(boundary_points) < 8:
                print("边界点不足，无法拟合椭圆")
                return None
            
            # 椭圆拟合
            X, Y = zip(*boundary_points)
            X, Y = np.array(X), np.array(Y)
            
            # 使用最小二乘法拟合椭圆
            a_initial = (np.max(Y) - np.min(Y)) / 2
            b_initial = (np.max(X) - np.min(X)) / 2
            initial_params = [a_initial, b_initial]
            
            params, _ = leastsq(self._ellipse_residual, initial_params, args=(X, Y))
            a, b = abs(params[0]), abs(params[1])
            
            return {
                'a': a,
                'b': b,
                'boundary_points': boundary_points,
                'center': (0, 0),
                'gradient_analysis': gradient_analysis_results
            }
            
        except Exception as e:
            print(f"梯度分析安全域计算失败: {e}")
            return None

    def _encounters_to_dataframe(self):
        """将会遇记录转换为DataFrame"""
        data = []
        for encounter in self.encounter_data:
            data.append({
                'RelativeX': encounter['relative_x'],
                'RelativeY': encounter['relative_y'],
                'Distance': encounter['distance'],
                'OwnLength': encounter['own_length'],
                'OwnSpeed': encounter['own_speed'],
                'OtherLength': encounter['other_length'],
                'OtherSpeed': encounter['other_speed']
            })
        return pd.DataFrame(data)

    @staticmethod
    def _ellipse_residual(params, x, y):
        """椭圆残差函数"""
        a, b = params
        return (x / b) ** 2 + (y / a) ** 2 - 1

    def visualize_gradient_analysis(self):
        """可视化密度梯度分析过程"""
        print("正在生成密度梯度分析可视化...")
        
        if not self.safety_domain or 'raw_data' not in self.safety_domain:
            print("无数据可视化")
            return
        
        df = self.safety_domain['raw_data']
        df['Angle'] = (np.degrees(np.arctan2(df['RelativeY'], df['RelativeX'])) + 360) % 360
        
        # 选择几个代表性扇区进行可视化
        # 自动选择4个均匀分布的扇区：前、右、后、左
        quarter_sectors = self.num_sectors // 4
        representative_sector_indices = [0, quarter_sectors, quarter_sectors * 2, quarter_sectors * 3]
        representative_angles = [i * self.sector_angle for i in representative_sector_indices]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, angle in enumerate(representative_angles):
            ax = axes[i]
            angle_end = angle + self.sector_angle
            
            # 获取扇区数据
            if angle_end <= 360:
                mask = (df['Angle'] >= angle) & (df['Angle'] < angle_end)
            else:
                mask = (df['Angle'] >= angle) | (df['Angle'] < angle_end - 360)
            
            sector_data = df[mask]
            
            if len(sector_data) > 10:
                distances = sector_data['Distance'].values
                
                # 执行梯度分析
                optimal_percentile, critical_distance = self.find_optimal_percentile_by_gradient(sector_data)
                
                # 绘制距离分布直方图
                ax.hist(distances, bins=50, alpha=0.7, density=True, color='lightblue', 
                       edgecolor='black', label='距离分布')
                
                # 绘制核密度估计曲线
                try:
                    q1, q99 = np.percentile(distances, [1, 99])
                    filtered_distances = distances[(distances >= q1) & (distances <= q99)]
                    kde = stats.gaussian_kde(filtered_distances)
                    
                    x_range = np.linspace(filtered_distances.min(), filtered_distances.max(), 500)
                    density = kde(x_range)
                    ax.plot(x_range, density, 'r-', linewidth=2, label='密度曲线')
                    
                    # 计算和绘制梯度
                    gradient = np.gradient(density, x_range)
                    ax2 = ax.twinx()
                    ax2.plot(x_range, gradient, 'g--', alpha=0.7, label='密度梯度')
                    ax2.set_ylabel('密度梯度', color='green')
                    ax2.tick_params(axis='y', labelcolor='green')
                    
                    # 标记临界距离
                    ax.axvline(critical_distance, color='red', linestyle=':', linewidth=2, 
                              label=f'临界距离 ({critical_distance:.0f}m)')
                    
                except Exception as e:
                    print(f"扇区{angle}°梯度分析可视化失败: {e}")
                
                ax.set_title(f'扇区 {angle}°-{angle_end}°\n'
                           f'最优分位数: {optimal_percentile:.3f}\n'
                           f'临界距离: {critical_distance:.0f}m')
                ax.set_xlabel('距离 (m)')
                ax.set_ylabel('密度')
                ax.legend(loc='upper right')
                ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('gradient_analysis_visualization.png', dpi=300, bbox_inches='tight')
        print("✅ 梯度分析可视化已保存: gradient_analysis_visualization.png")
        plt.show()

    def visualize_safety_domain(self):
        """可视化最终的安全域结果"""
        print("正在生成安全域拟合可视化...")
        
        if not self.safety_domain or 'ellipse_params' not in self.safety_domain:
            print("无安全域数据可视化")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 12))
        ellipse_params = self.safety_domain['ellipse_params']
        
        # 绘制边界点
        boundary_points = np.array(ellipse_params['boundary_points'])
        scatter = ax.scatter(boundary_points[:, 0], boundary_points[:, 1], 
                            c='darkblue', s=50, alpha=0.8, zorder=5, 
                            edgecolors='navy', linewidth=1, label='梯度分析边界点')
        
        # 绘制椭圆
        a, b = ellipse_params['a'], ellipse_params['b']
        theta = np.linspace(0, 2 * np.pi, 200)
        x_ellipse = b * np.cos(theta)
        y_ellipse = a * np.sin(theta)
        
        ax.plot(x_ellipse, y_ellipse, color='darkred', linewidth=4, 
               alpha=0.9, zorder=6, label='梯度优化安全域')
        ax.fill(x_ellipse, y_ellipse, color='red', alpha=0.1, zorder=1)
        
        # 绘制参考圆圈
        for radius in [200, 500, 800]:
            circle = plt.Circle((0, 0), radius, fill=False, 
                              linestyle=':', color='gray', alpha=0.4, linewidth=1)
            ax.add_patch(circle)
            ax.text(radius*0.7, radius*0.7, f'{radius}m', 
                   fontsize=9, alpha=0.7, ha='center')
        
        # 绘制船舶
        self._draw_own_ship(ax, size=1.5)
        
        # 添加方向指示
        ax.arrow(0, 200, 0, 150, head_width=50, head_length=40, 
                fc='black', ec='black', alpha=0.7, zorder=10)
        ax.text(80, 300, '船首', fontsize=16, ha='left', va='center',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', 
                        alpha=0.8, edgecolor='orange'))
        
        # 统计信息
        percentile_stats = list(self.optimal_percentiles.values())
        stats_text = [
            f"梯度优化安全域",
            f"长轴: {a:.0f}m × 短轴: {b:.0f}m",
            f"面积: {np.pi * a * b / 10000:.1f}万m²",
            f"扇区: {self.num_sectors}个 × {self.sector_angle:.1f}°",
            f"分位数: {min(percentile_stats):.3f}-{max(percentile_stats):.3f}",
            f"平均分位数: {np.mean(percentile_stats):.3f}"
        ]
        
        ax.text(0.02, 0.98, '\n'.join(stats_text),
               transform=ax.transAxes, verticalalignment='top',
               fontsize=14, weight='bold',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='white', 
                        alpha=0.9, edgecolor='gray'))
        
        ax.set_xlabel('横向距离 (m)', fontsize=14, weight='bold')
        ax.set_ylabel('纵向距离 (m)', fontsize=14, weight='bold')
        ax.set_title('基于密度梯度分析的船舶安全域', fontsize=18, weight='bold', pad=20)
        
        ax.legend(loc='upper right', framealpha=0.9, fontsize=12)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.set_facecolor('#fafafa')
        ax.axis('equal')
        
        # 设置显示范围
        max_range = max(a, b) * 1.2
        ax.set_xlim(-max_range, max_range)
        ax.set_ylim(-max_range, max_range)
        
        plt.tight_layout()
        plt.savefig('gradient_based_safety_domain.png', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print("✅ 梯度分析安全域已保存: gradient_based_safety_domain.png")
        plt.show()

    def _draw_own_ship(self, ax, size=1.0):
        """绘制船舶"""
        ship_length = 120 * size
        ship_width = 30 * size
        
        # 船体（橙色）
        ship_x = [-ship_width/2, ship_width/2, ship_width/2, -ship_width/2, -ship_width/2]
        ship_y = [-ship_length/2, -ship_length/2, ship_length/2, ship_length/2, -ship_length/2]
        ax.fill(ship_x, ship_y, color='orange', alpha=0.9, zorder=15, 
               edgecolor='darkorange', linewidth=2)
        
        # 船首（金色）
        bow_x = [-ship_width/4, ship_width/4, 0, -ship_width/4]
        bow_y = [ship_length/2, ship_length/2, ship_length/2 + ship_width/2, ship_length/2]
        ax.fill(bow_x, bow_y, color='gold', alpha=0.9, zorder=16,
               edgecolor='darkorange', linewidth=2)
        
        # 中心点
        ax.plot(0, 0, 'ko', markersize=8, zorder=20)

    def generate_report(self):
        """生成梯度分析报告"""
        print("\n" + "=" * 80)
        print("基于密度梯度分析的船舶安全域建模报告")
        print("=" * 80)
        
        if not self.safety_domain:
            print("无安全域数据")
            return
        
        df = self.safety_domain['raw_data']
        ellipse_params = self.safety_domain['ellipse_params']
        percentile_stats = list(self.optimal_percentiles.values())
        
        print(f"\n数据统计:")
        print(f"  总样本数: {len(df)}")
        print(f"  船长范围: {self.safety_domain['length_range'][0]:.0f}-{self.safety_domain['length_range'][1]:.0f}m")
        print(f"  航速范围: {self.safety_domain['speed_range'][0]:.1f}-{self.safety_domain['speed_range'][1]:.1f}节")
        print(f"  扇区设置: {self.num_sectors}个扇区，每个{self.sector_angle:.1f}°")
        
        print(f"\n梯度分析结果:")
        print(f"  分位数范围: {min(percentile_stats):.3f} - {max(percentile_stats):.3f}")
        print(f"  平均分位数: {np.mean(percentile_stats):.3f}")
        print(f"  分位数标准差: {np.std(percentile_stats):.3f}")
        
        print(f"\n安全域参数:")
        a, b = ellipse_params['a'], ellipse_params['b']
        print(f"  椭圆长轴(a): {a:.0f}m")
        print(f"  椭圆短轴(b): {b:.0f}m")
        print(f"  椭圆面积: {np.pi * a * b:.0f}m²")
        print(f"  长短轴比: {a / b:.2f}")
        print(f"  边界点数: {len(ellipse_params['boundary_points'])}")
        
        # 与传统5%方法对比
        print(f"\n方法对比:")
        print(f"  传统5%方法: 固定分位数")
        print(f"  梯度分析法: 自适应分位数 (平均{np.mean(percentile_stats):.1%})")
        
        if np.mean(percentile_stats) < 0.05:
            print(f"  → 梯度分析发现更严格的安全边界")
        elif np.mean(percentile_stats) > 0.05:
            print(f"  → 梯度分析发现更宽松的安全边界")
        else:
            print(f"  → 梯度分析结果接近传统方法")

    def save_model(self, filepath='result/gradient_safety_domain_model.pkl'):
        """保存模型"""
        os.makedirs('../result', exist_ok=True)
        
        with open(filepath, 'wb') as f:
            pickle.dump(self.safety_domain, f)
        
        print(f"✅ 梯度分析模型已保存: {filepath}")

    # 辅助方法
    def _get_ship_state(self, ship_group, pos_time, tolerance=20):
        time_mask = abs(ship_group['PosTime'] - pos_time) <= tolerance
        matched_data = ship_group[time_mask]

        if len(matched_data) == 0:
            return None

        closest_idx = abs(matched_data['PosTime'] - pos_time).idxmin()
        row = matched_data.loc[closest_idx]

        return {
            'mmsi': int(row['MMSI']),
            'lon': float(row['Lon']),
            'lat': float(row['Lat']),
            'sog': float(row['Sog']),
            'cog': float(row['Cog']),
            'length': float(row['Length']) if pd.notna(row['Length']) else None,
            'width': float(row.get('Width', row['Length'] * 0.15)) if pd.notna(
                row.get('Width', row['Length'])) else None,
            'pos_time': int(row['PosTime'])
        }

    def _validate_ship_data(self, ship1, ship2):
        required_fields = ['length', 'sog', 'cog', 'lat', 'lon']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if ship[field] is None or pd.isna(ship[field]):
                    return False

        distance = self._calculate_distance(ship1, ship2)
        return (30 < distance < 1500 and
                ship1['sog'] <= 20 and ship2['sog'] <= 20 and
                10 <= ship1['length'] <= 400 and 10 <= ship2['length'] <= 400)

    def _create_encounter_record(self, own_ship, other_ship):
        dx, dy = self._calculate_relative_position(own_ship, other_ship)
        distance = np.sqrt(dx ** 2 + dy ** 2)
        
        relative_x, relative_y = self._convert_to_ship_coordinates(dx, dy, own_ship['cog'])

        return {
            'distance': distance,
            'own_length': own_ship['length'],
            'own_speed': own_ship['sog'],
            'other_length': other_ship['length'],
            'other_speed': other_ship['sog'],
            'relative_x': relative_x,
            'relative_y': relative_y
        }

    def _calculate_distance(self, ship1, ship2):
        dx, dy = self._calculate_relative_position(ship1, ship2)
        return np.sqrt(dx ** 2 + dy ** 2)

    def _calculate_relative_position(self, ship1, ship2):
        lat1, lon1 = ship1['lat'], ship1['lon']
        lat2, lon2 = ship2['lat'], ship2['lon']

        dx = (lon2 - lon1) * 111000 * np.cos(np.radians((lat1 + lat2) / 2))
        dy = (lat2 - lat1) * 111000

        return dx, dy

    def _convert_to_ship_coordinates(self, dx, dy, heading):
        """转换到船舶坐标系"""
        math_angle = np.deg2rad(90 - heading)
        cos_theta = np.cos(math_angle)
        sin_theta = np.sin(math_angle)
        
        ship_x = cos_theta * dx + sin_theta * dy
        ship_y = -sin_theta * dx + cos_theta * dy
        
        return ship_x, ship_y


def main(num_sectors=None):
    """
    主函数
    
    Args:
        num_sectors: 扇区数量，如果为None则进入交互模式
    """
    try:
        print("开始基于密度梯度分析的船舶安全域建模")
        print("=" * 60)

        if num_sectors is None:
            # 交互模式：让用户选择扇区数量
            print("\n请选择扇区数量:")
            print("1. 12个扇区 (每个30°) - 粗糙分析")
            print("2. 18个扇区 (每个20°) - 中等精度") 
            print("3. 24个扇区 (每个15°) - 标准精度 [默认]")
            print("4. 36个扇区 (每个10°) - 高精度")
            print("5. 72个扇区 (每个5°) - 超高精度")
            print("6. 自定义扇区数量")
            
            try:
                choice = input("\n请输入选择 (1-6，直接回车使用默认): ").strip()
                
                if choice == "":
                    num_sectors = 24
                elif choice == "1":
                    num_sectors = 12
                elif choice == "2":
                    num_sectors = 18
                elif choice == "3":
                    num_sectors = 24
                elif choice == "4":
                    num_sectors = 36
                elif choice == "5":
                    num_sectors = 72
                elif choice == "6":
                    while True:
                        try:
                            num_sectors = int(input("请输入自定义扇区数量 (建议6-120): "))
                            if 6 <= num_sectors <= 120:
                                break
                            else:
                                print("扇区数量应在6-120之间，请重新输入")
                        except ValueError:
                            print("请输入有效的数字")
                else:
                    print("无效选择，使用默认24个扇区")
                    num_sectors = 24
                    
            except (EOFError, KeyboardInterrupt):
                print("使用默认24个扇区")
                num_sectors = 24
        else:
            # 程序化模式：直接使用指定的扇区数量
            print(f"使用指定的扇区数量: {num_sectors}")
            if not (6 <= num_sectors <= 120):
                print("警告: 扇区数量超出建议范围 (6-120)，可能影响性能或精度")

        # 创建模型
        model = GradientBasedSafetyDomainModel(num_sectors=num_sectors)
        
        # 提取数据
        model.extract_encounter_data()
        
        # 构建安全域
        model.build_safety_domain()
        
        # 生成可视化
        print("\n" + "=" * 40)
        print("生成可视化结果")
        print("=" * 40)
        
        # 梯度分析过程可视化
        model.visualize_gradient_analysis()
        
        # 最终安全域可视化
        model.visualize_safety_domain()
        
        # 生成报告
        model.generate_report()
        
        # 保存模型
        model.save_model()
        
        print(f"\n✅ 密度梯度分析完成!")
        print(f"生成文件:")
        print(f"  1. gradient_analysis_visualization.png - 梯度分析过程")
        print(f"  2. gradient_based_safety_domain.png - 最终安全域")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


def quick_test():
    """快速测试不同扇区数量的效果"""
    test_sectors = [12, 24, 36]
    
    print("🧪 快速测试模式：比较不同扇区数量的效果")
    print("=" * 60)
    
    results = {}
    
    for num_sectors in test_sectors:
        print(f"\n正在测试 {num_sectors} 个扇区...")
        try:
            model = GradientBasedSafetyDomainModel(num_sectors=num_sectors)
            model.extract_encounter_data()
            model.build_safety_domain()
            
            if model.safety_domain and 'ellipse_params' in model.safety_domain:
                params = model.safety_domain['ellipse_params']
                percentiles = list(model.optimal_percentiles.values())
                
                results[num_sectors] = {
                    'a': params['a'],
                    'b': params['b'],
                    'area': np.pi * params['a'] * params['b'],
                    'boundary_points': len(params['boundary_points']),
                    'avg_percentile': np.mean(percentiles),
                    'percentile_std': np.std(percentiles)
                }
                
                print(f"✅ {num_sectors}扇区: a={params['a']:.0f}m, b={params['b']:.0f}m, "
                      f"平均分位数={np.mean(percentiles):.3f}")
            else:
                print(f"❌ {num_sectors}扇区: 建模失败")
                
        except Exception as e:
            print(f"❌ {num_sectors}扇区: 错误 - {e}")
    
    # 输出对比结果
    if results:
        print("\n📊 扇区数量对比结果:")
        print("-" * 60)
        print(f"{'扇区数':<8} {'长轴(m)':<10} {'短轴(m)':<10} {'面积(万m²)':<12} {'平均分位数':<12}")
        print("-" * 60)
        
        for num_sectors, result in results.items():
            print(f"{num_sectors:<8} {result['a']:<10.0f} {result['b']:<10.0f} "
                  f"{result['area']/10000:<12.1f} {result['avg_percentile']:<12.3f}")
        
        print("\n💡 建议:")
        print("  - 12扇区: 快速分析，适合初步探索")
        print("  - 24扇区: 标准精度，推荐日常使用")  
        print("  - 36扇区: 高精度，适合精细分析")


if __name__ == "__main__":
    import sys
    
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            quick_test()
        elif sys.argv[1].isdigit():
            main(num_sectors=int(sys.argv[1]))
        else:
            print("用法:")
            print("  python gradient_safety_domain.py        # 交互模式")
            print("  python gradient_safety_domain.py 24     # 指定24个扇区")
            print("  python gradient_safety_domain.py test   # 快速测试模式")
    else:
        main() 