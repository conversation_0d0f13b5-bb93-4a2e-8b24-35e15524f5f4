#%%
import pickle

import pandas as pd

trajectory_list = pickle.load(open("data/2024_3_inter.pkl", "rb"))
# tras_df = pd.concat(trajectory_list)
# tras_df.to_parquet('data/tras_2024_3_inter.parquet')
tras_df = pd.read_parquet('data/tras_2024_3_inter.parquet')
with open('data/geo_info.pkl', 'rb') as f:
    geo_information = pickle.load(f)
#%%
# 穿越场景提取算法
import random
import numpy as np
from shapely.geometry import Polygon, LineString, Point
from tqdm import tqdm


class CrossSceneExtraction:
    def __init__(self, trajectory_list, tras_df, geo_information):
        self.trajectory_list = trajectory_list
        self.tras_df = tras_df

        self.channel_side1 = geo_information['channel_side1']
        self.channel_side2 = geo_information['channel_side2']
        self.anchorage = geo_information['anchorage']
        self.channel_polygon = geo_information['channel_boundary']

    def alls_indentify(self, own_ship_list):
        # idx = random.randrange(len(own_ship_list))
        # own_ship = self.trajectory_list[own_ship_list[idx]]
        own_ship = self.trajectory_list[9829]
        ts, te = own_ship['PosTime'].values[0], own_ship['PosTime'].values[-1]
        # print(own_ship_list[idx], ts, te)
        print(9829, ts, te)
        all_ships = self.tras_df.loc[(self.tras_df['PosTime'] >= ts) & (self.tras_df['PosTime'] <= te)]
        all_ships_dicts = []
        for postime in tqdm(all_ships['PosTime'].unique(), desc='step2'):
            # 'PosTime', 'MMSI', 'Lon', 'Lat', 'Cog', 'Sog', 'Type', 'Width', 'Length'
            all_ships_array = np.array(all_ships[all_ships['PosTime'] == postime])
            all_ships_dicts.append([{
                'id': int(all_ships_array[i][1]),
                'lon': float(all_ships_array[i][2]),
                'lat': float(all_ships_array[i][3]),
                'cog': float(all_ships_array[i][4]),
                'sog': float(all_ships_array[i][5])} for i in range(len(all_ships_array))])
        return all_ships_dicts

    def cross_owns_indentify(self):
        own_ship_list = []
        for k, tra in enumerate(tqdm(self.trajectory_list, desc='step1')):
            track = [(Lon, lat) for Lon, lat in zip(tra['Lon'], tra['Lat'])]
            if len(track) < 2:
                continue  # 跳过点数不足2的轨迹
            if (self.is_channel_truly_crossed(track, self.channel_side1, self.channel_side2, consider_direction=True)
                    & self.is_in_area(track, self.anchorage)):
                own_ship_list.append(k)
        return own_ship_list

    def is_channel_truly_crossed(self, track_points, channel_side1, channel_side2, consider_direction=True):
        """判断轨迹是否先穿过side1再穿过side2"""
        track_line = LineString(track_points)
        cross1 = self.find_cross_points(track_line, channel_side1)
        cross2 = self.find_cross_points(track_line, channel_side2)

        # 必须两条线都有交点
        if not cross1 or not cross2:
            return False

        # 找出所有交点对应的轨迹索引
        idx1_list = []
        for pt in cross1:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx1_list.append(min_idx)

        idx2_list = []
        for pt in cross2:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx2_list.append(min_idx)

        # 只考虑有方向的情况
        if consider_direction:
            # 找到最早的side1交点
            first_side1_idx = min(idx1_list)
            # 检查是否存在在这个side1交点之后的side2交点
            later_side2_indices = [idx for idx in idx2_list if idx > first_side1_idx]
            return len(later_side2_indices) > 0
        else:
            # 如果不考虑方向，只要两条线都穿过就行
            return True

    @staticmethod
    def find_cross_points(track_line, side_edges):
        """返回轨迹线与一组边的所有交点"""
        cross_points = []
        for i in range(len(side_edges) - 1):
            edge = LineString([side_edges[i], side_edges[i + 1]])
            inter = track_line.intersection(edge)
            if not inter.is_empty:
                if inter.geom_type == 'Point':
                    cross_points.append(inter)
                elif inter.geom_type == 'MultiPoint':
                    cross_points.extend(list(inter.geoms))
                elif inter.geom_type == 'LineString':
                    # 处理线段重叠情况，取端点
                    cross_points.extend([Point(inter.coords[0]), Point(inter.coords[-1])])
                elif inter.geom_type == 'GeometryCollection':
                    # 处理所有几何类型
                    for g in inter.geoms:
                        if g.geom_type == 'Point':
                            cross_points.append(g)
                        elif g.geom_type == 'LineString':
                            cross_points.extend([Point(g.coords[0]), Point(g.coords[-1])])

        # 去除重复点（距离小于1米认为是同一点）
        unique_points = []
        for pt in cross_points:
            is_duplicate = False
            for existing_pt in unique_points:
                if pt.distance(existing_pt) < 1.0:  # 1米容差
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_points.append(pt)

        return unique_points

    @staticmethod
    def is_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        pt = track_points[0]
        if poly.contains(Point(pt)):
            return True
        else:
            return False

    @staticmethod
    def all_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        for pt in track_points:
            if not poly.contains(Point(pt)):
                return False
        return True

#%%
# 船舶会遇检测算法
import math
import random
from typing import List, Dict, Tuple, Optional

import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, FancyArrowPatch
from scipy.interpolate import interp1d
from scipy.optimize import minimize_scalar

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class EnhancedWaterwayEncounterDetector:
    """
    增强版基于航道中心线投影的船舶会遇检测器
    支持航道区域拟合和改进的可视化
    """

    def __init__(self, waterway_points: List[Tuple[float, float]],
                 waterway_boundary: Optional[List[Tuple[float, float]]] = None,
                 tcpa_threshold: int = 600,
                 use_fitted_curve: bool = False):
        """
        初始化检测器
        
        参数:
        waterway_points: 航道中心线的点坐标列表 [(lon1, lat1), (lon2, lat2), ...]
        waterway_boundary: 航道边界的点坐标列表，如果提供则用于生成测试数据
        use_fitted_curve: 是否使用拟合曲线进行投影计算，默认False使用原始线段
        """
        self.KNOTS_TO_MS = 0.514444
        self.R_EARTH = 6371000
        self.tcpa_threshold = tcpa_threshold
        # 处理航道中心线
        self.waterway_points = waterway_points
        self.use_fitted_curve = use_fitted_curve

        # 只有在需要时才创建拟合函数
        if use_fitted_curve:
            self.waterway_function = self._fit_waterway_curve()
            self.waterway_length = self._calculate_waterway_length_fitted()
        else:
            self.waterway_function = None
            self.waterway_length = self._calculate_waterway_length_original()

        # 处理航道边界
        self.waterway_boundary = waterway_boundary
        self.waterway_polygon = self._create_waterway_polygon() if waterway_boundary else None

    def _fit_waterway_curve(self) -> Dict:
        """
        将航道中心线点拟合成连续的参数化曲线
        """
        if len(self.waterway_points) < 2:
            raise ValueError("航道中心线至少需要2个点")

        # 提取经纬度
        lons = [point[0] for point in self.waterway_points]
        lats = [point[1] for point in self.waterway_points]

        # 计算累积弧长作为参数
        distances = [0]
        for i in range(1, len(self.waterway_points)):
            dist = self._calculate_distance_between_points(
                self.waterway_points[i - 1], self.waterway_points[i]
            )
            distances.append(distances[-1] + dist)

        # 归一化到[0, 1]
        max_distance = distances[-1]
        if max_distance == 0:
            raise ValueError("航道中心线点重合")

        normalized_distances = [d / max_distance for d in distances]

        # 创建插值函数

        lon_func = interp1d(normalized_distances, lons, kind='linear')
        lat_func = interp1d(normalized_distances, lats, kind='linear')

        return {
            'lon_func': lon_func,
            'lat_func': lat_func,
            'total_length': max_distance,
            'normalized_distances': normalized_distances
        }

    def _create_waterway_polygon(self) -> Optional[Polygon]:
        """
        根据航道边界点创建多边形区域
        """
        if not self.waterway_boundary or len(self.waterway_boundary) < 3:
            return None

        try:
            points = self.waterway_boundary
            if points[0] != points[-1]:
                points.append(points[0])
            # poly = Polygon(points)
            return Polygon(points, alpha=0.3, facecolor='lightblue', edgecolor='blue')
        except Exception as e:
            # 这里可以选择直接抛出错误，或者返回 None 或一个自定义错误信息
            print(f"Error creating waterway polygon: {e}")
            raise  # 直接抛出异常

    def _point_in_polygon(self, point: Tuple[float, float]) -> bool:
        """
        判断点是否在航道多边形内
        """
        if not self.waterway_boundary:
            return True

        x, y = point
        n = len(self.waterway_boundary)
        inside = False

        p1x, p1y = self.waterway_boundary[0]
        for i in range(1, n + 1):
            p2x, p2y = self.waterway_boundary[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _calculate_distance_between_points(self, point1: Tuple[float, float],
                                           point2: Tuple[float, float]) -> float:
        """计算两点间距离（米）"""
        lon1, lat1 = point1
        lon2, lat2 = point2

        d_lon = math.radians(lon2 - lon1)
        d_lat = math.radians(lat2 - lat1)
        lat1_rad = math.radians(lat1)

        dx = d_lon * self.R_EARTH * math.cos(lat1_rad)
        dy = d_lat * self.R_EARTH
        return math.sqrt(dx ** 2 + dy ** 2)

    def _calculate_waterway_length_fitted(self) -> float:
        """计算拟合航道总长度"""
        return self.waterway_function['total_length']

    def _calculate_waterway_length_original(self) -> float:
        """计算原始航道总长度"""
        total_length = 0
        for i in range(len(self.waterway_points) - 1):
            total_length += self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )
        return total_length

    def _project_point_to_segment(self, point: Tuple[float, float],
                                  seg_start: Tuple[float, float],
                                  seg_end: Tuple[float, float]) -> Dict:
        """
        将点投影到线段上
        """
        px, py = point
        x1, y1 = seg_start
        x2, y2 = seg_end

        # 线段向量
        dx = x2 - x1
        dy = y2 - y1

        if dx == 0 and dy == 0:
            # 线段退化为点
            distance = self._calculate_distance_between_points(point, seg_start)
            return {
                'projection_point': seg_start,
                'distance': distance,
                'parameter': 0
            }

        # 计算投影参数
        t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)

        # 限制在线段范围内
        t = max(0, min(1, t))

        # 计算投影点
        proj_x = x1 + t * dx
        proj_y = y1 + t * dy

        # 计算距离
        distance = self._calculate_distance_between_points(point, (proj_x, proj_y))

        return {
            'projection_point': (proj_x, proj_y),
            'distance': distance,
            'parameter': t
        }

    def _project_ship_to_waterway_original(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到原始航道中心线上（使用线段）
        """
        ship_lon, ship_lat = ship['lon'], ship['lat']

        min_distance = float('inf')
        best_projection = None
        best_segment = 0
        best_parameter = 0

        total_length = 0
        segment_lengths = []

        # 计算各线段长度
        for i in range(len(self.waterway_points) - 1):
            segment_length = self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )
            segment_lengths.append(segment_length)
            total_length += segment_length

        cumulative_length = 0

        # 对每个线段进行投影
        for i in range(len(self.waterway_points) - 1):
            p1 = self.waterway_points[i]
            p2 = self.waterway_points[i + 1]

            # 计算点到线段的投影
            projection_info = self._project_point_to_segment(
                (ship_lon, ship_lat), p1, p2
            )

            if projection_info['distance'] < min_distance:
                min_distance = projection_info['distance']
                best_projection = projection_info['projection_point']
                best_segment = i

                # 计算全局参数
                segment_parameter = projection_info['parameter']
                best_parameter = (cumulative_length + segment_parameter * segment_lengths[i]) / total_length

            cumulative_length += segment_lengths[i]

        return {
            'parameter': best_parameter,
            'distance_to_centerline': min_distance,
            'projection_point': best_projection,
            'segment_index': best_segment
        }

    def _project_ship_to_waterway_fitted(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到拟合的航道中心线上
        """
        ship_lon, ship_lat = ship['lon'], ship['lat']

        # 定义目标函数：船舶到航道中心线上某点的距离
        def distance_to_centerline(t):
            if t < 0:
                t = 0
            elif t > 1:
                t = 1

            try:
                centerline_lon = float(self.waterway_function['lon_func'](t))
                centerline_lat = float(self.waterway_function['lat_func'](t))
                return self._calculate_distance_between_points(
                    (ship_lon, ship_lat), (centerline_lon, centerline_lat)
                )
            except:
                return float('inf')

        # 使用优化算法找到最近点
        result = minimize_scalar(distance_to_centerline, bounds=(0, 1), method='bounded')

        optimal_t = result.x
        min_distance = result.fun

        # 计算投影点坐标
        projection_lon = float(self.waterway_function['lon_func'](optimal_t))
        projection_lat = float(self.waterway_function['lat_func'](optimal_t))

        return {
            'parameter': optimal_t,
            'distance_to_centerline': min_distance,
            'projection_point': (projection_lon, projection_lat)
        }

    def _project_ship_to_waterway(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到航道中心线上
        根据use_fitted_curve参数选择使用原始线段还是拟合曲线
        """
        if self.use_fitted_curve:
            return self._project_ship_to_waterway_fitted(ship)
        else:
            return self._project_ship_to_waterway_original(ship)

    def _sort_ships_by_waterway_position(self, ships: List[Dict]) -> List[Dict]:
        """
        根据船舶在航道中心线上的投影位置对船舶进行排序
        """
        ships_with_projection = []

        for ship in ships:
            projection_info = self._project_ship_to_waterway(ship)
            ship_with_projection = ship.copy()
            ship_with_projection.update(projection_info)
            ships_with_projection.append(ship_with_projection)

        # 按参数位置排序
        ships_with_projection.sort(key=lambda x: x['parameter'])

        return ships_with_projection

    def _calculate_tcpa(self, ship1: Dict, ship2: Dict) -> float:
        """计算两船的TCPA"""
        # 坐标转换
        d_lon = math.radians(ship2['lon'] - ship1['lon'])
        d_lat = math.radians(ship2['lat'] - ship1['lat'])
        lat1_rad = math.radians(ship1['lat'])

        px = d_lon * self.R_EARTH * math.cos(lat1_rad)
        py = d_lat * self.R_EARTH

        # 速度向量转换
        sog1_ms = ship1['sog'] * self.KNOTS_TO_MS
        sog2_ms = ship2['sog'] * self.KNOTS_TO_MS

        cog1_rad = math.radians(ship1['cog'])
        cog2_rad = math.radians(ship2['cog'])

        v1x = sog1_ms * math.sin(cog1_rad)
        v1y = sog1_ms * math.cos(cog1_rad)
        v2x = sog2_ms * math.sin(cog2_rad)
        v2y = sog2_ms * math.cos(cog2_rad)

        # 相对速度
        vrx = v2x - v1x
        vry = v2y - v1y
        vr_sq = vrx ** 2 + vry ** 2

        if vr_sq == 0:
            return float('inf')  # 相对静止

        # TCPA计算
        dot_product = px * vrx + py * vry
        tcpa = -dot_product / vr_sq

        return tcpa

    def _get_waterway_direction_at_parameter(self, parameter: float) -> float:
        """
        获取航道在指定参数位置处的方向
        """
        if self.use_fitted_curve and self.waterway_function:
            # 使用拟合曲线计算方向
            dt = 0.01
            t1 = max(0, min(1, parameter - dt / 2))
            t2 = max(0, min(1, parameter + dt / 2))

            try:
                lon1 = float(self.waterway_function['lon_func'](t1))
                lat1 = float(self.waterway_function['lat_func'](t1))
                lon2 = float(self.waterway_function['lon_func'](t2))
                lat2 = float(self.waterway_function['lat_func'](t2))

                direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
                if direction < 0:
                    direction += 360
                return direction
            except:
                pass

        # 使用原始线段计算方向
        # 找到参数对应的线段
        cumulative_length = 0
        total_length = self._calculate_waterway_length_original()
        target_length = parameter * total_length

        for i in range(len(self.waterway_points) - 1):
            segment_length = self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )

            if cumulative_length + segment_length >= target_length:
                # 在这个线段上
                lon1, lat1 = self.waterway_points[i]
                lon2, lat2 = self.waterway_points[i + 1]

                direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
                if direction < 0:
                    direction += 360
                return direction

            cumulative_length += segment_length

        # 如果到达末尾，使用最后一个线段的方向
        if len(self.waterway_points) >= 2:
            lon1, lat1 = self.waterway_points[-2]
            lon2, lat2 = self.waterway_points[-1]
            direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
            if direction < 0:
                direction += 360
            return direction

        return 0  # 默认值

    def _is_ship_moving_along_waterway(self, ship: Dict, tolerance_angle: float = 45.0) -> bool:
        """
        判断船舶是否沿着航道方向航行
        """
        # 获取航道在投影点处的方向
        waterway_direction = self._get_waterway_direction_at_parameter(ship['parameter'])

        # 计算船舶航向与航道方向的夹角
        ship_heading = ship['cog']
        angle_diff = abs(ship_heading - waterway_direction)
        angle_diff = min(angle_diff, 360 - angle_diff)  # 取较小的角度

        # 也考虑反向航行的情况
        reverse_angle_diff = abs(angle_diff - 180)

        return min(angle_diff, reverse_angle_diff) <= tolerance_angle

    def detect_encounters_efficient(self, ships: List[Dict]) -> Dict[int, List[int]]:
        """
        高效的会遇检测方法
        """
        if not ships:
            return {}

        # 1. 对船舶按航道位置排序
        sorted_ships = self._sort_ships_by_waterway_position(ships)

        # 2. 过滤掉不沿航道航行的船舶（可选）
        waterway_ships = [ship for ship in sorted_ships
                          if self._is_ship_moving_along_waterway(ship)]

        encounter_results = {ship['id']: [] for ship in ships}

        # 3. 只检查相邻船舶的会遇关系
        for i in range(len(waterway_ships)):
            current_ship = waterway_ships[i]
            # 检查前一艘船（如果存在）
            if i > 0:
                prev_ship = waterway_ships[i - 1]
                if 0 < self._calculate_tcpa(current_ship, prev_ship) <=  self.tcpa_threshold:
                    encounter_results[current_ship['id']].append(prev_ship['id'])

            # 检查后一艘船（如果存在）
            if i < len(waterway_ships) - 1:
                next_ship = waterway_ships[i + 1]
                if 0< self._calculate_tcpa(current_ship, next_ship) <=  self.tcpa_threshold:
                    encounter_results[current_ship['id']].append(next_ship['id'])

        return encounter_results

    def get_ship_positions_on_waterway(self, ships: List[Dict]) -> List[Dict]:
        """
        获取所有船舶在航道上的位置信息
        """
        return self._sort_ships_by_waterway_position(ships)

    def generate_ships_in_waterway(self, num_ships: int = 10,
                                   speed_range: Tuple[float, float] = (6, 12)) -> List[Dict]:
        """
        在航道区域内生成测试船舶
        """
        if not self.waterway_boundary:
            return self._generate_ships_near_centerline(num_ships, speed_range)

        ships = []
        attempts = 0
        max_attempts = num_ships * 10

        # 计算航道边界的范围
        lons = [point[0] for point in self.waterway_boundary]
        lats = [point[1] for point in self.waterway_boundary]
        min_lon, max_lon = min(lons), max(lons)
        min_lat, max_lat = min(lats), max(lats)

        ship_id = 1
        while len(ships) < num_ships and attempts < max_attempts:
            attempts += 1

            # 在边界矩形内随机生成点
            lon = random.uniform(min_lon, max_lon)
            lat = random.uniform(min_lat, max_lat)

            # 检查是否在航道多边形内
            if self._point_in_polygon((lon, lat)):
                # 计算该位置在航道中心线上的投影，以确定航道方向
                temp_ship = {'lon': lon, 'lat': lat}
                projection_info = self._project_ship_to_waterway(temp_ship)

                # 获取航道方向
                waterway_direction = self._get_waterway_direction_at_parameter(projection_info['parameter'])

                # 船舶航向沿着航道方向或相反方向
                if random.random() < 0.7:  # 70%概率沿航道方向
                    base_cog = waterway_direction
                else:  # 30%概率反向
                    base_cog = (waterway_direction + 180) % 360

                # 添加随机偏差
                cog = (base_cog + random.uniform(-20, 20)) % 360

                ship = {
                    'id': ship_id,
                    'lon': lon,
                    'lat': lat,
                    'sog': random.uniform(speed_range[0], speed_range[1]),
                    'cog': cog
                }
                ships.append(ship)
                ship_id += 1

        return ships

    def _generate_ships_near_centerline(self, num_ships: int,
                                        speed_range: Tuple[float, float]) -> List[Dict]:
        """
        在航道中心线附近生成船舶（当没有边界时使用）
        """
        ships = []

        for i in range(num_ships):
            # 随机选择航道中心线上的一个位置
            segment_idx = random.randint(0, len(self.waterway_points) - 2)
            t = random.uniform(0, 1)

            # 在线段上插值
            lon1, lat1 = self.waterway_points[segment_idx]
            lon2, lat2 = self.waterway_points[segment_idx + 1]

            center_lon = lon1 + t * (lon2 - lon1)
            center_lat = lat1 + t * (lat2 - lat1)

            # 添加随机偏移
            lon = center_lon + random.uniform(-0.002, 0.002)
            lat = center_lat + random.uniform(-0.002, 0.002)

            # 计算航道方向
            waterway_direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
            if waterway_direction < 0:
                waterway_direction += 360

            # 船舶航向
            if random.random() < 0.7:
                base_cog = waterway_direction
            else:
                base_cog = (waterway_direction + 180) % 360

            cog = (base_cog + random.uniform(-20, 20)) % 360

            ship = {
                'id': i + 1,
                'lon': lon,
                'lat': lat,
                'sog': random.uniform(speed_range[0], speed_range[1]),
                'cog': cog
            }
            ships.append(ship)

        return ships

    def _draw_ship_arrow(self, ax, ship: Dict, color: str, scale: float = 0.0015):
        """绘制船舶位置和航向箭头"""
        lon, lat = ship['lon'], ship['lat']
        cog, sog = ship['cog'], ship['sog']

        # 绘制船舶位置点
        ax.scatter(lon, lat, c=color, s=12, zorder=5, edgecolors='black', linewidth=1.5)

        # 绘制船舶ID
        ax.annotate(f"{ship['id']}", (lon, lat), xytext=(6, 6),
                    textcoords='offset points', fontsize=9, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.8))

        # 计算箭头终点（箭头长度与速度成比例）
        arrow_length = sog * scale
        cog_rad = math.radians(cog)
        end_lon = lon + arrow_length * math.sin(cog_rad)
        end_lat = lat + arrow_length * math.cos(cog_rad)

        # 绘制航向箭头
        arrow = FancyArrowPatch((lon, lat), (end_lon, end_lat),
                                arrowstyle='->', mutation_scale=2,
                                color=color, linewidth=1, zorder=1)
        ax.add_patch(arrow)

    def visualize_enhanced_waterway_and_ships(self, ships: List[Dict],
                                              encounters: Optional[Dict[int, List[int]]] = None,
                                              show_tcpa: bool = True):
        """
        增强版可视化航道中心线和船舶位置
        """
        # fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
        fig, (ax1) = plt.subplots(1, 1, figsize=(12, 8))
        # 左图：显示实际位置
        ax1.set_title('航道实际位置图', fontsize=16, fontweight='bold')

        # 绘制航道边界（如果有）
        if self.waterway_polygon:
            ax1.add_patch(self.waterway_polygon)

        # 绘制航道中心线
        waterway_lons = [point[0] for point in self.waterway_points]
        waterway_lats = [point[1] for point in self.waterway_points]
        ax1.plot(waterway_lons, waterway_lats, linewidth=2,
                 label='航道中心线', zorder=3)
        # 
        # # 获取船舶投影信息
        ships_with_projection = self.get_ship_positions_on_waterway(ships)

        # 绘制船舶和投影
        for ship in ships_with_projection:
            # 确定船舶颜色
            has_encounter = encounters and encounters.get(ship['id'], [])
            color = 'red' if has_encounter else 'blue'

            # 绘制船舶位置和航向箭头
            self._draw_ship_arrow(ax1, ship, color)

            # # 绘制投影点
            # proj_lon, proj_lat = ship['projection_point']
            # ax1.scatter(proj_lon, proj_lat, c=color, s=80, marker='x',
            #             zorder=4, linewidth=3)
            # 
            # # 绘制到投影点的连接线
            # ax1.plot([ship['lon'], proj_lon], [ship['lat'], proj_lat],
            #          color=color, alpha=0.6, linestyle=':', linewidth=2)

        # 绘制会遇关系线
        if encounters:
            for ship in ships_with_projection:
                ship_id = ship['id']
                for encounter_id in encounters.get(ship_id, []):
                    encounter_ship = next((s for s in ships_with_projection
                                           if s['id'] == encounter_id), None)
                    if encounter_ship:
                        # 绘制会遇关系线
                        ax1.plot([ship['lon'], encounter_ship['lon']],
                                 [ship['lat'], encounter_ship['lat']],
                                 'purple', alpha=0.8, linewidth=1.5, linestyle='--',
                                 zorder=1)

                        # 显示TCPA值（如果启用）
                        if show_tcpa:
                            tcpa = self._calculate_tcpa(ship, encounter_ship)
                            mid_lon = (ship['lon'] + encounter_ship['lon']) / 2
                            mid_lat = (ship['lat'] + encounter_ship['lat']) / 2
                            ax1.annotate(f"TCPA: {tcpa:.1f}s", (mid_lon, mid_lat),
                                         xytext=(0, 0), textcoords='offset points',
                                         fontsize=8, ha='center', va='center',
                                         bbox=dict(boxstyle='round,pad=0.3',
                                                   facecolor='orange', alpha=0.9))

        ax1.set_xlabel('经度 (度)', fontsize=12)
        ax1.set_ylabel('纬度 (度)', fontsize=12)
        ax1.legend(loc='upper right', fontsize=10)
        ax1.grid(True, alpha=0.3)
        ax1.set_aspect('equal')

        # # 右图：显示航道位置排序
        # ax2.set_title('航道位置排序图', fontsize=16, fontweight='bold')
        # 
        # # 按航道位置排序
        # sorted_ships = sorted(ships_with_projection, key=lambda x: x['parameter'])
        # 
        # positions = [ship['parameter'] for ship in sorted_ships]
        # ship_ids = [ship['id'] for ship in sorted_ships]
        # colors = ['red' if encounters and encounters.get(ship['id'], []) else 'blue'
        #           for ship in sorted_ships]
        # 
        # # 绘制船舶在航道上的位置
        # ax2.scatter(positions, [0] * len(positions), c=colors, s=150, zorder=4,
        #             edgecolors='black', linewidth=2)
        # 
        # # 添加船舶ID和详细信息
        # for i, (pos, ship_id, ship) in enumerate(zip(positions, ship_ids, sorted_ships)):
        #     ax2.annotate(f"船舶{ship_id}", (pos, 0), xytext=(0, 20),
        #                  textcoords='offset points', fontsize=11, ha='center',
        #                  fontweight='bold')
        #     ax2.annotate(f"{ship['sog']:.1f}kn\n{ship['cog']:.0f}°",
        #                  (pos, 0), xytext=(0, -25),
        #                  textcoords='offset points', fontsize=9, ha='center',
        #                  bbox=dict(boxstyle='round,pad=0.2', facecolor='lightgray', alpha=0.8))
        # 
        # # 绘制会遇关系
        # if encounters:
        #     for ship in sorted_ships:
        #         ship_pos = ship['parameter']
        #         ship_id = ship['id']
        #         for encounter_id in encounters.get(ship_id, []):
        #             encounter_ship = next((s for s in sorted_ships if s['id'] == encounter_id), None)
        #             if encounter_ship:
        #                 encounter_pos = encounter_ship['parameter']
        #                 ax2.plot([ship_pos, encounter_pos], [0, 0],
        #                          'purple', alpha=0.8, linewidth=5, zorder=3)
        # 
        # ax2.set_xlabel('航道位置参数 (0=起点, 1=终点)', fontsize=12)
        # ax2.set_ylabel('')
        # ax2.set_ylim(-0.8, 0.8)
        # ax2.grid(True, alpha=0.3)
        # ax2.set_yticks([])
        # 
        # # 添加图例
        # legend_elements = [
        #     plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
        #                markersize=10, label='有会遇趋势的船舶'),
        #     plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue',
        #                markersize=10, label='无会遇趋势的船舶'),
        #     plt.Line2D([0], [0], color='purple', linestyle='--', linewidth=3,
        #                label='会遇关系'),
        # ]

        # if self.waterway_boundary:
        #     legend_elements.append(plt.Rectangle((0, 0), 1, 1, facecolor='lightblue',
        #                                          alpha=0.3, label='航道区域'))
        # 
        # ax1.legend(handles=legend_elements, loc='upper left', fontsize=10)

        # # 添加投影方法说明
        # method_text = f"投影方法: {'拟合曲线' if self.use_fitted_curve else '原始线段'}"
        # ax1.text(0.02, 0.02, method_text, transform=ax1.transAxes, fontsize=10,
        #          bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
        # 
        # plt.tight_layout()
        plt.show()

#%%
# from methods.Cross_scene_extraction import CrossSceneExtraction
import pickle
# 场景提取器初始化
cse = CrossSceneExtraction(trajectory_list, tras_df, geo_information)

# own_ship_list = cse.cross_owns_indentify()
# with open('data/own_ship_list.pkl', 'wb') as f:
#     pickle.dump(own_ship_list, f)

with open('data/own_ship_list.pkl', 'rb') as f:
    own_ship_list = pickle.load(f)   
test_ships_list = cse.alls_indentify(own_ship_list)
test_ships = test_ships_list[0]

# id 映射
unique_ids = []
for item in test_ships:
    if item["id"] not in unique_ids:
        unique_ids.append(item["id"])
id_map = {old_id: new_id for new_id, old_id in enumerate(unique_ids, start=1)}
for item in test_ships:
    item["id"] = id_map[item["id"]]

#%%
# 船舶会遇态势检测器初始化
centerline, boundary = geo_information['channel_centerline'], geo_information['channel_boundary']
detector = EnhancedWaterwayEncounterDetector(centerline, boundary, 300, use_fitted_curve=False)
# 执行会遇检测
encounters = detector.detect_encounters_efficient(test_ships)
# 
# # 显示船舶位置信息
# ships_with_projection = detector.get_ship_positions_on_waterway(test_ships)

# print("\n船舶在航道上的位置排序:")
# for ship in ships_with_projection:
#     in_waterway = detector._is_ship_moving_along_waterway(ship)
#     print(f"船舶{ship['id']}: 位置参数={ship['parameter']:.3f}, "
#           f"到中心线距离={ship['distance_to_centerline']:.1f}m, "
#           f"航向={ship['cog']:.0f}°, 速度={ship['sog']:.1f}节, "
#           f"沿航道航行={'是' if in_waterway else '否'}")

print("\n会遇检测结果:")
encounter_count = 0
for ship_id, encounter_list in encounters.items():
    if encounter_list:
        ship = next(s for s in test_ships if s['id'] == ship_id)
        print(f"船舶{
        ship_id} (航向:{ship['cog']:.0f}°, 速度:{ship['sog']:.1f}节) "
              f"与船舶{encounter_list}存在会遇趋势")
        encounter_count += 1
# if encounter_count == 0:
#     print("未检测到会遇趋势")
# else:
#     print(f"\n总计{encounter_count}艘船舶存在会遇趋势")
# 增强版可视化
detector.visualize_enhanced_waterway_and_ships(test_ships, encounters, show_tcpa=False)