#%%
import pickle
import pandas as pd

trajectory_list = pickle.load(open("data/2024_1_6_inter.pkl", "rb"))
# tras_df = pd.concat(trajectory_list)
# tras_df.to_parquet('data/tras_2024_3_inter.parquet')
tras_df = pd.read_parquet('data/tras_2024_1_6_inter.parquet')
#%%
trajectory_list[0]
#%%
tras_df['Type'].value_counts()
#%%
import numpy as np
from shapely.geometry import Polygon
from skimage.morphology import skeletonize
from skimage.draw import polygon as sk_polygon
import networkx as nx

"----------------------------------------------------------------------------------------------------"
channel_polygon = [
    (121.073141, 31.764529), (121.064957, 31.755378), (121.063715, 31.753956), (121.072114, 31.750114),  # 修正此处
    (121.077344, 31.746898), (121.080174, 31.745144), (121.085449, 31.741783), (121.088838, 31.739957),
    (121.092785, 31.737365), (121.095359, 31.735868), (121.096089, 31.735357), (121.096947, 31.734737),
    (121.097977, 31.733751), (121.100553, 31.731416), (121.106692, 31.72576), (121.110385, 31.72233),
    (121.116613, 31.716677), (121.118461, 31.715327), (121.119363, 31.714489), (121.124218, 31.710441),
    (121.126882, 31.708473), (121.129633, 31.706176), (121.134146, 31.702567), (121.135436, 31.701474),
    (121.136295, 31.700781), (121.168588, 31.682797), (121.182048, 31.675445), (121.184069, 31.673768),
    (121.246666, 31.62809), (121.256662, 31.62045), (121.266311, 31.611307), (121.269998, 31.607686),
    (121.274156, 31.603881), (121.277242, 31.600405), (121.280284, 31.596892), (121.286412, 31.590085),
    (121.293995, 31.580825), (121.297593, 31.575884), (121.305731, 31.564864), (121.309029, 31.561202),
    (121.322732, 31.546549), (121.333179, 31.554428), (121.319005, 31.569337), (121.316692, 31.569337),
    (121.311553, 31.57904), (121.305215, 31.588373), (121.302645, 31.592143), (121.277197, 31.615536),
    (121.263908, 31.627535), (121.223739, 31.656763), (121.20767, 31.667851), (121.198042, 31.674558),
    (121.188755, 31.680898), (121.172156, 31.691314), (121.164932, 31.69539), (121.151342, 31.703618),
    (121.145839, 31.70631), (121.13767, 31.714038), (121.119877, 31.730735), (121.110255, 31.740291),
    (121.10493, 31.745106), (121.104157, 31.746274)]

"----------------------------------------------------------------------------------------------------"
polygon_points = channel_polygon.copy()
if polygon_points[0] != polygon_points[-1]:
    polygon_points.append(polygon_points[0])
poly = Polygon(polygon_points)
minx, miny, maxx, maxy = poly.bounds

# --- 1. 区域离散化为二值图 ---
scale = 5000  # 精度（可根据区域大小微调）
img_shape = (int((maxy - miny) * scale) + 5, int((maxx - minx) * scale) + 5)
img = np.zeros(img_shape, dtype=np.uint8)
poly_x = [(pt[0] - minx) * scale for pt in polygon_points]
poly_y = [(pt[1] - miny) * scale for pt in polygon_points]
rr, cc = sk_polygon(poly_y, poly_x, img.shape)
img[rr, cc] = 1

# --- 2. 骨架化 ---
skeleton = skeletonize(img)

# --- 3. 骨架像素转无向图 ---
skel_points = np.array(np.nonzero(skeleton)).T
G = nx.Graph()
for y, x in skel_points:
    for dy, dx in [(-1, 0), (1, 0), (0, -1), (0, 1), (-1, -1), (-1, 1), (1, -1), (1, 1)]:
        ny, nx_ = y + dy, x + dx
        if 0 <= ny < skeleton.shape[0] and 0 <= nx_ < skeleton.shape[1]:
            if skeleton[ny, nx_]:
                G.add_edge((y, x), (ny, nx_))

# --- 4. 提取所有端点，两两计算最长路径 ---
deg = dict(G.degree)
end_points = [pt for pt, d in deg.items() if d == 1]
max_len = 0
main_path = []
for i in range(len(end_points)):
    for j in range(i + 1, len(end_points)):
        try:
            path = nx.shortest_path(G, source=end_points[i], target=end_points[j])
            if len(path) > max_len:
                max_len = len(path)
                main_path = path
        except nx.NetworkXNoPath:
            continue

main_path = np.array(main_path)
centerline_lon = main_path[:, 1] / scale + minx
centerline_lat = main_path[:, 0] / scale + miny

centerline_lon = centerline_lon[60:-60]
centerline_lat = centerline_lat[60:-60]
lon_head, lat_head = 121.327805, 31.550480
lon_tail, lat_tail = 121.068438, 31.759278

centerline_lon = np.insert(centerline_lon, 0, lon_head)
centerline_lon = np.append(centerline_lon, lon_tail)
centerline_lat = np.insert(centerline_lat, 0, lat_head)
centerline_lat = np.append(centerline_lat, lat_tail)
np.save('centerline_lon.npy', centerline_lon)
np.save('centerline_lat.npy', centerline_lat)
"----------------------------------------------------------------------------------------------------"
# 北，南
channel_side1 = [(121.073956, 31.76409), (121.104844, 31.746274), (121.145065, 31.707185), (121.194259, 31.677474),
                 (121.259191, 31.631338), (121.303073, 31.592106), (121.317463, 31.570729), (121.333393, 31.554318)]
channel_side2 = [(121.064358, 31.753882), (121.096947, 31.735175), (121.130965, 31.704827), (121.137499, 31.700454),
                 (121.18394, 31.674243), (121.257133, 31.620523), (121.274627, 31.603844), (121.292795, 31.581595),
                 (121.310613, 31.558457), (121.322603, 31.546147)]

"----------------------------------------------------------------------------------------------------"
anchorage = [(121.109654, 31.741313), (121.120992, 31.75709), (121.151768, 31.753632), (121.159852, 31.752771),
             (121.18875, 31.741582),
             (121.222102, 31.711983), (121.238077, 31.690958), (121.254899, 31.667728), (121.269481, 31.637616),
             (121.257819, 31.63207),
             (121.233616, 31.650339), (121.208014, 31.667705), (121.181359, 31.686072), (121.146613, 31.705873)]
#%%
# 提取穿越船
from shapely.geometry import Polygon, LineString, Point
from tqdm import tqdm
import pickle

def find_cross_points(track_line, side_edges):
    # """返回轨迹线与一组边的所有交点"""
    # cross_points = []
    # for i in range(len(side_edges) - 1):
    #     edge = LineString([side_edges[i], side_edges[i + 1]])
    #     inter = track_line.intersection(edge)
    #     if not inter.is_empty:
    #         if inter.geom_type == 'Point':
    #             cross_points.append(inter)
    #         elif inter.geom_type == 'MultiPoint':
    #             cross_points.extend(list(inter.geoms))
    #         elif inter.geom_type == 'LineString':
    #             # 处理线段重叠情况，取端点
    #             cross_points.extend([Point(inter.coords[0]), Point(inter.coords[-1])])
    #         elif inter.geom_type == 'GeometryCollection':
    #             # 处理所有几何类型
    #             for g in inter.geoms:
    #                 if g.geom_type == 'Point':
    #                     cross_points.append(g)
    #                 elif g.geom_type == 'LineString':
    #                     cross_points.extend([Point(g.coords[0]), Point(g.coords[-1])])
    # 
    # # 去除重复点（距离小于1米认为是同一点）
    # unique_points = []
    # for pt in cross_points:
    #     is_duplicate = False
    #     for existing_pt in unique_points:
    #         if pt.distance(existing_pt) < 1.0:  # 1米容差
    #             is_duplicate = True
    #             break
    #     if not is_duplicate:
    #         unique_points.append(pt)
    # 
    # return unique_points
    # 创建完整的边界线
    boundary_line = LineString(side_edges)
    
    # 直接计算交点
    intersection = track_line.intersection(boundary_line)
    
    # 处理交点
    cross_points = []
    if not intersection.is_empty:
        if intersection.geom_type == 'Point':
            cross_points.append(intersection)
        elif intersection.geom_type == 'MultiPoint':
            cross_points.extend(list(intersection.geoms))
    
    return cross_points

def is_channel_truly_crossed(track_points, channel_side1, channel_side2, consider_direction=True):
    """判断轨迹是否先穿过side1再穿过side2"""
    track_line = LineString(track_points)
    cross1 = find_cross_points(track_line, channel_side1)
    cross2 = find_cross_points(track_line, channel_side2)

    # 必须两条线都有交点
    if not cross1 or not cross2:
        return False

    # 找出所有交点对应的轨迹索引
    idx1_list = []
    for pt in cross1:
        min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
        idx1_list.append(min_idx)

    idx2_list = []
    for pt in cross2:
        min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
        idx2_list.append(min_idx)

    # 只考虑有方向的情况
    if consider_direction:
        # 找到最早的side1交点
        first_side1_idx = min(idx1_list)
        # 检查是否存在在这个side1交点之后的side2交点
        later_side2_indices = [idx for idx in idx2_list if idx > first_side1_idx]
        return len(later_side2_indices) > 0
    else:
        # 如果不考虑方向，只要两条线都穿过就行
        return True


def is_in_area(track_points, polygon_points):
    poly = Polygon(polygon_points)
    pt = track_points[0]
    if poly.contains(Point(pt)):
        return True
    else:
        return False


own_ship_list = []
for k, tra in enumerate(tqdm(trajectory_list)):
    track = [(Lon, lat) for Lon, lat in zip(tra['Lon'], tra['Lat'])]
    if len(track) < 2:
        continue  # 跳过点数不足2的轨迹
    if (is_channel_truly_crossed(track, channel_side1, channel_side2, consider_direction=True)
            & is_in_area(track, anchorage)):
    # if (is_channel_truly_crossed(track, channel_side1, channel_side2, consider_direction=True)
    #        ):
        own_ship_list.append(k)

# with open('own_ship_list_3.pkl', 'wb') as f:
#     pickle.dump(own_ship_list, f)
#%%
len(own_ship_list)
#%%
len(own_ship_list)
#%%
from tqdm import tqdm
import random

import pickle
with open('own_ship_list_3.pkl', 'rb') as f:
    own_ship_list = pickle.load(f)
    
def all_in_area(track_points, polygon_points):
    poly = Polygon(polygon_points)
    for pt in track_points:
        if not poly.contains(Point(pt)):
            return False
    return True


idx = random.randrange(len(own_ship_list))
own_ship = trajectory_list[own_ship_list[idx]]
ts, te = own_ship['PosTime'].values[0], own_ship['PosTime'].values[-1]
print(own_ship_list[idx], ts, te)

other_ships = tras_df.loc[(tras_df['PosTime'] >= ts) & (tras_df['PosTime'] <= te)]
other_ships_list = []
for ms in tqdm(other_ships['MMSI'].unique()):
    data_ms = other_ships.loc[other_ships['MMSI'] == ms].sort_values(by="PosTime", ascending=True)
    data_ms = data_ms[['MMSI', 'PosTime', 'Lon', 'Lat', 'Sog', 'Cog', 'Length', 'Width', 'Type']]
    data_ms.reset_index(drop=True, inplace=True)

    # 直接在原数据上计算时间差和标记拆分点（间隔大于5分钟）
    data_ms['TimeDiff'] = data_ms['PosTime'].diff()
    data_ms['SplitPoint'] = data_ms['TimeDiff'] >= 5 * 60
    data_ms['SegmentID'] = data_ms['SplitPoint'].cumsum()
    # 对每个轨迹段进行处理
    grouped = data_ms.groupby('SegmentID')
    for _, group in grouped:
        # duration = group['PosTime'].iloc[-1] - group['PosTime'].iloc[0]
        # if (duration >= 15 * 60) & (len(group) >= 30):  # 持续时间超过15分钟
        tra_segment = group
        track1 = [(Lon, lat) for Lon, lat in zip(tra_segment['Lon'], tra_segment['Lat'])]
        if all_in_area(track1, channel_polygon):
            tra_segment = tra_segment.drop(['TimeDiff', 'SplitPoint', 'SegmentID'], axis=1)
            tra_segment = tra_segment.reset_index(drop=True)
            other_ships_list.append(tra_segment)
#%%
import numpy as np
from scipy.interpolate import splprep, splev
from PIL import Image
from matplotlib import pyplot as plt
from matplotlib.ticker import FormatStrFormatter, MultipleLocator

colors = plt.cm.tab10(np.linspace(0, 1, len(other_ships_list)))


def line_smooth(x, y):
    # 使用参数样条拟合，s为平滑因子，数值越大越平滑
    tck, u = splprep([x, y], s=0.000001)  # s 可调，0表示通过所有点
    unew = np.linspace(0, 1, 300)  # 输出300个平滑点
    out = splev(unew, tck)
    return out


plt.rcParams['font.family'] = 'Times New Roman'
# plt.rcParams['font.size'] = 12  # 设置默认字体大小
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗

trajectory_list_vis = [trajectory_list[365]]

Lat = [31.516, 31.784]  # 纬度范围
Lon = [121.05, 121.35]  # 经度范围

# 加载背景底图
background_image = Image.open('data/map0.png')  # 替换为你的底图文件路径

# 创建一个新图层，合并两张图
combined_img = Image.new("RGBA", background_image.size)
combined_img.paste(background_image, (0, 0))  # 将底图粘贴到新图层

fig, ax = plt.subplots(dpi=300)
ax.imshow(combined_img, extent=[Lon[0], Lon[1], Lat[0], Lat[1]])

# 绘制本船
color = '#00CC00'
# out_ownship = line_smooth(np.array(own_ship['Lon']), np.array(own_ship['Lat']))
plt.plot(own_ship['Lon'], own_ship['Lat'], lw=0.3, color=color)
# plt.plot(out_ownship[0], out_ownship[1], lw=0.3, color=color)
if len(own_ship) >= 2:
    start_lon, start_lat = own_ship.iloc[-2][['Lon', 'Lat']]
    end_lon, end_lat = own_ship.iloc[-1][['Lon', 'Lat']]
    # 箭头方向矢量
    dx = end_lon - start_lon
    dy = end_lat - start_lat
    # 用 annotate 画箭头，箭头参数可调
    plt.annotate(
        '', xy=(end_lon, end_lat), xytext=(start_lon, start_lat),
        arrowprops=dict(arrowstyle='simple', color=color, mutation_scale=3, shrinkA=0, shrinkB=0))

# 绘制航道线
polygon = channel_polygon.copy()
# 确保闭合
if polygon[0] != polygon[-1]:
    polygon.append(polygon[0])
# 拆分为经纬度
Lon_poly = [pt[0] for pt in polygon]
Lat_poly = [pt[1] for pt in polygon]
# 填充区域（可选，推荐）
# plt.fill(Lon_poly, Lat_poly, color='#C545C3', alpha=0.15, label='Channel Area')
# 画多边形边界
plt.plot(Lon_poly, Lat_poly, color='#C545C3', ls='--', lw=1)
# 画中心线
plt.plot(centerline_lon, centerline_lat, color='#C545C3', ls='--', lw=1)

# 绘制他船
for i, traj in enumerate(tqdm(other_ships_list)):
    if not {'Lon', 'Lat', 'PosTime'}.issubset(traj.columns) or traj.empty:
        print(f"轨迹 {i} 缺少 'Lon'、'Lat' 或 'PosTime' 列，或为空，已跳过")
        continue
    color = '#FFFF66'
    # color = colors[i % len(colors)]
    # ---- 1. 线性插值到每1秒 ----
    # 保证时间升序
    traj = traj.sort_values('PosTime')
    time_arr = traj['PosTime'].values
    lon_arr = traj['Lon'].values
    lat_arr = traj['Lat'].values
    # 新的等间隔时间序列
    interp_times = np.arange(time_arr[0], time_arr[-1] + 1, 1)  # 步长1s
    interp_lon = np.interp(interp_times, time_arr, lon_arr)
    interp_lat = np.interp(interp_times, time_arr, lat_arr)
    # ---- 2. 画轨迹 ----
    plt.plot(interp_lon, interp_lat, lw=0.3, color=color)
    # ---- 3. 箭头 ----
    if len(interp_lon) >= 2:
        start_lon, start_lat = interp_lon[-2], interp_lat[-2]
        end_lon, end_lat = interp_lon[-1], interp_lat[-1]
        plt.annotate(
            '', xy=(end_lon, end_lat), xytext=(start_lon, start_lat),
            arrowprops=dict(arrowstyle='simple', color=color, mutation_scale=3, lw=0.8, shrinkA=0, shrinkB=0), )

# 添加坐标轴
ax.set_xlabel('Longitude (°)', fontsize=13, fontdict={'family': 'Times New Roman', 'weight': 'bold'})
ax.set_ylabel('Latitude (°)', fontsize=13, fontdict={'family': 'Times New Roman', 'weight': 'bold'})

# 设置网格透明度和颜色
ax.grid(True, color='gray', alpha=0.3, linestyle='--')  # 设置网格颜色为灰色，透明度为0.5

# 设置刻度朝内，四周都要有刻度
ax.tick_params(axis='both', direction='in', length=6, width=1, top=True, right=True)  # 刻度朝内，并在顶部和右侧也有刻度

# 设置框线的颜色和透明度
for spine in ax.spines.values():
    spine.set_edgecolor('black')  # 设置框线颜色为黑色
    spine.set_alpha(0.7)  # 设置框线透明度为0.7

# 调整横纵坐标的小数点位数
ax.xaxis.set_major_formatter(FormatStrFormatter('%.1f'))  # 横坐标显示三位小数
ax.yaxis.set_major_formatter(FormatStrFormatter('%.1f'))  # 纵坐标显示三位小数

# 设置横纵坐标的间隔
ax.xaxis.set_major_locator(MultipleLocator(0.1))  # 横坐标
ax.yaxis.set_major_locator(MultipleLocator(0.1))  # 纵坐标

# 设置想要显示的经纬度范围
ax.set_xlim(Lon[0], Lon[1])
ax.set_ylim(Lat[0], Lat[1])

# 添加坐标轴线
ax.axhline(0, color='black', linewidth=1)

plt.savefig('vis/test.svg', format='svg',
            bbox_inches='tight', pad_inches=0.1, dpi=300)
plt.close()
#%%

#%%

#%%
import pickle
channel_side1 = [(121.073956, 31.76409), (121.104844, 31.746274), (121.145065, 31.707185),
                              (121.194259, 31.677474), (121.259191, 31.631338), (121.303073, 31.592106),
                              (121.317463, 31.570729), (121.333393, 31.554318)]
channel_side2 = [(121.064358, 31.753882), (121.096947, 31.735175), (121.130965, 31.704827),
                      (121.137499, 31.700454), (121.18394, 31.674243), (121.257133, 31.620523),
                      (121.274627, 31.603844), (121.292795, 31.581595), (121.310613, 31.558457),
                      (121.322603, 31.546147)]

anchorage = [(121.109654, 31.741313), (121.120992, 31.75709), (121.151768, 31.753632),
                  (121.159852, 31.752771), (121.18875, 31.741582), (121.222102, 31.711983),
                  (121.238077, 31.690958), (121.254899, 31.667728), (121.269481, 31.637616),
                  (121.257819, 31.63207), (121.233616, 31.650339), (121.208014, 31.667705),
                  (121.181359, 31.686072), (121.146613, 31.705873)]
channel_boundary = [
    (121.073141, 31.764529), (121.064957, 31.755378), (121.063715, 31.753956), (121.072114, 31.750114),
    (121.077344, 31.746898), (121.080174, 31.745144), (121.085449, 31.741783), (121.088838, 31.739957),
    (121.092785, 31.737365), (121.095359, 31.735868), (121.096089, 31.735357), (121.096947, 31.734737),
    (121.097977, 31.733751), (121.100553, 31.731416), (121.106692, 31.72576), (121.110385, 31.72233),
    (121.116613, 31.716677), (121.118461, 31.715327), (121.119363, 31.714489), (121.124218, 31.710441),
    (121.126882, 31.708473), (121.129633, 31.706176), (121.134146, 31.702567), (121.135436, 31.701474),
    (121.136295, 31.700781), (121.168588, 31.682797), (121.182048, 31.675445), (121.184069, 31.673768),
    (121.246666, 31.62809), (121.256662, 31.62045), (121.266311, 31.611307), (121.269998, 31.607686),
    (121.274156, 31.603881), (121.277242, 31.600405), (121.280284, 31.596892), (121.286412, 31.590085),
    (121.293995, 31.580825), (121.297593, 31.575884), (121.305731, 31.564864), (121.309029, 31.561202),
    (121.322732, 31.546549), (121.333179, 31.554428), (121.319005, 31.569337), (121.316692, 31.569337),
    (121.311553, 31.57904), (121.305215, 31.588373), (121.302645, 31.592143), (121.277197, 31.615536),
    (121.263908, 31.627535), (121.223739, 31.656763), (121.20767, 31.667851), (121.198042, 31.674558),
    (121.188755, 31.680898), (121.172156, 31.691314), (121.164932, 31.69539), (121.151342, 31.703618),
    (121.145839, 31.70631), (121.13767, 31.714038), (121.119877, 31.730735), (121.110255, 31.740291),
    (121.10493, 31.745106), (121.104157, 31.746274)]

channel_centerline = [
    (121.0685167, 31.75925), (121.0725, 31.7573), (121.1005167, 31.74041667), (121.1115833, 31.72993333),
    (121.1196667, 31.7223), (121.1407833, 31.70385), (121.16795, 31.68841667), (121.1836333, 31.67931667),
    (121.2079333, 31.6623), (121.2285, 31.64766667), (121.241, 31.63916667), (121.2588, 31.62508333),
    (121.2769833, 31.60851667), (121.2871333, 31.59835), (121.2964833, 31.58878333), (121.30775, 31.57326667),
    (121.3116833, 31.5676), (121.32785, 31.55041667)]


geo_information = {
    'channel_side1': channel_side1,
    'channel_side2': channel_side2,
    'channel_centerline': channel_centerline,
    'channel_boundary': channel_boundary,
    'anchorage':anchorage
}
with open('data/geo_info.pkl', 'wb') as f:
    pickle.dump(geo_information, f)
#%%

#%%
