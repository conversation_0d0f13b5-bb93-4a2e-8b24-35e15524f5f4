import pickle
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

def analyze_classification_results():
    """分析穿越场景分类结果"""
    
    print("正在加载分类结果...")
    
    # 加载所有结果文件
    try:
        with open('../result/cross_ship_info.pkl', 'rb') as f:
            cross_ship_info = pickle.load(f)
        
        with open('../result/cross_related_avoid_results.pkl', 'rb') as f:
            cross_related_results = pickle.load(f)
        
        with open('../result/non_cross_avoid_results.pkl', 'rb') as f:
            non_cross_results = pickle.load(f)
        
        with open('../result/classification_stats.pkl', 'rb') as f:
            stats = pickle.load(f)
        
        with open('../result/avoid_results.pkl', 'rb') as f:
            original_results = pickle.load(f)
            
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        print("请先运行 02.穿越场景分类.py")
        return
    
    # 1. 基本统计信息
    print("\n=== 分类结果验证 ===")
    print(f"原始避让场景总数: {len(original_results['s_pair'])}")
    print(f"穿越船相关场景: {len(cross_related_results['s_pair'])}")
    print(f"非穿越船场景: {len(non_cross_results['s_pair'])}")
    print(f"分类完整性检查: {len(cross_related_results['s_pair']) + len(non_cross_results['s_pair']) == len(original_results['s_pair'])}")
    
    # 2. 穿越船信息分析
    print(f"\n=== 穿越船舶分析 ===")
    print(f"穿越船舶数量: {len(cross_ship_info['cross_ship_mmsi_set'])}")
    print(f"穿越轨迹数量: {len(cross_ship_info['cross_ship_indices'])}")
    
    # 3. 时间分布分析
    print(f"\n=== 时间分布分析 ===")
    
    # 分析穿越船相关场景的时间分布
    cross_times = pd.to_datetime(cross_related_results['PosTime'])
    non_cross_times = pd.to_datetime(non_cross_results['PosTime'])
    
    print(f"穿越船场景时间范围: {cross_times.min()} 到 {cross_times.max()}")
    print(f"非穿越船场景时间范围: {non_cross_times.min()} 到 {non_cross_times.max()}")
    
    # 4. 船舶对分析
    print(f"\n=== 船舶对分析 ===")
    
    # 统计穿越船在船舶对中的出现频次
    cross_ship_pair_count = Counter()
    for s_pair in cross_related_results['s_pair']:
        for ship_id in s_pair:
            if ship_id in cross_ship_info['cross_ship_mmsi_set']:
                cross_ship_pair_count[ship_id] += 1
    
    print(f"参与避让的穿越船数量: {len(cross_ship_pair_count)}")
    if cross_ship_pair_count:
        most_active_cross_ship = cross_ship_pair_count.most_common(1)[0]
        print(f"最活跃的穿越船: MMSI {most_active_cross_ship[0]}, 参与 {most_active_cross_ship[1]} 次避让")
    
    # 5. 详细统计报告
    print(f"\n=== 详细统计报告 ===")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    return {
        'cross_ship_info': cross_ship_info,
        'cross_related_results': cross_related_results,
        'non_cross_results': non_cross_results,
        'stats': stats,
        'cross_ship_pair_count': cross_ship_pair_count
    }

def create_visualization(analysis_results):
    """创建可视化图表"""
    
    stats = analysis_results['stats']
    cross_ship_pair_count = analysis_results['cross_ship_pair_count']
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 场景分类饼图
    labels = ['包含穿越船', '不含穿越船']
    sizes = [stats['cross_scenarios'], stats['non_cross_scenarios']]
    colors = ['#ff9999', '#66b3ff']
    
    axes[0, 0].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[0, 0].set_title('避让场景分类分布')
    
    # 2. 穿越船活跃度柱状图（取前10）
    if cross_ship_pair_count:
        top_ships = cross_ship_pair_count.most_common(10)
        ship_ids = [str(ship[0]) for ship in top_ships]
        counts = [ship[1] for ship in top_ships]
        
        axes[0, 1].bar(range(len(ship_ids)), counts, color='lightcoral')
        axes[0, 1].set_xlabel('穿越船舶 MMSI')
        axes[0, 1].set_ylabel('参与避让次数')
        axes[0, 1].set_title('穿越船舶避让活跃度（前10）')
        axes[0, 1].set_xticks(range(len(ship_ids)))
        axes[0, 1].set_xticklabels(ship_ids, rotation=45)
    
    # 3. 数量对比柱状图
    categories = ['总场景', '穿越船场景', '非穿越船场景', '穿越船数量']
    values = [stats['total_scenarios'], stats['cross_scenarios'], 
              stats['non_cross_scenarios'], stats['cross_ship_count']]
    
    bars = axes[1, 0].bar(categories, values, color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
    axes[1, 0].set_ylabel('数量')
    axes[1, 0].set_title('场景分类统计')
    
    # 在柱状图上显示数值
    for bar, value in zip(bars, values):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                       str(value), ha='center', va='bottom')
    
    # 4. 比例对比
    percentages = [stats['cross_percentage'], stats['non_cross_percentage']]
    categories = ['穿越船场景比例', '非穿越船场景比例']
    
    axes[1, 1].bar(categories, percentages, color=['lightcoral', 'lightgreen'])
    axes[1, 1].set_ylabel('百分比 (%)')
    axes[1, 1].set_title('场景分类比例')
    axes[1, 1].set_ylim(0, 100)
    
    # 在柱状图上显示数值
    for i, v in enumerate(percentages):
        axes[1, 1].text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('result/classification_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存到: result/classification_analysis.png")

def main():
    """主函数"""
    print("开始分析穿越场景分类结果...")
    
    # 分析结果
    analysis_results = analyze_classification_results()
    
    if analysis_results:
        # 创建可视化
        create_visualization(analysis_results)
        
        print("\n分析完成！")
    else:
        print("分析失败，请检查数据文件。")

if __name__ == '__main__':
    main() 