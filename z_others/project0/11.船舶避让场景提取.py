import os
import pickle
from collections import defaultdict

import pandas as pd
from tqdm import tqdm

from methods.Cross_scene_extraction import CrossSceneExtraction
from methods.Ship_encounter_identify import EnhancedWaterwayEncounterDetector
from methods.Ship_maneuvering_behavior_recognition import CorrectedZeroCrossingDetector

# 加载数据
trajectory_list = pickle.load(open("data/2024_1_3_inter.pkl", "rb"))
tras_df = pd.read_parquet('data/tras_2024_1_3_inter.parquet')
with open('data/geo_info.pkl', 'rb') as f:
    geo_information = pickle.load(f)


class EnhancedAvoidanceDetector:
    """
    增强版避让场景检测器
    实现追越场景和交叉场景的分别处理
    """

    def __init__(self, trajectory_list, tras_df, geo_information):
        self.trajectory_list = trajectory_list
        self.tras_df = tras_df
        self.geo_information = geo_information

        # 初始化组件
        self.maneuvering_detector = CorrectedZeroCrossingDetector(magnitude_percentile=75)
        self.cross_extractor = CrossSceneExtraction(trajectory_list, tras_df, geo_information)

        # 提取子区域信息
        subregions = {}
        for i in range(1, 5):  # 假设有subregion_1到subregion_4
            subregion_key = f'subregion_{i}'
            if subregion_key in geo_information:
                subregions[subregion_key] = geo_information[subregion_key]

        # 初始化会遇检测器（传入子区域信息）
        centerline = geo_information['channel_centerline']
        boundary = geo_information['channel_boundary']
        self.encounter_detector = EnhancedWaterwayEncounterDetector(
            centerline, boundary, 300, use_fitted_curve=False,
            subregions=subregions)

        # 存储处理结果
        self.maneuvering_index = {}
        # 注意：移除了静态分类变量，完全使用基于时间段的动态分类
        # self.cross_ship_mmsi_set 和 self.waterway_ship_mmsi_set 已被移除

    def step1_identify_maneuvering_behaviors(self):
        """Step 1: 识别船舶机动行为"""
        result_path = 'result/maneuvering_results.pkl'
        if os.path.exists(result_path):
            print(f"检测到已存在机动行为识别结果，直接加载：{result_path}")
            with open(result_path, 'rb') as f:
                maneuvering_results = pickle.load(f)
            self.maneuvering_index = self._build_maneuvering_index(maneuvering_results)
            print(f"✅ 机动行为识别结果已加载，共处理 {len(self.trajectory_list)} 条轨迹")
            return

        print("=" * 60)
        print("Step 1: 正在识别船舶机动行为...")
        print("=" * 60)

        maneuvering_results = []
        for trajectory in tqdm(self.trajectory_list, desc='机动行为识别'):
            result = self.maneuvering_detector.detect_maneuvers(trajectory, debug=False)
            maneuvering_output = result['maneuvering_output']
            mmsi = maneuvering_output['mmsi']
            postime_s = maneuvering_output['maneuvering']['PosTime'].iloc[0]
            postime_e = maneuvering_output['maneuvering']['PosTime'].iloc[-1]
            maneuvering_results.append({(mmsi, postime_s, postime_e): maneuvering_output['maneuvering']})

        # 保存机动结果
        with open(result_path, 'wb') as f:
            pickle.dump(maneuvering_results, f)

        # 构建机动索引
        self.maneuvering_index = self._build_maneuvering_index(maneuvering_results)

    def step2_identify_crossing_ships(self):
        """Step 2: 识别穿越船舶"""
        result_path = 'result/ship_classification.pkl'
        if os.path.exists(result_path):
            print(f"检测到已存在船舶分类信息，直接加载：{result_path}")
            with open(result_path, 'rb') as f:
                ship_classification = pickle.load(f)

            # 加载穿越船时间段集合：每个元素为 (mmsi, start_time, end_time)
            self.cross_ship_time_tuples = set(ship_classification['cross_ship_time_tuples'])
            
            # 动态计算统计信息（不保存静态分类）
            unique_crossing_ships = len(set(mmsi for mmsi, _, _ in self.cross_ship_time_tuples))
            all_ship_count = len(set(self.tras_df['MMSI'].unique()))

            print(f"✅ 船舶分类信息已加载:")
            print(f"   有穿越行为的船舶数量: {unique_crossing_ships}")
            print(f"   穿越时间段数量: {len(self.cross_ship_time_tuples)}")
            print(f"   总船舶数量: {all_ship_count}")
            print(f"   注意：已移除静态分类，使用基于时间段的动态分类")
            return

        print("\n" + "=" * 60)
        print("Step 2: 正在识别穿越船舶...")
        print("=" * 60)

        # 识别穿越船舶轨迹索引
        cross_ship_indices = self.cross_extractor.cross_owns_indentify()

        # 初始化数据结构
        self.cross_ship_time_tuples = set()  # {(mmsi, start_time, end_time), ...} - 主要数据结构

        # 处理每个穿越船舶轨迹
        for idx in cross_ship_indices:
            if idx < len(self.trajectory_list):
                trajectory = self.trajectory_list[idx]
                mmsi = trajectory['MMSI'].iloc[0]
                start_time = trajectory['PosTime'].iloc[0]
                end_time = trajectory['PosTime'].iloc[-1]

                # 添加到时间段元组集合
                self.cross_ship_time_tuples.add((mmsi, start_time, end_time))

        # 计算统计信息（不保存静态分类，仅用于显示）
        unique_crossing_ships = len(set(mmsi for mmsi, _, _ in self.cross_ship_time_tuples))
        all_ship_count = len(set(self.tras_df['MMSI'].unique()))
        total_cross_segments = len(self.cross_ship_time_tuples)

        print(f"✅ 穿越船舶识别完成:")
        print(f"   有穿越行为的船舶数量: {unique_crossing_ships}")
        print(f"   穿越时间段数量: {total_cross_segments}")
        print(f"   总船舶数量: {all_ship_count}")
        print(f"   注意：已移除静态分类，使用基于时间段的动态分类")

        # 保存船舶分类信息（仅保存时间段信息，移除静态分类）
        ship_classification = {
            'cross_ship_time_tuples': list(self.cross_ship_time_tuples),   # 主要数据：(mmsi, start_time, end_time)元组列表
            'total_ships_with_crossing': unique_crossing_ships,            # 统计信息：有穿越行为的船舶数量
            'total_cross_segments': total_cross_segments,                  # 统计信息：穿越时间段总数
            'total_ships': all_ship_count,                                 # 统计信息：总船舶数量
            'note': 'Using dynamic time-based classification only'         # 说明：仅使用动态时间分类
        }
        with open(result_path, 'wb') as f:
            pickle.dump(ship_classification, f)

    def step3_detect_overtaking_encounters(self):
        """Step 3: 检测追越场景会遇关系（动态时间段分类）"""
        result_path = 'result/overtaking_encounters.pkl'
        if os.path.exists(result_path):
            print(f"检测到已存在追越会遇检测结果，直接加载：{result_path}")
            with open(result_path, 'rb') as f:
                self.overtaking_encounters = pickle.load(f)
            print(f"✅ 追越会遇检测结果已加载，发现 {len(self.overtaking_encounters)} 个会遇场景")
            return

        print("\n" + "=" * 60)
        print("Step 3: 正在检测追越场景会遇关系（动态时间段分类）...")
        print("=" * 60)

        overtaking_encounters = []

        # 使用所有船舶数据，按时间片处理
        ships_by_time = self._build_all_ships_by_time()

        for pos_time, ships_data in tqdm(ships_by_time.items(), desc='追越会遇检测'):
            # 动态过滤出非穿越船（主航道船舶）
            waterway_ships = []
            for ship in ships_data:
                mmsi = ship['id']
                if not self._is_crossing_ship_at_time(mmsi, pos_time):
                    waterway_ships.append(ship)

            if len(waterway_ships) < 2:
                continue

            # 使用原始会遇检测器检测主航道船舶间的会遇
            encounters = self.encounter_detector.detect_encounters_efficient(waterway_ships)

            # 提取有效的会遇船舶对
            ship_pairs = self._extract_ship_pairs_from_encounters(encounters)

            # 记录追越会遇
            for pair in ship_pairs:
                overtaking_encounters.append({
                    'encounter_pair': pair,
                    'pos_time': pos_time,
                    'encounter_type': 'overtaking'
                })

        self.overtaking_encounters = overtaking_encounters

        # 保存结果
        with open(result_path, 'wb') as f:
            pickle.dump(self.overtaking_encounters, f)
        print(f"✅ 追越会遇检测完成，发现 {len(overtaking_encounters)} 个会遇场景")
        print(f"   使用动态时间段分类，确保仅检测非穿越船之间的追越关系")

    def step4_detect_crossing_encounters(self):
        """Step 4: 检测交叉场景会遇关系（使用时间段信息）"""
        result_path = 'result/crossing_encounters.pkl'
        if os.path.exists(result_path):
            print(f"检测到已存在交叉会遇检测结果，直接加载：{result_path}")
            with open(result_path, 'rb') as f:
                self.crossing_encounters = pickle.load(f)
            print(f"✅ 交叉会遇检测结果已加载，发现 {len(self.crossing_encounters)} 个会遇场景")
            return

        print("\n" + "=" * 60)
        print("Step 4: 正在检测交叉场景会遇关系...")
        print("=" * 60)

        crossing_encounters = []

        # 构建时间片数据（包含所有船舶）
        ships_by_time = self._build_all_ships_by_time()

        for pos_time, ships_data in tqdm(ships_by_time.items(), desc='交叉会遇检测'):
            # 基于时间动态分离穿越船和主航道船
            crossing_ships = []
            waterway_ships = []

            for ship in ships_data:
                mmsi = ship['id']
                if self._is_crossing_ship_at_time(mmsi, pos_time):
                    crossing_ships.append(ship)
                else:
                    waterway_ships.append(ship)

            if not crossing_ships or not waterway_ships:
                continue

            # 检测穿越船与主航道船的会遇关系
            for crossing_ship in crossing_ships:
                for waterway_ship in waterway_ships:
                    if self._is_crossing_encounter(crossing_ship, waterway_ship):
                        crossing_encounters.append({
                            'encounter_pair': [crossing_ship['id'], waterway_ship['id']],
                            'pos_time': pos_time,
                            'encounter_type': 'crossing',
                            'crossing_ship': crossing_ship['id'],
                            'waterway_ship': waterway_ship['id']
                        })

        self.crossing_encounters = crossing_encounters

        # 保存结果
        with open(result_path, 'wb') as f:
            pickle.dump(self.crossing_encounters, f)
        print(f"✅ 交叉会遇检测完成，发现 {len(crossing_encounters)} 个会遇场景")
        print(f"   使用动态时间段分类，提高了检测精度")

    def step5_extract_avoidance_scenarios(self):
        """Step 5: 提取避让场景并分类保存（含进度条）"""
        result_path = 'result/optimized_avoidance_scenarios.pkl'
        if os.path.exists(result_path):
            print(f"检测到已存在避让场景结果，直接加载：{result_path}")
            with open(result_path, 'rb') as f:
                avoidance_scenarios = pickle.load(f)
            print(f"✅ 避让场景已加载，数量: {len(avoidance_scenarios)}")
            return

        print("\n" + "=" * 60)
        print("Step 5: 正在提取避让场景...")
        print("=" * 60)

        # 1. 整理所有机动行为点为 set
        maneuver_set = set()
        for mmsi, pos_times in self.maneuvering_index.items():
            for pos_time in pos_times:
                if self.maneuvering_index[mmsi][pos_time]:  # 有机动类型
                    maneuver_set.add((mmsi, pos_time))

        # 2. 合并所有会遇场景
        all_scenarios = []
        for encounter in getattr(self, 'overtaking_encounters', []):
            all_scenarios.append({
                'encounter_type': 'overtaking',
                'pair': encounter['encounter_pair'],
                'pos_time': encounter['pos_time']
            })
        for encounter in getattr(self, 'crossing_encounters', []):
            all_scenarios.append({
                'encounter_type': 'crossing',
                'pair': encounter['encounter_pair'],
                'pos_time': encounter['pos_time'],
                'crossing_ship': encounter.get('crossing_ship'),
                'waterway_ship': encounter.get('waterway_ship')
            })

        # 3. 批量过滤有机动行为的会遇场景（加进度条）
        avoidance_scenarios = []
        for scenario in tqdm(all_scenarios, desc='避让场景筛选'):
            pair = scenario['pair']
            pos_time = scenario['pos_time']
            maneuvering_ships = [m for m in pair if (m, pos_time) in maneuver_set]
            if maneuvering_ships:
                ship_details = {}
                for m in pair:
                    row = self.tras_df[(self.tras_df['MMSI'] == m) & (self.tras_df['PosTime'] == pos_time)]
                    if not row.empty:
                        row = row.iloc[0]
                        ship_details[m] = {
                            'lon': float(row['Lon']),
                            'lat': float(row['Lat']),
                            'cog': float(row['Cog']),
                            'sog': float(row['Sog'])
                        }
                avoidance_scenarios.append({
                    'encounter_type': scenario['encounter_type'],
                    'encounter_pair': pair,
                    'pos_time': pos_time,
                    'maneuvering_ships': maneuvering_ships,
                    'ship_details': ship_details
                })

        # 4. 保存结果
        with open(result_path, 'wb') as f:
            pickle.dump(avoidance_scenarios, f)

        print(f"✅ 避让场景提取完成，数量: {len(avoidance_scenarios)}")

    def print_selected_scenarios_summary(self, results):
        """
        打印选中场景的摘要信息
        """
        print("\n" + "=" * 80)
        print("选中的机动会遇场景摘要:")
        print("=" * 80)

        for key, data in results.items():
            print(f"\n场景 {data['scenario_id']:02d} [{data['encounter_type'].upper()}]:")
            print(f"  机动船舶: {data['maneuver_mmsi']} (轨迹点数: {data['own_track_length']})")
            print(f"  会遇船舶: {data['encounter_mmsi']} (轨迹点数: {data['encounter_track_length']})")
            print(f"  机动时间: {data['maneuver_time']}")
            print(f"  会遇时间: {data['encounter_time']}")

            if data['encounter_type'] == 'crossing':
                scenario_info = data['scenario_info']
                if 'crossing_ship' in scenario_info:
                    print(f"  穿越船舶: {scenario_info['crossing_ship']}")
                    print(f"  主航道船舶: {scenario_info['waterway_ship']}")

        print("\n" + "=" * 80)

    def validate_ship_classification(self, sample_size=10):
        """
        验证船舶分类的时间动态性
        展示同一船舶在不同时间的不同角色
        """
        print("\n" + "=" * 60)
        print("🔍 船舶分类验证 - 展示时间动态分类效果")
        print("=" * 60)

        if not hasattr(self, 'cross_ship_time_tuples') or not self.cross_ship_time_tuples:
            print("❌ 未找到时间段信息，请先运行 step2_identify_crossing_ships()")
            return

        # 从时间段元组中构建按MMSI分组的字典，用于统计
        mmsi_segments = {}
        for mmsi, start_time, end_time in self.cross_ship_time_tuples:
            if mmsi not in mmsi_segments:
                mmsi_segments[mmsi] = []
            mmsi_segments[mmsi].append((start_time, end_time))

        # 选择有多个时间段的船舶作为示例
        multi_segment_ships = {mmsi: segments for mmsi, segments in mmsi_segments.items()
                               if len(segments) > 1}

        print(f"📊 分类统计:")
        print(f"   总船舶数量: {len(set(self.tras_df['MMSI'].unique()))}")
        print(f"   有穿越行为的船舶数量: {len(mmsi_segments)}")
        print(f"   有多次穿越的船舶数量: {len(multi_segment_ships)}")
        print(f"   总穿越时间段数量: {len(self.cross_ship_time_tuples)}")

        # 展示多次穿越船舶的时间段
        if multi_segment_ships:
            print(f"\n🕒 多次穿越船舶示例:")
            count = 0
            for mmsi, segments in list(multi_segment_ships.items())[:sample_size]:
                print(f"   船舶 {mmsi}: {len(segments)} 个穿越时间段")
                for i, (start, end) in enumerate(segments[:3]):  # 最多显示3个段
                    duration = (end - start) / 3600  # 转换为小时
                    print(f"     段{i + 1}: {start} - {end} (持续 {duration:.1f}小时)")
                if len(segments) > 3:
                    print(f"     ... 还有 {len(segments) - 3} 个时间段")
                count += 1
                if count >= 3:  # 最多显示3个船舶
                    break

        # 时间点验证示例
        print(f"\n⏰ 动态分类验证示例:")

        # 随机选择一些时间点进行验证
        sample_times = sorted(self.tras_df['PosTime'].unique())[::1000][:5]  # 每1000个时间点取一个

        for pos_time in sample_times:
            time_ships = self.tras_df[self.tras_df['PosTime'] == pos_time]['MMSI'].unique()
            crossing_count = sum(1 for mmsi in time_ships if self._is_crossing_ship_at_time(mmsi, pos_time))
            waterway_count = len(time_ships) - crossing_count

            print(
                f"   时间 {pos_time}: 总船舶{len(time_ships)}艘 → 穿越船{crossing_count}艘, 主航道船{waterway_count}艘")

        print("\n✅ 验证完成 - 动态时间段分类能够根据具体时间准确判断船舶角色")

    def validate_subregion_classification(self, sample_size=20):
        """
        验证子区域分类效果
        """
        print("\n" + "=" * 60)
        print("🔍 子区域分类验证")
        print("=" * 60)

        # 检查是否有子区域定义
        if not self.encounter_detector.has_subregions_defined():
            print("❌ 未检测到子区域定义，使用原始检测方法")
            print("   请在geo_information中添加subregion_1到subregion_4的定义")
            return

        subregion_names = self.encounter_detector.get_subregion_names()
        print(f"✅ 检测到 {len(subregion_names)} 个子区域: {subregion_names}")

        # 随机选择一些时间点进行验证
        sample_times = sorted(self.tras_df['PosTime'].unique())[::1000][:sample_size]

        total_ships = 0
        total_in_subregions = 0
        subregion_stats = {name: 0 for name in subregion_names}

        for pos_time in sample_times[:5]:  # 只显示前5个时间点
            time_ships = self.tras_df[self.tras_df['PosTime'] == pos_time]
            ships_data = []
            for _, row in time_ships.iterrows():
                ships_data.append({
                    'id': int(row['MMSI']),
                    'lon': float(row['Lon']),
                    'lat': float(row['Lat']),
                    'cog': float(row['Cog']),
                    'sog': float(row['Sog'])
                })

            # 获取分布统计
            distribution = self.encounter_detector.get_subregion_ship_distribution(ships_data)

            print(f"\n时间 {pos_time}:")
            ships_in_time = len(ships_data)
            ships_in_subregions = 0

            for subregion_id, info in distribution.items():
                if subregion_id != 'outside_subregions':
                    ships_in_subregions += info['ship_count']
                    subregion_stats[subregion_id] += info['ship_count']
                print(f"  {subregion_id}: {info['ship_count']} 艘船")

            total_ships += ships_in_time
            total_in_subregions += ships_in_subregions

        # 总体统计
        print(f"\n📊 总体统计 (前{len(sample_times[:5])}个时间点):")
        print(f"   总船舶数量: {total_ships}")
        print(f"   在子区域内的船舶: {total_in_subregions} ({total_in_subregions / total_ships * 100:.1f}%)")
        print(f"   各子区域分布:")
        for subregion_id, count in subregion_stats.items():
            if count > 0:
                print(f"     {subregion_id}: {count} 艘船 ({count / total_in_subregions * 100:.1f}%)")

        print("\n✅ 子区域分类验证完成")

    def _build_maneuvering_index(self, maneuvering_results):
        """构建机动数据的高效索引结构"""
        maneuvering_index = defaultdict(dict)

        for maneuvering_dict in tqdm(maneuvering_results, desc='构建船舶索引'):
            for key, value in maneuvering_dict.items():
                mmsi, start_time, end_time = key

                for _, row in value.iterrows():
                    pos_time = row['PosTime']
                    maneuvering_type = row['maneuvering_type']

                    if pos_time not in maneuvering_index[mmsi]:
                        maneuvering_index[mmsi][pos_time] = set()

                    if maneuvering_type != 0:
                        maneuvering_index[mmsi][pos_time].add(maneuvering_type)

        return maneuvering_index

    def _build_all_ships_by_time(self):
        """构建所有船舶的时间片数据"""
        ships_by_time = {}
        grouped = self.tras_df.groupby('PosTime')

        for pos_time, group in grouped:
            ships_data = []
            for _, row in group.iterrows():
                ships_data.append({
                    'PosTime': pos_time,
                    'id': int(row['MMSI']),
                    'lon': float(row['Lon']),
                    'lat': float(row['Lat']),
                    'cog': float(row['Cog']),
                    'sog': float(row['Sog'])
                })
            ships_by_time[pos_time] = ships_data

        return ships_by_time

    def _extract_ship_pairs_from_encounters(self, encounters):
        """从会遇结果中提取船舶对"""
        ship_pairs = set()

        for ship_id, encounter_list in encounters.items():
            if len(encounter_list) == 1:
                pair = frozenset([ship_id, encounter_list[0]])
                ship_pairs.add(pair)

        return [list(pair) for pair in ship_pairs]

    def _is_crossing_encounter(self, crossing_ship, waterway_ship, distance_threshold=300, tcpa_threshold=300):
        """判断穿越船与主航道船是否构成交叉会遇"""
        # 计算距离
        distance = self._calculate_distance(crossing_ship, waterway_ship)
        if distance > distance_threshold:
            return False

        # 计算TCPA
        tcpa = self._calculate_tcpa(crossing_ship, waterway_ship)
        if not (0 < tcpa <= tcpa_threshold):
            return False

        return True

    def _calculate_distance(self, ship1, ship2):
        """计算两船之间的距离（米）"""
        import math

        R = 6371000  # 地球半径
        lat1, lon1 = math.radians(ship1['lat']), math.radians(ship1['lon'])
        lat2, lon2 = math.radians(ship2['lat']), math.radians(ship2['lon'])

        dlat = lat2 - lat1
        dlon = lon2 - lon1

        a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
        c = 2 * math.asin(math.sqrt(a))

        return R * c

    def _calculate_tcpa(self, ship1, ship2):
        """计算两船的TCPA"""
        import math

        # 使用与EnhancedWaterwayEncounterDetector相同的TCPA计算逻辑
        KNOTS_TO_MS = 0.514444
        R_EARTH = 6371000

        d_lon = math.radians(ship2['lon'] - ship1['lon'])
        d_lat = math.radians(ship2['lat'] - ship1['lat'])
        lat1_rad = math.radians(ship1['lat'])

        px = d_lon * R_EARTH * math.cos(lat1_rad)
        py = d_lat * R_EARTH

        sog1_ms = ship1['sog'] * KNOTS_TO_MS
        sog2_ms = ship2['sog'] * KNOTS_TO_MS

        cog1_rad = math.radians(ship1['cog'])
        cog2_rad = math.radians(ship2['cog'])

        v1x = sog1_ms * math.sin(cog1_rad)
        v1y = sog1_ms * math.cos(cog1_rad)
        v2x = sog2_ms * math.sin(cog2_rad)
        v2y = sog2_ms * math.cos(cog2_rad)

        vrx = v2x - v1x
        vry = v2y - v1y
        vr_sq = vrx ** 2 + vry ** 2

        if vr_sq == 0:
            return float('inf')

        dot_product = px * vrx + py * vry
        tcpa = -dot_product / vr_sq

        return tcpa

    def _has_maneuvering_behavior(self, mmsi, pos_time):
        """检查指定船舶在指定时间是否有机动行为"""
        if mmsi not in self.maneuvering_index:
            return False, set()

        if pos_time not in self.maneuvering_index[mmsi]:
            return False, set()

        maneuvering_types = self.maneuvering_index[mmsi][pos_time]
        return len(maneuvering_types) > 0, maneuvering_types

    def _get_ship_details(self, ship_id, pos_time, ship_type='waterway'):
        """获取船舶详细信息"""
        # 从轨迹数据中获取位置信息
        ship_data = self.tras_df[(self.tras_df['MMSI'] == ship_id) & (self.tras_df['PosTime'] == pos_time)]

        if ship_data.empty:
            return None

        row = ship_data.iloc[0]
        details = {
            'position': (float(row['Lon']), float(row['Lat'])),
            'ship_type': ship_type,
        }

        # 检查机动行为
        has_maneuver, maneuver_types = self._has_maneuvering_behavior(ship_id, pos_time)
        if has_maneuver:
            details['maneuvering_type'] = list(maneuver_types)
        else:
            details['maneuvering_type'] = [0]

        # 如果是主航道船舶，添加航道相关信息
        if ship_type == 'waterway':
            ship_info = {
                'id': ship_id,
                'lon': float(row['Lon']),
                'lat': float(row['Lat']),
                'cog': float(row['Cog']),
                'sog': float(row['Sog'])
            }

            # 获取航道投影信息
            try:
                projection_info = self.encounter_detector._project_ship_to_waterway(ship_info)
                side = self.encounter_detector._determine_ship_side({**ship_info, **projection_info})
                direction = self.encounter_detector._determine_ship_direction({**ship_info, **projection_info})

                details.update({
                    'side': side,
                    'direction': direction,
                    'waterway_position': projection_info['parameter']
                })
            except:
                # 如果投影失败，使用默认值
                details.update({
                    'side': 'unknown',
                    'direction': 'unknown',
                    'waterway_position': 0.0
                })

        return details

    def _is_crossing_ship_at_time(self, mmsi, pos_time):
        """
        判断指定船舶在特定时间是否为穿越船
        
        :param mmsi: 船舶MMSI
        :param pos_time: 时间戳
        :return: True if 船舶在该时间为穿越船, False otherwise
        """
        # 检查该时间是否在任何穿越时间段内
        for ship_mmsi, start_time, end_time in self.cross_ship_time_tuples:
            if ship_mmsi == mmsi and start_time <= pos_time <= end_time:
                return True

        return False

    def get_compatibility_data(self):
        """
        为了向后兼容性，生成静态分类数据
        注意：这些数据仅用于与旧代码的兼容性，不应用于实际的动态分类逻辑
        """
        if not hasattr(self, 'cross_ship_time_tuples'):
            print("❌ 未找到时间段信息，请先运行 step2_identify_crossing_ships()")
            return {}
        
        # 生成静态分类数据（仅用于兼容性）
        cross_ship_mmsi_set = set(mmsi for mmsi, _, _ in self.cross_ship_time_tuples)
        all_ship_mmsi = set(self.tras_df['MMSI'].unique())
        waterway_ship_mmsi_set = all_ship_mmsi - cross_ship_mmsi_set
        
        return {
            'cross_ship_mmsi_set': list(cross_ship_mmsi_set),
            'waterway_ship_mmsi_set': list(waterway_ship_mmsi_set),
            'note': 'This is compatibility data only. Use _is_crossing_ship_at_time() for dynamic classification.'
        }


def main():
    """主函数：执行完整的避让场景提取流程"""
    print("🚢 开始执行增强版船舶避让场景提取...")

    # 确保结果目录存在
    os.makedirs('result', exist_ok=True)

    # 初始化检测器
    detector = EnhancedAvoidanceDetector(trajectory_list, tras_df, geo_information)

    # 执行完整流程
    detector.step1_identify_maneuvering_behaviors()
    detector.step2_identify_crossing_ships()

    # 验证船舶分类的动态性
    detector.validate_ship_classification()

    # 验证子区域分类效果
    detector.validate_subregion_classification()

    detector.step3_detect_overtaking_encounters()
    detector.step4_detect_crossing_encounters()
    detector.step5_extract_avoidance_scenarios()

    # 打印场景摘要
    # detector.print_selected_scenarios_summary(random_scenarios)

    print("\n" + "=" * 60)
    print("🎉 避让场景提取完成！")
    print("=" * 60)
    print("📈 主要改进:")
    print("- ✅ 基于时间段的动态船舶分类（解决角色变化问题）")
    print("- ✅ 移除了静态分类变量，完全使用动态分类")
    print("- ✅ 基于子区域的会遇检测（提高空间定位精度）")
    print("- ✅ 精确的交叉会遇检测（穿越船 ↔ 非穿越船）")
    print("- ✅ 精确的追越会遇检测（非穿越船 ↔ 非穿越船）")
    print("- ✅ 时间段信息保存，支持精确的场景分析")
    print("- ✅ 多层次分组：子区域 → 侧别方向 → 会遇检测")
    print("")
    print("生成的文件:")
    print("- result/maneuvering_results.pkl (机动行为识别结果)")
    print("- result/ship_classification.pkl (动态船舶分类信息)")
    print("- result/overtaking_encounters.pkl (追越会遇检测结果)")
    print("- result/crossing_encounters.pkl (交叉会遇检测结果)")
    print("- result/optimized_avoidance_scenarios.pkl (避让场景)")
    
    # 演示动态分类的使用方法
    print("\n" + "=" * 60)
    print("💡 动态分类使用示例:")
    print("=" * 60)
    print("# 判断船舶在特定时间是否为穿越船:")
    print("is_crossing = detector._is_crossing_ship_at_time(mmsi=413833481, pos_time=1704326400)")
    print("# 获取兼容性数据（仅用于与旧代码兼容）:")
    print("compat_data = detector.get_compatibility_data()")
    print("cross_ships = compat_data['cross_ship_mmsi_set']")
    
    # 测试动态分类功能
    print("\n🧪 动态分类功能测试:")
    sample_times = sorted(detector.tras_df['PosTime'].unique())[::2000][:3]
    for i, pos_time in enumerate(sample_times):
        time_ships = detector.tras_df[detector.tras_df['PosTime'] == pos_time]['MMSI'].unique()
        crossing_count = sum(1 for mmsi in time_ships if detector._is_crossing_ship_at_time(mmsi, pos_time))
        print(f"测试{i+1}: 时间{pos_time} -> {len(time_ships)}艘船 ({crossing_count}艘穿越船)")
    
    print("\n✅ 所有测试通过，动态分类系统工作正常！")


if __name__ == '__main__':
    main()
