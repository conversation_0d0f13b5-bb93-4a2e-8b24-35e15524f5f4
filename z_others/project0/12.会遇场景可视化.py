import os
import pickle
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Circle
from matplotlib.lines import Line2D
import seaborn as sns
import matplotlib
import warnings
from PIL import Image

# 忽略字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

# 设置中文字体和样式 - 更通用的字体配置
def setup_chinese_font():
    """设置中文字体显示"""
    try:
        # 尝试不同的中文字体
        chinese_fonts = [
            'Microsoft YaHei',  # 微软雅黑
            'SimHei',           # 黑体
            'SimSun',           # 宋体
            'KaiTi',            # 楷体
            'FangSong',         # 仿宋
            'STSong',           # 华文宋体
            'STKaiti',          # 华文楷体
            'STHeiti',          # 华文黑体
            'Arial Unicode MS', # Arial Unicode
            'DejaVu Sans'       # 备选
        ]
        
        import matplotlib.font_manager as fm
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        # 找到第一个可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break
        
        if selected_font:
            plt.rcParams['font.sans-serif'] = [selected_font]
            print(f"✅ 使用字体: {selected_font}")
        else:
            # 如果没有找到中文字体，使用系统默认字体并禁用unicode minus处理
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
            print("⚠️  未找到中文字体，将使用英文显示")
            
    except Exception as e:
        print(f"⚠️  字体设置失败: {e}，使用默认字体")
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']

# 设置字体
setup_chinese_font()
plt.rcParams['axes.unicode_minus'] = False

# 设置matplotlib后端为Agg避免显示问题
matplotlib.use('Agg')

sns.set_style("whitegrid")

class EncounterTrajectoryVisualizer:
    """
    会遇场景轨迹可视化器
    用于可视化机动会遇场景中船舶对的轨迹
    """
    
    def __init__(self, results_path='result/random_encounter_tracks.pkl', 
                 map_path='data/map0.png', geo_info_path='data/geo_info.pkl'):
        """
        初始化可视化器
        
        :param results_path: 随机会遇轨迹结果文件路径
        :param map_path: 底图文件路径
        :param geo_info_path: 地理信息文件路径
        """
        self.results_path = results_path
        self.map_path = map_path
        self.geo_info_path = geo_info_path
        
        self.results = self._load_results()
        self.map_img, self.map_extent = self._load_map_and_geo_info()
        
        self.colors = {
            'overtaking': {'own': '#FF6B6B', 'encounter': '#4ECDC4'},
            'crossing': {'own': '#45B7D1', 'encounter': '#FFA07A'}
        }
        
    def _load_results(self):
        """加载随机会遇轨迹结果"""
        if not os.path.exists(self.results_path):
            raise FileNotFoundError(f"未找到结果文件: {self.results_path}")
        
        with open(self.results_path, 'rb') as f:
            results = pickle.load(f)
        
        print(f"✅ 成功加载 {len(results)} 个会遇场景")
        return results
    
    def _load_map_and_geo_info(self):
        """加载底图和地理信息"""
        map_img = None
        map_extent = None
        
        # 加载底图
        if os.path.exists(self.map_path):
            try:
                map_img = Image.open(self.map_path)
                print(f"✅ 成功加载底图: {self.map_path}")
            except Exception as e:
                print(f"⚠️  加载底图失败: {e}")
        else:
            print(f"⚠️  底图文件不存在: {self.map_path}")
        
        # 加载地理信息
        if os.path.exists(self.geo_info_path):
            try:
                with open(self.geo_info_path, 'rb') as f:
                    geo_info = pickle.load(f)
                    
                # 检查geo_info的结构
                print(f"📍 地理信息内容: {geo_info}")
                
                # 尝试提取地理范围信息
                if isinstance(geo_info, dict):
                    if 'extent' in geo_info:
                        map_extent = geo_info['extent']
                    elif 'bounds' in geo_info:
                        map_extent = geo_info['bounds']
                    elif all(key in geo_info for key in ['min_lon', 'max_lon', 'min_lat', 'max_lat']):
                        map_extent = [geo_info['min_lon'], geo_info['max_lon'], 
                                    geo_info['min_lat'], geo_info['max_lat']]
                    else:
                        # 如果没有明确的范围信息，尝试从数据中推断
                        print("🔍 未找到明确的地理范围，将从轨迹数据推断")
                        
                if map_extent:
                    print(f"✅ 地理范围: {map_extent}")
                else:
                    print("⚠️  未能确定地理范围，将从轨迹数据推断")
                    
            except Exception as e:
                print(f"⚠️  加载地理信息失败: {e}")
        else:
            print(f"⚠️  地理信息文件不存在: {self.geo_info_path}")
        
        return map_img, map_extent
    
    def _get_data_extent(self):
        """从轨迹数据中计算地理范围"""
        all_lons = []
        all_lats = []
        
        for data in self.results.values():
            own_track = data['own_track']
            encounter_track = data['encounter_track']
            
            if not own_track.empty:
                all_lons.extend(own_track['Lon'].tolist())
                all_lats.extend(own_track['Lat'].tolist())
            
            if not encounter_track.empty:
                all_lons.extend(encounter_track['Lon'].tolist())
                all_lats.extend(encounter_track['Lat'].tolist())
        
        if all_lons and all_lats:
            # 添加一些边距
            margin_lon = (max(all_lons) - min(all_lons)) * 0.05
            margin_lat = (max(all_lats) - min(all_lats)) * 0.05
            
            extent = [
                min(all_lons) - margin_lon,  # min_lon
                max(all_lons) + margin_lon,  # max_lon
                min(all_lats) - margin_lat,  # min_lat
                max(all_lats) + margin_lat   # max_lat
            ]
            print(f"📊 从数据推断的地理范围: {extent}")
            return extent
        
        return None
    
    def _setup_map_background(self, ax):
        """设置底图背景"""
        if self.map_img is not None:
            # 确定地图范围
            extent = self.map_extent if self.map_extent else self._get_data_extent()
            
            if extent:
                # 显示底图
                ax.imshow(self.map_img, extent=extent, aspect='auto', alpha=0.7, zorder=0)
                print(f"🗺️  底图已显示，范围: {extent}")
                return extent
            else:
                print("⚠️  无法确定地图范围，跳过底图显示")
        
        return None
    
    def visualize_single_scenario(self, scenario_key, save_path=None, show_details=True, use_map=True):
        """
        可视化单个会遇场景
        
        :param scenario_key: 场景键值
        :param save_path: 保存路径
        :param show_details: 是否显示详细信息
        :param use_map: 是否使用底图
        """
        if scenario_key not in self.results:
            print(f"未找到场景: {scenario_key}")
            return
        
        data = self.results[scenario_key]
        encounter_type = data['encounter_type']
        
        try:
            # 创建图形
            fig, ax = plt.subplots(1, 1, figsize=(12, 10))
            
            # 设置底图背景
            if use_map:
                map_extent = self._setup_map_background(ax)
            
            # 获取轨迹数据
            own_track = data['own_track']
            encounter_track = data['encounter_track']
            
            if own_track.empty or encounter_track.empty:
                print(f"场景 {scenario_key} 轨迹数据为空")
                plt.close(fig)
                return
            
            # 绘制轨迹
            colors = self.colors[encounter_type]
            linewidth = 2
            # 机动船舶轨迹
            ax.plot(own_track['Lon'], own_track['Lat'], 
                   color=colors['own'], linewidth=linewidth, alpha=0.9,
                   label=f'Maneuvering Ship {data["maneuver_mmsi"]}', zorder=5)
            
            # 会遇船舶轨迹
            ax.plot(encounter_track['Lon'], encounter_track['Lat'], 
                   color=colors['encounter'], linewidth=linewidth, alpha=0.9,
                   label=f'Encounter Ship {data["encounter_mmsi"]}', zorder=5)
            
            # 标记起始点
            ax.scatter(own_track['Lon'].iloc[0], own_track['Lat'].iloc[0], 
                      color=colors['own'], s=60, marker='o', edgecolor='white', linewidth=linewidth,
                      label='Start Point', zorder=3)
            ax.scatter(encounter_track['Lon'].iloc[0], encounter_track['Lat'].iloc[0], 
                      color=colors['encounter'], s=60, marker='o', edgecolor='white', linewidth=linewidth, zorder=3)
            
            # 标记结束点
            ax.scatter(own_track['Lon'].iloc[-1], own_track['Lat'].iloc[-1], 
                      color=colors['own'], s=60, marker='s', edgecolor='white', linewidth=linewidth,
                      label='End Point', zorder=3)
            ax.scatter(encounter_track['Lon'].iloc[-1], encounter_track['Lat'].iloc[-1], 
                      color=colors['encounter'], s=60, marker='s', edgecolor='white', linewidth=linewidth, zorder=3)
            
            # 标记机动时间点和会遇时间点
            # maneuver_time = data['maneuver_time']
            encounter_time = data['encounter_time']
            
            # # 找到机动时间点的位置
            # maneuver_point = own_track[own_track['PosTime'] == maneuver_time]
            # if not maneuver_point.empty:
            #     ax.scatter(maneuver_point['Lon'].iloc[0], maneuver_point['Lat'].iloc[0],
            #               color='red', s=180, marker='*', edgecolor='white', linewidth=2,
            #               label='Maneuver Time', zorder=7)
            
            # 找到会遇时间点的位置（两船都在的时间点）
            encounter_own = own_track[own_track['PosTime'] == encounter_time]
            encounter_other = encounter_track[encounter_track['PosTime'] == encounter_time]

            if not encounter_own.empty and not encounter_other.empty:
                ax.scatter(encounter_own['Lon'].iloc[0], encounter_own['Lat'].iloc[0],
                          color='orange', s=60, marker='D', edgecolor='white', linewidth=2,
                          label='Encounter Time', zorder=3)
                ax.scatter(encounter_other['Lon'].iloc[0], encounter_other['Lat'].iloc[0],
                          color='orange', s=60, marker='D', edgecolor='white', linewidth=2, zorder=3)
            #
            #     # 绘制会遇时刻的连接线
            #     ax.plot([encounter_own['Lon'].iloc[0], encounter_other['Lon'].iloc[0]],
            #            [encounter_own['Lat'].iloc[0], encounter_other['Lat'].iloc[0]],
            #            'orange', linestyle='--', alpha=0.8, linewidth=3, zorder=5)
            
            # 设置图形属性

            encounter_type_english = 'Overtaking' if encounter_type == 'overtaking' else 'Crossing'
            ax.set_title(f'{encounter_type_english} Encounter Scenario - Scenario {data["scenario_id"]:02d}\n'
                        f'Maneuvering Ship: {data["maneuver_mmsi"]} | Encounter Ship: {data["encounter_mmsi"]}', 
                        fontsize=14, fontweight='bold')
            
            ax.set_xlabel('Longitude (°)', fontsize=12)
            ax.set_ylabel('Latitude (°)', fontsize=12)
            
            # 只在没有底图时显示网格
            if not use_map or self.map_img is None:
                ax.grid(True, alpha=0.3)
            
            ax.legend(fontsize=10, loc='upper right', fancybox=True, shadow=True)
            
            # 设置坐标轴等比例
            ax.set_aspect('equal', adjustable='box')
            
            # 添加详细信息文本框
            if show_details:
                info_text = self._generate_info_text(data)
                ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                       fontsize=9, verticalalignment='top', 
                       bbox=dict(boxstyle="round,pad=0.4", facecolor="lightgray", alpha=0.9))
            
            plt.tight_layout()
            
            # 保存图形
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 场景图保存到: {save_path}")
            
            # 不显示图形，直接关闭
            plt.close(fig)
            
        except Exception as e:
            print(f"处理场景 {scenario_key} 时出错: {e}")
            plt.close('all')  # 确保关闭所有图形
    
    def visualize_all_scenarios(self, save_dir='vis/encounter_scenarios', show_plots=False, use_map=True):
        """
        可视化所有会遇场景
        
        :param save_dir: 保存目录
        :param show_plots: 是否显示图形
        :param use_map: 是否使用底图
        """
        os.makedirs(save_dir, exist_ok=True)
        
        print(f"正在可视化 {len(self.results)} 个会遇场景...")
        if use_map and self.map_img is not None:
            print("🗺️  使用底图模式")
        
        success_count = 0
        for i, (scenario_key, data) in enumerate(self.results.items()):
            print(f"处理场景 {i+1}/{len(self.results)}: {scenario_key}")
            
            save_path = os.path.join(save_dir, f"{scenario_key}.png")
            
            try:
                self.visualize_single_scenario(scenario_key, save_path, show_details=True, use_map=use_map)
                success_count += 1
            except Exception as e:
                print(f"处理场景 {scenario_key} 时出错: {e}")
                continue
        
        print(f"✅ 成功处理 {success_count}/{len(self.results)} 个场景，保存到目录: {save_dir}")
    
    def visualize_scenario_comparison(self, save_path='vis/encounter_comparison.png', use_map=True):
        """
        对比展示追越和交叉场景
        """
        try:
            # 分类场景
            overtaking_scenarios = {k: v for k, v in self.results.items() if v['encounter_type'] == 'overtaking'}
            crossing_scenarios = {k: v for k, v in self.results.items() if v['encounter_type'] == 'crossing'}
            
            # 选择代表性场景（每类选择前2个）
            selected_overtaking = list(overtaking_scenarios.items())[:2]
            selected_crossing = list(crossing_scenarios.items())[:2]
            
            fig, axes = plt.subplots(2, 2, figsize=(20, 16))
            fig.suptitle('Ship Encounter Scenario Comparison', fontsize=18, fontweight='bold')
            
            # 绘制追越场景
            for i, (key, data) in enumerate(selected_overtaking):
                if i < 2:
                    self._plot_scenario_subplot(axes[0, i], data, f'Overtaking Scenario {i+1}', use_map)
            
            # 绘制交叉场景
            for i, (key, data) in enumerate(selected_crossing):
                if i < 2:
                    self._plot_scenario_subplot(axes[1, i], data, f'Crossing Scenario {i+1}', use_map)
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 对比图保存到: {save_path}")
            plt.close(fig)
            
        except Exception as e:
            print(f"生成对比图时出错: {e}")
            plt.close('all')
    
    def _plot_scenario_subplot(self, ax, data, title, use_map=True):
        """绘制子图"""
        try:
            # 设置底图背景
            if use_map:
                self._setup_map_background(ax)
            
            encounter_type = data['encounter_type']
            colors = self.colors[encounter_type]
            
            own_track = data['own_track']
            encounter_track = data['encounter_track']
            
            if own_track.empty or encounter_track.empty:
                ax.text(0.5, 0.5, 'No Track Data', ha='center', va='center', transform=ax.transAxes)
                return
            
            # 绘制轨迹
            ax.plot(own_track['Lon'], own_track['Lat'], 
                   color=colors['own'], linewidth=2.5, alpha=0.9,
                   label=f'Maneuvering Ship {data["maneuver_mmsi"]}', zorder=5)
            ax.plot(encounter_track['Lon'], encounter_track['Lat'], 
                   color=colors['encounter'], linewidth=2.5, alpha=0.9,
                   label=f'Encounter Ship {data["encounter_mmsi"]}', zorder=5)
            
            # 标记关键点
            ax.scatter(own_track['Lon'].iloc[0], own_track['Lat'].iloc[0], 
                      color=colors['own'], s=100, marker='o', edgecolor='white', linewidth=2, zorder=6)
            ax.scatter(encounter_track['Lon'].iloc[0], encounter_track['Lat'].iloc[0], 
                      color=colors['encounter'], s=100, marker='o', edgecolor='white', linewidth=2, zorder=6)
            
            ax.set_title(title, fontsize=12, fontweight='bold')
            ax.set_xlabel('Longitude (°)', fontsize=10)
            ax.set_ylabel('Latitude (°)', fontsize=10)
            
            # 只在没有底图时显示网格
            if not use_map or self.map_img is None:
                ax.grid(True, alpha=0.3)
            
            ax.legend(fontsize=8, loc='upper right')
            ax.set_aspect('equal', adjustable='box')
            
        except Exception as e:
            print(f"绘制子图时出错: {e}")
            ax.text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)
    
    def _generate_info_text(self, data):
        """生成信息文本"""
        try:
            scenario_info = data['scenario_info']
            
            info_lines = [
                f"Scenario ID: {data['scenario_id']}",
                f"Type: {'Overtaking' if data['encounter_type'] == 'overtaking' else 'Crossing'}",
                f"Maneuvering Ship: {data['maneuver_mmsi']}",
                f"Encounter Ship: {data['encounter_mmsi']}",
                f"Maneuver Time: {data['maneuver_time']}",
                f"Encounter Time: {data['encounter_time']}",
                f"Own Track Points: {data['own_track_length']}",
                f"Encounter Track Points: {data['encounter_track_length']}"
            ]
            
            if data['encounter_type'] == 'crossing' and 'crossing_ship' in scenario_info:
                info_lines.extend([
                    f"Crossing Ship: {scenario_info['crossing_ship']}",
                    f"Waterway Ship: {scenario_info['waterway_ship']}"
                ])
            
            return '\n'.join(info_lines)
            
        except Exception as e:
            return f"Info Error: {str(e)}"
    
    def generate_summary_statistics(self):
        """生成摘要统计"""
        try:
            overtaking_count = len([k for k in self.results.keys() if self.results[k]['encounter_type'] == 'overtaking'])
            crossing_count = len([k for k in self.results.keys() if self.results[k]['encounter_type'] == 'crossing'])
            
            print("\n" + "=" * 60)
            print("会遇场景统计摘要 / Encounter Scenario Statistics")
            print("=" * 60)
            print(f"总场景数 / Total Scenarios: {len(self.results)}")
            print(f"追越场景 / Overtaking Scenarios: {overtaking_count}")
            print(f"交叉场景 / Crossing Scenarios: {crossing_count}")
            
            # 轨迹长度统计
            own_lengths = [data['own_track_length'] for data in self.results.values()]
            encounter_lengths = [data['encounter_track_length'] for data in self.results.values()]
            
            print(f"\n轨迹点数统计 / Track Points Statistics:")
            print(f"机动船舶轨迹点数 / Maneuvering Ship Track Points - 平均/Avg: {np.mean(own_lengths):.1f}, 范围/Range: {min(own_lengths)}-{max(own_lengths)}")
            print(f"会遇船舶轨迹点数 / Encounter Ship Track Points - 平均/Avg: {np.mean(encounter_lengths):.1f}, 范围/Range: {min(encounter_lengths)}-{max(encounter_lengths)}")
            print("=" * 60)
            
        except Exception as e:
            print(f"生成统计信息时出错: {e}")


def main():
    """主函数：演示可视化功能"""
    print("🚢 开始会遇场景轨迹可视化...")
    
    # 确保可视化目录存在
    os.makedirs('vis', exist_ok=True)
    
    try:
        # 初始化可视化器
        visualizer = EncounterTrajectoryVisualizer()
        
        # 生成统计摘要
        visualizer.generate_summary_statistics()
        
        # 可视化所有场景（保存到文件，不显示，使用底图）
        visualizer.visualize_all_scenarios(show_plots=False, use_map=True)
        
        # 生成对比图（使用底图）
        visualizer.visualize_scenario_comparison(use_map=True)
        
        # 可视化第一个场景作为示例（不显示，使用底图）
        if visualizer.results:
            first_key = list(visualizer.results.keys())[0]
            print(f"\n生成示例场景: {first_key}")
            visualizer.visualize_single_scenario(first_key, 
                                               save_path=f'vis/example_{first_key}.png',
                                               use_map=True)
        
        print("\n🎉 可视化完成！")
        print("生成的文件:")
        print("- vis/encounter_scenarios/ (各场景详细图，含底图)")
        print("- vis/encounter_comparison.png (对比分析图，含底图)")
        print("- vis/example_*.png (示例场景图，含底图)")
        
    except FileNotFoundError as e:
        print(f"❌ 错误: {e}")
        print("请先运行 11.船舶避让场景提取.py 生成随机会遇轨迹数据")
    except Exception as e:
        print(f"❌ 可视化过程中出现错误: {e}")
    finally:
        # 确保关闭所有matplotlib图形
        plt.close('all')


if __name__ == '__main__':
    main() 