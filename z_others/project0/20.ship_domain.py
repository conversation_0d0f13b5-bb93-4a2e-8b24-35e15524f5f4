"""
基于避让场景的船舶安全域建模 - 按船长分类版
依赖于11.船舶避让场景提取.py的输出结果
分别拟合穿越船和非穿越船的安全域，并按船长区间细化
船长区间：0-50m, 50-100m, 100-150m, 150-200m, 200m+

https://ieeexplore.ieee.org/document/10288364
"""

import os
import pickle
import warnings
import math

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import leastsq
from tqdm import tqdm

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class LengthBasedSafetyDomainModel:
    """
    基于船长分类的船舶安全域建模
    - 穿越船安全域：基于交叉会遇，以机动的穿越船为本船，按船长分类
    - 非穿越船安全域：基于追越会遇，以机动的非穿越船为本船，按船长分类
    - 船长区间：0-50m, 50-100m, 100-150m, 150-200m, 200m+
    """

    def __init__(self, debug=False):
        # 调试开关：控制是否打印详细信息
        self.debug = debug

        # 船长区间定义
        self.length_intervals = [
            (0, 50, "0-50m"),
            (50, 100, "50-100m"),
            (100, 150, "100-150m"),
            (150, 200, "150-200m"),
            (200, float('inf'), "200m+")
        ]

        # 基础数据
        self.ship_classification = {}  # 动态船舶分类信息
        self.crossing_encounters = []  # 交叉会遇数据
        self.overtaking_encounters = []  # 追越会遇数据
        self.avoidance_scenarios = []  # 避让场景数据
        self.trajectory_data = None  # 轨迹数据

        # 按船长分类的会遇数据
        self.crossing_encounter_data_by_length = {}  # {interval_name: [encounter_data]}
        self.overtaking_encounter_data_by_length = {}  # {interval_name: [encounter_data]}

        # 按船长分类的安全域模型
        self.crossing_safety_domains = {}  # {interval_name: safety_domain}
        self.overtaking_safety_domains = {}  # {interval_name: safety_domain}

    def load_avoidance_results(self):
        """加载避让场景提取结果"""
        print("正在加载避让场景提取结果...")

        # 检查必要文件是否存在
        required_files = [
            'result/ship_classification.pkl',
            'result/crossing_encounters.pkl',
            'result/overtaking_encounters.pkl',
            'result/optimized_avoidance_scenarios.pkl'
        ]

        missing_files = [f for f in required_files if not os.path.exists(f)]
        if missing_files:
            print(f"❌ 缺少必要文件: {missing_files}")
            print("请先运行 11.船舶避让场景提取.py 生成所有结果文件")
            return False

        try:
            # 加载船舶分类信息
            with open('result/ship_classification.pkl', 'rb') as f:
                self.ship_classification = pickle.load(f)

            # 加载会遇检测结果
            with open('result/crossing_encounters.pkl', 'rb') as f:
                self.crossing_encounters = pickle.load(f)

            with open('result/overtaking_encounters.pkl', 'rb') as f:
                self.overtaking_encounters = pickle.load(f)

            # 加载避让场景
            with open('result/optimized_avoidance_scenarios.pkl', 'rb') as f:
                self.avoidance_scenarios = pickle.load(f)

            print("✅ 避让场景提取结果加载成功:")
            print(f"   交叉会遇场景: {len(self.crossing_encounters)} 个")
            print(f"   追越会遇场景: {len(self.overtaking_encounters)} 个")
            print(f"   避让场景: {len(self.avoidance_scenarios)} 个")

            # 显示船舶分类统计
            if 'cross_ship_time_tuples' in self.ship_classification:
                cross_ship_time_tuples = self.ship_classification['cross_ship_time_tuples']
                crossing_ships = len(set(mmsi for mmsi, _, _ in cross_ship_time_tuples))
                total_segments = len(cross_ship_time_tuples)
                print(f"   穿越船数量: {crossing_ships} (穿越时间段: {total_segments})")

            return True

        except Exception as e:
            print(f"❌ 加载避让场景结果失败: {e}")
            return False

    def load_trajectory_data(self):
        """加载轨迹数据"""
        print("正在加载轨迹数据...")

        try:
            # 尝试加载轨迹数据
            data_files = [
                'data/tras_2024_1_3_inter.parquet',
            ]

            for data_file in data_files:
                if os.path.exists(data_file):
                    self.trajectory_data = pd.read_parquet(data_file)
                    print(f"✅ 轨迹数据加载成功: {data_file}")
                    print(f"   数据量: {len(self.trajectory_data)} 条记录")
                    print(
                        f"   时间范围: {self.trajectory_data['PosTime'].min()} - {self.trajectory_data['PosTime'].max()}")
                    return True

            print("❌ 未找到轨迹数据文件")
            return False

        except Exception as e:
            print(f"❌ 轨迹数据加载失败: {e}")
            return False

    def _get_ship_data_from_trajectory(self, mmsi):
        """从轨迹数据中获取船舶基本信息（船长等固定属性）"""
        ship_records = self.trajectory_data[self.trajectory_data['MMSI'] == mmsi]
        if ship_records.empty:
            return None

        # 取第一条记录（船长等属性是固定的）
        record = ship_records.iloc[0]
        return {
            'mmsi': int(mmsi),
            'length': float(record['Length']) if pd.notna(record['Length']) else 100.0,
            'width': float(record['Width']) if pd.notna(record['Width']) else 15.0
        }

    def _extract_ship_data_from_scenario(self, scenario, ship_mmsi):
        """从避让场景和轨迹数据中组合提取完整的船舶数据"""
        # 1. 从避让场景获取位置和运动状态
        if ship_mmsi not in scenario['ship_details']:
            return None

        ship_detail = scenario['ship_details'][ship_mmsi]

        # 2. 从轨迹数据获取船舶固定属性
        ship_attributes = self._get_ship_data_from_trajectory(ship_mmsi)
        if not ship_attributes:
            return None

        # 3. 组合完整数据
        return {
            'mmsi': ship_mmsi,
            'lon': ship_detail['lon'],
            'lat': ship_detail['lat'],
            'sog': ship_detail['sog'],
            'cog': ship_detail['cog'],
            'length': ship_attributes['length'],
            'width': ship_attributes['width'],
            'pos_time': scenario['pos_time']
        }

    def extract_encounter_data(self, force_rebuild=False):
        """提取会遇数据并按船长分类"""
        print("\n正在提取会遇数据并按船长分类...")

        # 检查是否已存在缓存的会遇数据
        cache_file = 'result/length_based_encounter_data_cache.pkl'
        if not force_rebuild and os.path.exists(cache_file):
            print(f"检测到缓存文件，正在加载: {cache_file}")
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)

                self.crossing_encounter_data_by_length = cache_data['crossing_encounter_data_by_length']
                self.overtaking_encounter_data_by_length = cache_data['overtaking_encounter_data_by_length']

                print("✅ 缓存的会遇数据加载成功")
                self._print_length_classification_stats()
                return

            except Exception as e:
                print(f"❌ 加载缓存失败: {e}")
                print("将重新提取数据...")

        # 初始化按船长分类的数据结构
        for _, _, interval_name in self.length_intervals:
            self.crossing_encounter_data_by_length[interval_name] = []
            self.overtaking_encounter_data_by_length[interval_name] = []

        # 检查船长数据分布
        print("正在检查船长数据分布...")
        self._check_length_data_distribution()

        # 提取穿越船会遇数据 (交叉会遇场景)
        print("正在提取穿越船会遇数据并按船长分类...")
        self._extract_crossing_encounter_data_by_length()

        # 提取非穿越船会遇数据 (追越会遇场景)  
        print("正在提取非穿越船会遇数据并按船长分类...")
        self._extract_overtaking_encounter_data_by_length()

        # 统计结果
        self._print_length_classification_stats()

        # 保存会遇数据到缓存
        self._save_encounter_data_cache(cache_file)

    def _check_length_data_distribution(self):
        """检查船长数据分布"""
        length_data = self.trajectory_data['Length'] if 'Length' in self.trajectory_data.columns else None

        if length_data is None:
            print("❌ 警告: 轨迹数据中没有找到 'Length' 字段!")
            print(f"   可用字段: {list(self.trajectory_data.columns)}")
            return

        print(f"✅ 找到 Length 字段")

        # 统计 Length 数据
        total_records = len(length_data)
        nan_count = length_data.isna().sum()
        valid_count = total_records - nan_count

        print(f"   总记录数: {total_records}")
        print(f"   有效船长数据: {valid_count} ({valid_count / total_records * 100:.1f}%)")
        print(f"   缺失船长数据: {nan_count} ({nan_count / total_records * 100:.1f}%)")

        if valid_count > 0:
            valid_lengths = length_data.dropna()
            print(f"   船长范围: {valid_lengths.min():.1f} - {valid_lengths.max():.1f} 米")
            print(f"   船长均值: {valid_lengths.mean():.1f} 米")
            print(f"   船长中位数: {valid_lengths.median():.1f} 米")

            # 按我们的区间统计
            print(f"   按区间分布:")
            for min_len, max_len, interval_name in self.length_intervals:
                if max_len == float('inf'):
                    count = (valid_lengths >= min_len).sum()
                else:
                    count = ((valid_lengths >= min_len) & (valid_lengths < max_len)).sum()
                print(f"     {interval_name}: {count} ({count / valid_count * 100:.1f}%)")

    def _extract_crossing_encounter_data_by_length(self):
        """提取穿越船会遇数据并按船长分类"""
        # 从避让场景中筛选交叉会遇场景
        crossing_scenarios = [s for s in self.avoidance_scenarios if s['encounter_type'] == 'crossing']

        processed_count = 0
        valid_length_count = 0
        classification_count = {name: 0 for _, _, name in self.length_intervals}

        for scenario in tqdm(crossing_scenarios, desc="处理交叉会遇"):
            try:
                encounter_pair = scenario['encounter_pair']
                pos_time = scenario['pos_time']
                maneuvering_ships = scenario['maneuvering_ships']

                # 确定穿越船和主航道船
                if 'crossing_ship' in scenario and 'waterway_ship' in scenario:
                    crossing_ship = scenario['crossing_ship']
                    waterway_ship = scenario['waterway_ship']
                else:
                    # 如果没有明确标识，基于时间动态判断
                    crossing_ship = None
                    waterway_ship = None
                    for ship_id in encounter_pair:
                        if self._is_crossing_ship_at_time(ship_id, pos_time):
                            crossing_ship = ship_id
                        else:
                            waterway_ship = ship_id

                    if crossing_ship is None or waterway_ship is None:
                        continue

                # 只有当穿越船发生机动行为时才作为本船
                if crossing_ship in maneuvering_ships:
                    # 获取船舶状态数据
                    own_ship_data = self._extract_ship_data_from_scenario(scenario, crossing_ship)
                    other_ship_data = self._extract_ship_data_from_scenario(scenario, waterway_ship)

                    if own_ship_data and other_ship_data:
                        processed_count += 1
                        own_length = own_ship_data['length']

                        if self._validate_ship_data(own_ship_data, other_ship_data):
                            valid_length_count += 1
                            encounter_record = self._create_encounter_record(own_ship_data, other_ship_data)
                            encounter_record['scenario_type'] = 'crossing'
                            encounter_record['own_ship_type'] = 'crossing'

                            # 根据本船船长分类
                            interval_name = self._get_length_interval(own_length)
                            if interval_name:
                                self.crossing_encounter_data_by_length[interval_name].append(encounter_record)
                                classification_count[interval_name] += 1

            except Exception as e:
                continue

        print(f"   穿越船数据处理统计:")
        print(f"   - 处理的场景数: {processed_count}")
        print(f"   - 通过验证的: {valid_length_count}")
        print(f"   - 按船长分类结果: {classification_count}")

    def _extract_overtaking_encounter_data_by_length(self):
        """提取非穿越船会遇数据并按船长分类"""
        # 从避让场景中筛选追越会遇场景
        overtaking_scenarios = [s for s in self.avoidance_scenarios if s['encounter_type'] == 'overtaking']

        processed_count = 0
        valid_length_count = 0
        classification_count = {name: 0 for _, _, name in self.length_intervals}

        for scenario in tqdm(overtaking_scenarios, desc="处理追越会遇"):
            try:
                encounter_pair = scenario['encounter_pair']
                pos_time = scenario['pos_time']
                maneuvering_ships = scenario['maneuvering_ships']

                ship1_id, ship2_id = encounter_pair

                # 确保两船都是非穿越船（在该时刻）
                if (not self._is_crossing_ship_at_time(ship1_id, pos_time) and
                        not self._is_crossing_ship_at_time(ship2_id, pos_time)):

                    # 获取船舶状态数据
                    ship1_data = self._extract_ship_data_from_scenario(scenario, ship1_id)
                    ship2_data = self._extract_ship_data_from_scenario(scenario, ship2_id)

                    if ship1_data and ship2_data:
                        processed_count += 1

                        if self._validate_ship_data(ship1_data, ship2_data):
                            # 为每个发生机动的船舶创建记录（以其为本船）
                            if ship1_id in maneuvering_ships:
                                valid_length_count += 1
                                encounter_record = self._create_encounter_record(ship1_data, ship2_data)
                                encounter_record['scenario_type'] = 'overtaking'
                                encounter_record['own_ship_type'] = 'non_crossing'

                                # 根据本船船长分类
                                own_length = ship1_data['length']
                                interval_name = self._get_length_interval(own_length)
                                if interval_name:
                                    self.overtaking_encounter_data_by_length[interval_name].append(encounter_record)
                                    classification_count[interval_name] += 1

                            if ship2_id in maneuvering_ships:
                                valid_length_count += 1
                                encounter_record = self._create_encounter_record(ship2_data, ship1_data)
                                encounter_record['scenario_type'] = 'overtaking'
                                encounter_record['own_ship_type'] = 'non_crossing'

                                # 根据本船船长分类
                                own_length = ship2_data['length']
                                interval_name = self._get_length_interval(own_length)
                                if interval_name:
                                    self.overtaking_encounter_data_by_length[interval_name].append(encounter_record)
                                    classification_count[interval_name] += 1

            except Exception as e:
                continue

        print(f"   非穿越船数据处理统计:")
        print(f"   - 处理的场景数: {processed_count}")
        print(f"   - 通过验证的: {valid_length_count}")
        print(f"   - 按船长分类结果: {classification_count}")

    def _get_length_interval(self, length):
        """根据船长获取对应的区间名称"""
        for min_len, max_len, interval_name in self.length_intervals:
            if min_len <= length < max_len:
                return interval_name
        return None

    def _print_length_classification_stats(self):
        """打印按船长分类的统计信息"""
        print(f"\n✅ 按船长分类的会遇数据提取完成:")

        total_crossing = 0
        total_overtaking = 0

        for _, _, interval_name in self.length_intervals:
            crossing_count = len(self.crossing_encounter_data_by_length[interval_name])
            overtaking_count = len(self.overtaking_encounter_data_by_length[interval_name])

            total_crossing += crossing_count
            total_overtaking += overtaking_count

            print(f"   {interval_name}: 穿越船{crossing_count}个, 非穿越船{overtaking_count}个")

        print(f"   总计: 穿越船{total_crossing}个, 非穿越船{total_overtaking}个")

    def _save_encounter_data_cache(self, cache_file):
        """保存会遇数据到缓存文件"""
        print(f"正在保存会遇数据到缓存: {cache_file}")

        try:
            os.makedirs('result', exist_ok=True)

            cache_data = {
                'crossing_encounter_data_by_length': self.crossing_encounter_data_by_length,
                'overtaking_encounter_data_by_length': self.overtaking_encounter_data_by_length,
                'length_intervals': self.length_intervals,
                'cache_info': {
                    'total_crossing_scenarios': len(
                        [s for s in self.avoidance_scenarios if s['encounter_type'] == 'crossing']),
                    'total_overtaking_scenarios': len(
                        [s for s in self.avoidance_scenarios if s['encounter_type'] == 'overtaking']),
                    'cache_time': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            print("✅ 会遇数据缓存保存成功")

        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")

    def clear_encounter_data_cache(self):
        """清除会遇数据缓存文件"""
        cache_file = 'result/length_based_encounter_data_cache.pkl'
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
                print(f"✅ 缓存文件已删除: {cache_file}")
            except Exception as e:
                print(f"❌ 删除缓存文件失败: {e}")
        else:
            print(f"缓存文件不存在: {cache_file}")

    def build_safety_domains(self):
        """构建按船长分类的安全域模型"""
        print("\n正在构建按船长分类的安全域模型...")

        min_samples = 50  # 提高最少样本数要求，确保拟合可靠性

        # 构建穿越船安全域（按船长分类）
        print("正在构建穿越船安全域...")
        for _, _, interval_name in self.length_intervals:
            encounter_data = self.crossing_encounter_data_by_length[interval_name]

            if len(encounter_data) >= min_samples:
                print(f"  正在构建{interval_name}穿越船安全域 (样本数: {len(encounter_data)})...")
                df_crossing = self._encounters_to_dataframe(encounter_data)
                crossing_params = self._calculate_safety_domain(df_crossing)

                if crossing_params is not None:
                    self.crossing_safety_domains[interval_name] = {
                        'method': f'crossing_ship_ellipse_{interval_name}',
                        'sample_size': len(df_crossing),
                        'ellipse_params': crossing_params,
                        'raw_data': df_crossing,
                        'category': f'穿越船安全域({interval_name})',
                        'ship_type': 'crossing',
                        'length_interval': interval_name,
                        'scenario_type': 'crossing_encounter',
                        'length_range': (df_crossing['OwnLength'].min(), df_crossing['OwnLength'].max()),
                        'speed_range': (df_crossing['OwnSpeed'].min(), df_crossing['OwnSpeed'].max())
                    }
                    a, b = crossing_params['a'], crossing_params['b']
                    print(f"    ✅ {interval_name}穿越船安全域: 纵向{a:.0f}m × 横向{b:.0f}m")  # 保持简洁的结果显示
            else:
                if self.debug:
                    print(f"    ❌ {interval_name}穿越船数据不足 ({len(encounter_data)}个)，需要至少{min_samples}个样本")

        # 构建非穿越船安全域（按船长分类）
        print("正在构建非穿越船安全域...")
        for _, _, interval_name in self.length_intervals:
            encounter_data = self.overtaking_encounter_data_by_length[interval_name]

            if len(encounter_data) >= min_samples:
                print(f"  正在构建{interval_name}非穿越船安全域 (样本数: {len(encounter_data)})...")
                df_overtaking = self._encounters_to_dataframe(encounter_data)
                overtaking_params = self._calculate_safety_domain(df_overtaking)

                if overtaking_params is not None:
                    self.overtaking_safety_domains[interval_name] = {
                        'method': f'non_crossing_ship_ellipse_{interval_name}',
                        'sample_size': len(df_overtaking),
                        'ellipse_params': overtaking_params,
                        'raw_data': df_overtaking,
                        'category': f'非穿越船安全域({interval_name})',
                        'ship_type': 'non_crossing',
                        'length_interval': interval_name,
                        'scenario_type': 'overtaking_encounter',
                        'length_range': (df_overtaking['OwnLength'].min(), df_overtaking['OwnLength'].max()),
                        'speed_range': (df_overtaking['OwnSpeed'].min(), df_overtaking['OwnSpeed'].max())
                    }
                    a, b = overtaking_params['a'], overtaking_params['b']
                    print(f"    ✅ {interval_name}非穿越船安全域: 纵向{a:.0f}m × 横向{b:.0f}m")  # 保持简洁的结果显示
            else:
                if self.debug:
                    print(
                        f"    ❌ {interval_name}非穿越船数据不足 ({len(encounter_data)}个)，需要至少{min_samples}个样本")

    def save_models(self):
        """保存按船长分类的安全域模型"""
        os.makedirs('result', exist_ok=True)

        # 保存安全域模型
        safety_domains = {
            'crossing_safety_domains': self.crossing_safety_domains,
            'overtaking_safety_domains': self.overtaking_safety_domains,
            'length_intervals': self.length_intervals,
            'model_info': {
                'model_type': 'length_based_safety_domain',
                'length_classification': True,
                'interval_count': len(self.length_intervals),
                'total_crossing_encounters': len(self.crossing_encounters),
                'total_overtaking_encounters': len(self.overtaking_encounters),
                'total_avoidance_scenarios': len(self.avoidance_scenarios)
            },
            'data_summary': {
                'crossing_by_length': {name: len(data) for name, data in
                                       self.crossing_encounter_data_by_length.items()},
                'overtaking_by_length': {name: len(data) for name, data in
                                         self.overtaking_encounter_data_by_length.items()}
            }
        }

        with open('result/length_based_safety_domains.pkl', 'wb') as f:
            pickle.dump(safety_domains, f)

        print(f"✅ 按船长分类的安全域模型已保存: result/length_based_safety_domains.pkl")

    # 辅助方法
    def _is_crossing_ship_at_time(self, mmsi, pos_time):
        """判断指定船舶在特定时间是否为穿越船"""
        if 'cross_ship_time_tuples' not in self.ship_classification:
            return False

        cross_ship_time_tuples = self.ship_classification['cross_ship_time_tuples']

        # 检查该时间是否在任何穿越时间段内
        for ship_mmsi, start_time, end_time in cross_ship_time_tuples:
            if ship_mmsi == mmsi and start_time <= pos_time <= end_time:
                return True

        return False

    def _encounters_to_dataframe(self, encounters):
        """将会遇记录转换为DataFrame"""
        data = []
        for encounter in encounters:
            data.append({
                'RelativeX': encounter['relative_x'],
                'RelativeY': encounter['relative_y'],
                'Distance': encounter['distance'],
                'OwnLength': encounter['own_length'],
                'OwnSpeed': encounter['own_speed'],
                'OtherLength': encounter['other_length'],
                'OtherSpeed': encounter['other_speed']
            })
        return pd.DataFrame(data)

    def _calculate_safety_domain(self, df):
        """
        基于三阶段椭圆拟合的安全域计算（强制对称性约束）
        阶段1: 自适应异常值移除(AOR)
        阶段2: 极坐标角度采样 + 强制双向对称约束（使用较小范围）
        阶段3: 简化椭圆拟合
        """
        try:
            # print(f"    🔍 三阶段椭圆拟合 - 数据概览:")
            # print(f"       原始样本数量: {len(df)}")
            # print(f"       X范围: [{df['RelativeX'].min():.1f}, {df['RelativeX'].max():.1f}]")
            # print(f"       Y范围: [{df['RelativeY'].min():.1f}, {df['RelativeY'].max():.1f}]")
            # print(f"       距离范围: [{df['Distance'].min():.1f}, {df['Distance'].max():.1f}]")

            # 阶段1: 自适应异常值移除 (AOR)
            # print(f"    📍 阶段1: 自适应异常值移除...")
            cleaned_df = self._adaptive_outlier_removal(df)
            # print(f"       异常值移除后样本数量: {len(cleaned_df)} (移除了 {len(df) - len(cleaned_df)} 个异常值)")

            if len(cleaned_df) < 20:
                print("       ❌ 清理后数据点不足，无法拟合椭圆")
                return None

            # 阶段2: 极坐标角度采样
            # print(f"    📍 阶段2: 极坐标角度采样...")
            boundary_points = self._polar_angle_sampling(cleaned_df)
            # print(f"       提取边界点数量: {len(boundary_points)}")

            if len(boundary_points) < 8:
                print("       ❌ 边界点不足，无法拟合椭圆")
                return None

            # 阶段3: 简化椭圆拟合
            # print(f"    📍 阶段3: 简化椭圆拟合...")
            ellipse_params = self._simplified_ellipse_fitting(boundary_points)

            if ellipse_params is None:
                print("       ❌ 椭圆拟合失败")
                return None

            a, b = ellipse_params['a'], ellipse_params['b']
            print(f"    ✅ 拟合完成: a(纵向)={a:.0f}m, b(横向)={b:.0f}m")  # 始终显示最终结果

            return {
                'a': a,
                'b': b,
                'boundary_points': boundary_points,
                'center': (0, 0),
                'method': 'three_stage_robust_fitting_with_symmetry',
                'debug_info': {
                    'original_count': len(df),
                    'cleaned_count': len(cleaned_df),
                    'boundary_count': len(boundary_points),
                    'outliers_removed': len(df) - len(cleaned_df),
                    'stages_completed': 3,
                    'symmetry_constraints': 'front_back_left_right_enforced',
                    'constraint_method': 'smaller_range_boundary'
                }
            }

        except Exception as e:
            print(f"       ❌ 三阶段椭圆拟合失败: {e}")
            import traceback
            print(f"       详细错误: {traceback.format_exc()}")
            return None

    def _adaptive_outlier_removal(self, df):
        """
        阶段1: 自适应异常值移除 (AOR)
        基于概率密度动态移除异常值，无需手动参数调整
        """
        from sklearn.neighbors import KernelDensity

        # 提取坐标数据
        points = df[['RelativeX', 'RelativeY']].values

        # 数据标准化（用于密度估计）
        points_mean = np.mean(points, axis=0)
        points_std = np.std(points, axis=0)
        points_normalized = (points - points_mean) / (points_std + 1e-8)

        # 使用核密度估计计算每个点的概率密度
        bandwidth = max(0.1, min(1.0, len(points) ** (-1 / 6)))  # 自适应带宽
        kde = KernelDensity(bandwidth=bandwidth, kernel='gaussian')
        kde.fit(points_normalized)

        # 计算每个点的对数概率密度
        log_densities = kde.score_samples(points_normalized)
        densities = np.exp(log_densities)

        # 自适应阈值：基于密度分布的分位数
        # 移除密度最低的5-15%点（根据数据量自适应）
        removal_ratio = max(0.05, min(0.15, 50 / len(points)))  # 数据越多，移除比例越小
        density_threshold = np.percentile(densities, removal_ratio * 100)

        # 筛选数据
        mask = densities >= density_threshold
        cleaned_df = df[mask].copy()

        if self.debug:
            print(f"       移除比例: {removal_ratio:.1%}, 密度阈值: {density_threshold:.6f}")

        return cleaned_df

    def _polar_angle_sampling(self, df):
        """
        阶段2: 极坐标角度采样（强制双向对称约束）
        将点投影到极坐标系统，按角度均匀采样边界点
        强制前后对称（Y方向）和左右对称（X方向），使用较小范围作为约束边界
        """
        # 计算极坐标
        df_copy = df.copy()
        df_copy['Radius'] = np.sqrt(df_copy['RelativeX'] ** 2 + df_copy['RelativeY'] ** 2)
        df_copy['Angle'] = (np.degrees(np.arctan2(df_copy['RelativeY'], df_copy['RelativeX'])) + 360) % 360

        # 分析并强制前后对称（Y方向）
        y_positive = df_copy[df_copy['RelativeY'] > 0]['RelativeY']
        y_negative = df_copy[df_copy['RelativeY'] < 0]['RelativeY']

        if len(y_positive) > 0 and len(y_negative) > 0:
            max_positive = y_positive.max()
            max_negative_abs = abs(y_negative.min())

            if self.debug:
                print(f" Y方向分析: 正向最大={max_positive:.1f}, 负向最大={max_negative_abs:.1f}")

            # 强制前后对称
            symmetric_y_limit = min(max_positive, max_negative_abs)  # 使用较小值作为对称边界
            if self.debug:
                print(f"       强制前后对称: 使用较小范围±{symmetric_y_limit:.1f}")

            # 过滤超出对称范围的点
            df_copy = df_copy[abs(df_copy['RelativeY']) <= symmetric_y_limit].copy()
            if self.debug:
                print(f"       前后对称过滤后样本数量: {len(df_copy)}")

        # 分析并强制左右对称（X方向）
        x_positive = df_copy[df_copy['RelativeX'] > 0]['RelativeX']
        x_negative = df_copy[df_copy['RelativeX'] < 0]['RelativeX']

        if len(x_positive) > 0 and len(x_negative) > 0:
            max_x_positive = x_positive.max()
            max_x_negative_abs = abs(x_negative.min())

            if self.debug:
                print(f"       X方向分析: 右舷最大={max_x_positive:.1f}, 左舷最大={max_x_negative_abs:.1f}")

            # 强制左右对称
            symmetric_x_limit = min(max_x_positive, max_x_negative_abs)  # 使用较小值作为对称边界
            if self.debug:
                print(f"       强制左右对称: 使用较小范围±{symmetric_x_limit:.1f}")

            # 过滤超出对称范围的点
            df_copy = df_copy[abs(df_copy['RelativeX']) <= symmetric_x_limit].copy()
            if self.debug:
                print(f"       左右对称过滤后样本数量: {len(df_copy)}")

        boundary_points = []
        angle_step = 15  # 每15度一个扇区
        sector_distances = {}  # 记录每个扇区的距离，用于对称性检查

        for angle in range(0, 360, angle_step):
            angle_end = angle + angle_step

            # 处理跨越0度的情况
            if angle_end <= 360:
                mask = (df_copy['Angle'] >= angle) & (df_copy['Angle'] < angle_end)
            else:
                mask = (df_copy['Angle'] >= angle) | (df_copy['Angle'] < angle_end - 360)

            sector_data = df_copy[mask]

            if len(sector_data) > 0:
                # 选择该扇区内最近的几个点（边界采样）
                n_select = max(1, min(3, len(sector_data) // 10))  # 自适应选择数量
                nearest_points = sector_data.nsmallest(n_select, 'Radius')

                # 记录该扇区的平均距离（用于对称性约束）
                sector_distances[angle] = nearest_points['Radius'].mean()

                for _, point in nearest_points.iterrows():
                    boundary_points.append((point['RelativeX'], point['RelativeY']))

        # 应用前后对称性约束（主要针对Y轴方向）
        boundary_points = self._apply_symmetry_constraint(boundary_points)

        return boundary_points

    def _apply_symmetry_constraint(self, boundary_points):
        """
        强制前后对称约束（Y方向）
        使用船首和船尾方向的较小范围作为严格边界，确保椭圆前后对称
        """
        if len(boundary_points) < 8:
            return boundary_points

        X, Y = zip(*boundary_points)
        X, Y = np.array(X), np.array(Y)

        if self.debug:
            print(f"       边界点范围: X[{X.min():.1f}, {X.max():.1f}], Y[{Y.min():.1f}, {Y.max():.1f}]")

        # 分析前后对称性（Y轴方向）
        forward_points = [(x, y) for x, y in boundary_points if y > 0]  # 船首方向
        backward_points = [(x, y) for x, y in boundary_points if y < 0]  # 船尾方向

        if len(forward_points) > 0 and len(backward_points) > 0:
            forward_y = [y for _, y in forward_points]
            backward_y = [abs(y) for _, y in backward_points]

            max_forward = max(forward_y)
            max_backward = max(backward_y)

            if self.debug:
                print(f"       对称性分析: 船首最远={max_forward:.1f}, 船尾最远={max_backward:.1f}")

            # 强制前后对称：始终使用较小范围
            symmetric_limit = min(max_forward, max_backward)  # 使用较小值作为严格边界
            if self.debug:
                print(
                    f"       强制前后对称约束: ±{symmetric_limit:.1f} (船首最远={max_forward:.1f}, 船尾最远={max_backward:.1f})")

            # 过滤超出对称范围的所有点
            filtered_points = []
            for x, y in boundary_points:
                if abs(y) <= symmetric_limit:
                    filtered_points.append((x, y))

            if self.debug:
                print(f"       前后对称过滤: {len(boundary_points)} -> {len(filtered_points)} 个点")
            boundary_points = filtered_points

        # 应用左右对称性约束（X轴方向，船舶宽度方向）
        boundary_points = self._apply_lateral_symmetry(boundary_points)

        return boundary_points

    def _apply_lateral_symmetry(self, boundary_points):
        """
        强制左右对称约束（X方向）
        使用左舷和右舷方向的较小范围作为严格边界，确保椭圆左右对称
        """
        if len(boundary_points) < 6:
            return boundary_points

        X, Y = zip(*boundary_points)
        X, Y = np.array(X), np.array(Y)

        # 分析左右对称性
        left_points = [(x, y) for x, y in boundary_points if x < 0]  # 左舷
        right_points = [(x, y) for x, y in boundary_points if x > 0]  # 右舷

        if len(left_points) > 0 and len(right_points) > 0:
            left_x = [abs(x) for x, _ in left_points]
            right_x = [x for x, _ in right_points]

            max_left = max(left_x)
            max_right = max(right_x)

            # 强制左右对称：始终使用较小范围
            lateral_limit = min(max_left, max_right)  # 使用较小值作为严格边界
            if self.debug:
                print(
                    f"       强制左右对称约束: ±{lateral_limit:.1f} (左舷最远={max_left:.1f}, 右舷最远={max_right:.1f})")

            # 过滤超出对称范围的所有点
            filtered_points = []
            for x, y in boundary_points:
                if abs(x) <= lateral_limit:
                    filtered_points.append((x, y))

            if len(filtered_points) >= 6:  # 确保有足够的点进行拟合
                if self.debug:
                    print(f"       左右对称过滤: {len(boundary_points)} -> {len(filtered_points)} 个点")
                boundary_points = filtered_points
            else:
                if self.debug:
                    print(f"       左右对称过滤后点数不足({len(filtered_points)})，保持原边界点")

        return boundary_points

    def _simplified_ellipse_fitting(self, boundary_points):
        """
        阶段3: 简化椭圆拟合
        避免几何距离计算，使用代数距离和约束优化
        """
        from scipy.optimize import minimize

        if len(boundary_points) < 6:
            return None

        X, Y = zip(*boundary_points)
        X, Y = np.array(X), np.array(Y)

        if self.debug:
            print(f"边界点范围: X[{X.min():.1f}, {X.max():.1f}], Y[{Y.min():.1f}, {Y.max():.1f}]")

        # 简化的初始参数估计（更稳健）
        x_span = max(10, np.max(X) - np.min(X))
        y_span = max(10, np.max(Y) - np.min(Y))

        # 使用统计方法估计初始参数
        a_initial = max(20, y_span / 2)  # 纵向半轴
        b_initial = max(20, x_span / 2)  # 横向半轴

        if self.debug:
            print(f"初始参数: a={a_initial:.1f}, b={b_initial:.1f}")

        # 简化的目标函数（代数距离）
        def simplified_objective(params):
            a, b = abs(params[0]), abs(params[1])

            # 避免除零
            if a < 5 or b < 5:
                return 1e10

            # 代数距离（避免几何距离的复杂计算）
            residuals = (X / b) ** 2 + (Y / a) ** 2 - 1

            # L2范数 + 正则化
            objective = np.sum(residuals ** 2)

            # 添加物理约束正则化
            # 1. 参数应该在合理范围内
            if a > 2000 or b > 2000:
                objective += (a / 1000) ** 2 + (b / 1000) ** 2

            # 2. 椭圆不应过于细长（船舶安全域通常不会是极细长的椭圆）
            aspect_ratio = max(a / b, b / a)
            if aspect_ratio > 10:
                objective += (aspect_ratio - 10) ** 2

            return objective

        # 约束优化
        bounds = [(10, 2000), (10, 2000)]  # a和b的物理约束

        try:
            # 尝试多种优化方法
            methods = ['L-BFGS-B', 'SLSQP', 'TNC']
            best_result = None
            best_objective = float('inf')

            for method in methods:
                try:
                    result = minimize(simplified_objective, [a_initial, b_initial],
                                      bounds=bounds, method=method)

                    if result.success and result.fun < best_objective:
                        best_result = result
                        best_objective = result.fun

                except:
                    continue

            if best_result is not None and best_result.success:
                a, b = abs(best_result.x[0]), abs(best_result.x[1])
                if self.debug:
                    print(f"优化成功: a={a:.1f}, b={b:.1f}, 残差={best_result.fun:.3f}")

                # 最终合理性检查
                if 10 <= a <= 2000 and 10 <= b <= 2000:
                    return {'a': a, 'b': b, 'residual': best_result.fun}

            # 如果优化失败，使用鲁棒的备用方法
            if self.debug:
                print(f"优化失败，使用备用的矩估计方法...")
            return self._moment_based_ellipse_estimation(X, Y)

        except Exception as e:
            if self.debug:
                print(f"拟合异常: {e}")
            return self._moment_based_ellipse_estimation(X, Y)



    def _moment_based_ellipse_estimation(self, X, Y):
        """
        备用方法：基于统计矩的椭圆估计
        更稳健但精度略低的方法
        """
        try:
            # 计算二阶矩
            mean_x, mean_y = np.mean(X), np.mean(Y)

            # 中心化
            x_centered = X - mean_x
            y_centered = Y - mean_y

            # 计算协方差矩阵
            cov_xx = np.mean(x_centered ** 2)
            cov_yy = np.mean(y_centered ** 2)
            cov_xy = np.mean(x_centered * y_centered)

            # 特征值分解得到椭圆参数
            cov_matrix = np.array([[cov_xx, cov_xy], [cov_xy, cov_yy]])
            eigenvalues, _ = np.linalg.eigh(cov_matrix)

            # 椭圆半轴长度（使用2-sigma原则）
            a = max(20, 2 * np.sqrt(abs(eigenvalues[1])))  # 纵向
            b = max(20, 2 * np.sqrt(abs(eigenvalues[0])))  # 横向

            # 确保在合理范围内
            a = min(2000, a)
            b = min(2000, b)

            if self.debug:
                print(f"       矩估计结果: a={a:.1f}, b={b:.1f}")

            return {'a': a, 'b': b, 'residual': 0.0}

        except Exception as e:
            if self.debug:
                print(f"       矩估计也失败: {e}")
            # 最后的保底方法：基于数据范围的简单估计
            a = max(50, (np.max(Y) - np.min(Y)) / 2)
            b = max(50, (np.max(X) - np.min(X)) / 2)
            return {'a': min(2000, a), 'b': min(2000, b), 'residual': 0.0}

    @staticmethod
    def _ellipse_residual(params, x, y):
        """椭圆残差函数 - 改进数值稳定性（保留用于兼容性）"""
        a, b = abs(params[0]), abs(params[1])  # 确保参数为正

        # 避免除零和数值溢出
        if a < 1e-6 or b < 1e-6:
            return np.full_like(x, 1e10)

        # 椭圆方程残差
        residual = (x / b) ** 2 + (y / a) ** 2 - 1

        # 添加正则化项防止参数过大
        if a > 10000 or b > 10000:
            regularization = (a / 1000) ** 2 + (b / 1000) ** 2
            residual = residual + regularization

        return residual

    def _validate_ship_data(self, ship1, ship2):
        """验证船舶数据的有效性"""
        # 检查必要字段
        required_fields = ['length', 'sog', 'cog', 'lat', 'lon']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if ship[field] is None or pd.isna(ship[field]):
                    return False
        return True

    def _create_encounter_record(self, own_ship, other_ship):
        """创建会遇记录"""
        # 计算地理距离和相对位置
        dx, dy = self._calculate_relative_position(own_ship, other_ship)
        distance = np.sqrt(dx ** 2 + dy ** 2)

        # 转换到船舶坐标系
        relative_x, relative_y = self._convert_to_ship_coordinates(dx, dy, own_ship['cog'])

        return {
            'distance': distance,
            'own_length': own_ship['length'],
            'own_speed': own_ship['sog'],
            'other_length': other_ship['length'],
            'other_speed': other_ship['sog'],
            'relative_x': relative_x,
            'relative_y': relative_y,
            'own_mmsi': own_ship['mmsi'],
            'other_mmsi': other_ship['mmsi'],
            'pos_time': own_ship['pos_time']
        }

    def _calculate_distance(self, ship1, ship2):
        """计算两船间距离"""
        dx, dy = self._calculate_relative_position(ship1, ship2)
        return np.sqrt(dx ** 2 + dy ** 2)

    def _calculate_relative_position(self, ship1, ship2):
        """计算相对位置（米）"""
        lat1, lon1 = ship1['lat'], ship1['lon']
        lat2, lon2 = ship2['lat'], ship2['lon']

        # 转换为米制坐标
        dx = (lon2 - lon1) * 111000 * np.cos(np.radians((lat1 + lat2) / 2))
        dy = (lat2 - lat1) * 111000

        return dx, dy

    def _convert_to_ship_coordinates(self, dx, dy, heading):
        """转换到船舶坐标系"""
        # 参考正确实现：取负数是为了将艏向角转换为坐标旋转角
        theta = np.deg2rad(-heading)  # 航向角，0为正北，注意负号
        cos_angle = np.cos(theta)
        sin_angle = np.sin(theta)

        # 使用参考代码的正确公式
        ship_x = cos_angle * dx + sin_angle * dy  # 横向（右舷为正）
        ship_y = -sin_angle * dx + cos_angle * dy  # 纵向（船首为正）
        return round(ship_x, 2), round(ship_y, 2)




def main(debug=False):
    """主函数"""
    try:
        print("🚢 基于船长分类的船舶安全域建模")
        print("=" * 60)

        # 创建模型（传递debug参数）
        model = LengthBasedSafetyDomainModel(debug=debug)

        # 加载避让场景提取结果
        if not model.load_avoidance_results():
            print("❌ 无法加载避让场景结果，程序退出")
            return

        # 加载轨迹数据  
        if not model.load_trajectory_data():
            print("❌ 无法加载轨迹数据，程序退出")
            return

        # 提取会遇数据 (支持缓存)
        # 如需强制重新计算，可使用: model.extract_encounter_data(force_rebuild=True)
        # 如需清除缓存，可使用: model.clear_encounter_data_cache()
        model.extract_encounter_data(force_rebuild=True)

        # 构建安全域
        model.build_safety_domains()

        # 保存模型
        model.save_models()

        print(f"\n✅ 数据处理完成!")
        print(f"生成文件:")
        print(f"  result/length_based_safety_domains.pkl - 按船长分类的安全域模型")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 使用示例：
    # main(debug=True)   # 打印详细调试信息
    # main(debug=False)  # 仅打印最终结果（默认）
    main()
