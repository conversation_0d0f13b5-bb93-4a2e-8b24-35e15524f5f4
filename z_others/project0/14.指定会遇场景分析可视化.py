import os
import pickle
import warnings

import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
from geopy.distance import geodesic
import matplotlib.dates as mdates
from datetime import datetime, timedelta

# 忽略警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')


# 设置中文字体
def setup_chinese_font():
    """设置中文字体显示"""
    try:
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
        import matplotlib.font_manager as fm
        available_fonts = [f.name for f in fm.fontManager.ttflist]

        for font in chinese_fonts:
            if font in available_fonts:
                plt.rcParams['font.sans-serif'] = [font]
                print(f"[FONT] 使用字体: {font}")
                break
        else:
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
            print("[FONT] 使用英文字体")
    except:
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']


setup_chinese_font()
plt.rcParams['axes.unicode_minus'] = False


class EncounterAnalysisVisualizer:
    """会遇场景分析可视化器"""

    def __init__(self, results_path='result/random_encounter_tracks.pkl',
                 map_path='data/map0.png', geo_info_path='data/geo_info.pkl',
                 manual_extent=None):
        """
        初始化
        
        :param results_path: 结果文件路径
        :param map_path: 底图文件路径  
        :param geo_info_path: 地理信息文件路径
        :param manual_extent: 手动指定地图范围 [min_lon, max_lon, min_lat, max_lat]
                             例如: [120.0, 121.5, 31.0, 32.0]
        """
        self.results_path = results_path
        self.map_path = map_path
        self.geo_info_path = geo_info_path

        # 手动指定的地图范围优先级最高
        self.manual_extent = manual_extent

        self.results = self._load_results()
        self.map_img, self.map_extent = self._load_map_and_geo_info()

        # 创建保存目录
        self.save_dir = 'vis/encounter_analysis'
        os.makedirs(self.save_dir, exist_ok=True)

        # 颜色配置
        self.colors = {
            'overtaking': {'own': '#FF6B6B', 'encounter': '#4ECDC4'},
            'crossing': {'own': '#45B7D1', 'encounter': '#FFA07A'}
        }

    def _load_results(self):
        """加载结果数据"""
        with open(self.results_path, 'rb') as f:
            results = pickle.load(f)
        print(f"[DATA] 加载 {len(results)} 个会遇场景")
        return results

    def _load_map_and_geo_info(self):
        """加载底图和地理信息"""
        map_img = None
        map_extent = None

        # 加载底图
        if os.path.exists(self.map_path):
            try:
                map_img = Image.open(self.map_path)
                print(f"[MAP] ✅ 成功加载底图: {self.map_path}")
            except Exception as e:
                print(f"[MAP] ⚠️ 加载底图失败: {e}")
        else:
            print(f"[MAP] ⚠️ 底图文件不存在: {self.map_path}")

        # 优先使用手动指定的范围
        if self.manual_extent:
            map_extent = self.manual_extent
            print(f"[MAP] 🎯 使用手动指定的地图范围: {map_extent}")
        else:
            # 尝试从地理信息文件加载
            if os.path.exists(self.geo_info_path):
                try:
                    with open(self.geo_info_path, 'rb') as f:
                        geo_info = pickle.load(f)

                    print(f"[MAP] 📍 地理信息内容: {geo_info}")

                    # 尝试提取地理范围信息
                    if isinstance(geo_info, dict):
                        if 'extent' in geo_info:
                            map_extent = geo_info['extent']
                        elif 'bounds' in geo_info:
                            map_extent = geo_info['bounds']
                        elif all(key in geo_info for key in ['min_lon', 'max_lon', 'min_lat', 'max_lat']):
                            map_extent = [geo_info['min_lon'], geo_info['max_lon'],
                                          geo_info['min_lat'], geo_info['max_lat']]

                    if map_extent:
                        print(f"[MAP] ✅ 从geo_info加载地理范围: {map_extent}")
                    else:
                        print("[MAP] ⚠️ 无法从geo_info确定地理范围")

                except Exception as e:
                    print(f"[MAP] ⚠️ 加载地理信息失败: {e}")
            else:
                print(f"[MAP] ⚠️ 地理信息文件不存在: {self.geo_info_path}")

        return map_img, map_extent

    def set_manual_extent(self, min_lon, max_lon, min_lat, max_lat):
        """
        设置手动地图范围
        
        :param min_lon: 最小经度
        :param max_lon: 最大经度  
        :param min_lat: 最小纬度
        :param max_lat: 最大纬度
        """
        self.manual_extent = [min_lon, max_lon, min_lat, max_lat]
        self.map_extent = self.manual_extent
        print(f"[MAP] 🎯 更新手动指定的地图范围: {self.manual_extent}")

    def _setup_map_background(self, ax):
        """设置底图背景"""
        if self.map_img is not None and self.map_extent:
            # 显示底图
            ax.imshow(self.map_img, extent=self.map_extent, aspect='auto', alpha=0.7, zorder=0)
            print(f"[MAP] 🗺️ 底图已显示，范围: {self.map_extent}")

            # 设置坐标轴范围
            ax.set_xlim(self.map_extent[0], self.map_extent[1])
            ax.set_ylim(self.map_extent[2], self.map_extent[3])

            return self.map_extent
        else:
            if not self.map_img:
                print("[MAP] ⚠️ 未加载底图")
            if not self.map_extent:
                print("[MAP] ⚠️ 未设置地图范围")

        return None

    def list_scenarios(self):
        """列出所有场景"""
        print("\n[SCENARIOS] 可用场景:")
        print("=" * 60)

        for i, (key, data) in enumerate(self.results.items()):
            encounter_type = data['encounter_type']
            maneuver_mmsi = data['maneuver_mmsi']
            encounter_mmsi = data['encounter_mmsi']
            type_chinese = '追越' if encounter_type == 'overtaking' else '交叉'

            print(f"{i + 1:2d}. {key}")
            print(f"    类型: {type_chinese} | 机动船: {maneuver_mmsi} | 会遇船: {encounter_mmsi}")

    def get_filtered_tracks(self, scenario_data, center_event='encounter', minutes_before=1, minutes_after=1):
        """获取过滤后的轨迹数据"""
        own_track = scenario_data['own_track'].copy()
        encounter_track = scenario_data['encounter_track'].copy()
        
        # 直接使用绝对秒时间
        if 'time' in own_track.columns:
            own_time_col = 'time'
        else:
            # 如果没有time列，从PosTime转换为绝对秒
            own_time_col = 'time_seconds'
            own_track[own_time_col] = own_track['PosTime'].astype('int64')
        
        if 'time' in encounter_track.columns:
            encounter_time_col = 'time'
        else:
            encounter_time_col = 'time_seconds'
            encounter_track[encounter_time_col] = encounter_track['PosTime'].astype('int64')
        
        # 获取事件时间（绝对秒）
        maneuver_time_seconds = scenario_data['maneuver_time']
        encounter_time_seconds = scenario_data['encounter_time']
        
        # 确定中心时间
        if center_event == 'encounter':
            center_time_seconds = encounter_time_seconds
            event_name = '会遇时刻'
        else:
            center_time_seconds = maneuver_time_seconds
            event_name = '机动时刻'
        
        print(f"[FILTER] 中心事件: {event_name}, 绝对秒: {center_time_seconds}")
        
        # 计算时间窗口（绝对秒）
        seconds_before = minutes_before * 60
        seconds_after = minutes_after * 60
        start_time_seconds = center_time_seconds - seconds_before
        end_time_seconds = center_time_seconds + seconds_after
        
        print(f"[FILTER] 时间窗口: {start_time_seconds} 到 {end_time_seconds} (绝对秒)")
        
        # 直接用绝对秒筛选轨迹
        own_filtered = own_track[
            (own_track[own_time_col] >= start_time_seconds) & 
            (own_track[own_time_col] <= end_time_seconds)
        ].copy()
        
        encounter_filtered = encounter_track[
            (encounter_track[encounter_time_col] >= start_time_seconds) & 
            (encounter_track[encounter_time_col] <= end_time_seconds)
        ].copy()
        
        print(f"[FILTER] 机动船轨迹: {len(own_filtered)} 点")
        print(f"[FILTER] 会遇船轨迹: {len(encounter_filtered)} 点")
        
        return own_filtered, encounter_filtered, event_name, own_time_col, encounter_time_col

    def calculate_distances(self, own_track, encounter_track, own_time_col, encounter_time_col):
        """计算两船距离和对应的时间"""
        distances = []
        timestamps = []
        
        print(f"[DISTANCE] 开始计算距离...")
        
        # 取较短轨迹的长度
        min_length = min(len(own_track), len(encounter_track))
        
        for i in range(min_length):
            try:
                own_pos = own_track.iloc[i]
                encounter_pos = encounter_track.iloc[i]
                
                coord1 = (own_pos['Lat'], own_pos['Lon'])
                coord2 = (encounter_pos['Lat'], encounter_pos['Lon'])
                distance_m = geodesic(coord1, coord2).meters
                
                distances.append(distance_m)
                # 使用机动船的时间作为参考时间
                timestamps.append(own_pos[own_time_col])
                
            except Exception as e:
                print(f"[DISTANCE] 计算第{i}个点失败: {e}")
                distances.append(np.nan)
                if i < len(own_track):
                    timestamps.append(own_track.iloc[i][own_time_col])
                else:
                    timestamps.append(np.nan)
        
        print(f"[DISTANCE] 计算完成，共 {len(distances)} 个点")
        if distances:
            valid_distances = [d for d in distances if not np.isnan(d)]
            if valid_distances:
                print(f"[DISTANCE] 距离范围: {min(valid_distances):.0f} - {max(valid_distances):.0f} 米")
        
        return distances, timestamps

    def create_trajectory_plot(self, scenario_data, own_track, encounter_track, save_prefix):
        """创建轨迹图"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        encounter_type = scenario_data['encounter_type']
        colors = self.colors[encounter_type]
        
        # 设置底图背景
        map_extent = self._setup_map_background(ax)
        
        # 绘制轨迹
        ax.plot(own_track['Lon'], own_track['Lat'],
                color=colors['own'], linewidth=3, alpha=0.9,
                label=f'{scenario_data["maneuver_mmsi"]}', zorder=5)

        ax.plot(encounter_track['Lon'], encounter_track['Lat'],
                color=colors['encounter'], linewidth=3, alpha=0.9,
                label=f'{scenario_data["encounter_mmsi"]}', zorder=5)

        # 设置标题和标签
        encounter_type_chinese = '追越' if encounter_type == 'overtaking' else '交叉'
        ax.set_title(f'{encounter_type_chinese}会遇场景 - 轨迹图\n'
                     f'场景{scenario_data["scenario_id"]:02d} | '
                     f'机动船: {scenario_data["maneuver_mmsi"]} | 会遇船: {scenario_data["encounter_mmsi"]}',
                     fontsize=14, fontweight='bold')
        ax.set_xlabel('经度 (°)', fontsize=12)
        ax.set_ylabel('纬度 (°)', fontsize=12)
        
        # 美化样式
        # 设置刻度朝内
        ax.tick_params(direction='in', which='both')
        # 设置网格为灰色虚线
        ax.grid(True, linestyle='--', color='gray', alpha=0.5)

        ax.legend(fontsize=10, loc='upper right')
        ax.set_aspect('equal', adjustable='box')
        
        # 保存
        trajectory_path = os.path.join(self.save_dir, f"{save_prefix}_trajectory.png")
        plt.savefig(trajectory_path, dpi=300, bbox_inches='tight')
        print(f"[SUCCESS] 轨迹图保存: {trajectory_path}")
        
        plt.close()

    def create_distance_plot(self, distances, timestamps, scenario_data, event_name, save_prefix, 
                       minutes_before, minutes_after):
        """创建距离图"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        
        if not distances:
            ax.text(0.5, 0.5, '无距离数据', ha='center', va='center', transform=ax.transAxes)
            plt.close()
            return
        
        # 将时间戳转换为datetime对象
        datetime_list = []
        for ts in timestamps:
            if not np.isnan(ts):
                # 假设时间戳是Unix时间戳（秒）
                dt = datetime.fromtimestamp(ts)
                datetime_list.append(dt)
            else:
                datetime_list.append(None)
        
        # 绘制距离曲线
        valid_mask = ~np.isnan(distances)
        valid_indices = np.where(valid_mask)[0]
        
        if len(valid_indices) > 0:
            valid_distances = np.array(distances)[valid_mask]
            valid_datetimes = [datetime_list[i] for i in valid_indices if datetime_list[i] is not None]
            
            if len(valid_datetimes) == len(valid_distances):
                ax.plot(valid_datetimes, valid_distances, 
                       color='#2E86AB', linewidth=2, alpha=0.8)
                
                # 标记最近距离点
                min_idx = np.argmin(valid_distances)
                min_time = valid_datetimes[min_idx]
                min_distance = valid_distances[min_idx]
                
                ax.scatter(min_time, min_distance, color='red', s=60, marker='o', 
                          edgecolor='white', linewidth=1, zorder=6)
                
                # 设置x轴时间格式
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
                ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=max(1, (minutes_before + minutes_after) // 10)))
                
                # 旋转x轴标签以避免重叠
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

        # 设置标题和标签
        encounter_type = scenario_data['encounter_type']
        encounter_type_chinese = '追越' if encounter_type == 'overtaking' else '交叉'
        
        ax.set_title(f'{encounter_type_chinese}会遇场景 - 船舶间距离变化\n'
                f'场景{scenario_data["scenario_id"]:02d} | '
                f'机动船: {scenario_data["maneuver_mmsi"]} | 会遇船: {scenario_data["encounter_mmsi"]}',
                fontsize=14, fontweight='bold')
        ax.set_xlabel('绝对时间 (时:分:秒)', fontsize=12)
        ax.set_ylabel('距离 (米)', fontsize=12)
        
        # 美化样式
        # 设置刻度朝内
        ax.tick_params(direction='in', which='both')
        # 设置网格为灰色虚线
        ax.grid(True, linestyle='--', color='gray', alpha=0.5)
        
        # 保存
        distance_path = os.path.join(self.save_dir, f"{save_prefix}_distance.png")
        plt.savefig(distance_path, dpi=300, bbox_inches='tight')
        print(f"[SUCCESS] 距离图保存: {distance_path}")
        
        plt.close()

    def analyze_scenario(self, scenario_key, center_event='encounter', 
                    minutes_before=1, minutes_after=1, save_prefix=None):
        """分析指定场景"""
        if scenario_key not in self.results:
            print(f"[ERROR] 未找到场景: {scenario_key}")
            return
        
        data = self.results[scenario_key]
        print(f"\n[ANALYSIS] 分析场景: {scenario_key}")
        
        # 获取过滤后的轨迹（修改后返回时间列信息）
        own_filtered, encounter_filtered, event_name, own_time_col, encounter_time_col = self.get_filtered_tracks(
            data, center_event, minutes_before, minutes_after)
        
        if own_filtered.empty or encounter_filtered.empty:
            print("[ERROR] 过滤后轨迹为空")
            return
        
        # 计算距离（修改后返回时间戳）
        distances, timestamps = self.calculate_distances(own_filtered, encounter_filtered, own_time_col, encounter_time_col)
        
        # 生成保存前缀
        if save_prefix is None:
            save_prefix = scenario_key
        
        # 创建图表
        self.create_trajectory_plot(data, own_filtered, encounter_filtered, save_prefix)
        self.create_distance_plot(distances, timestamps, data, event_name, save_prefix, 
                             minutes_before, minutes_after)
        
        print(f"[SUCCESS] 场景 {scenario_key} 分析完成")

    def analyze_by_index(self, scenario_index, center_event='encounter',
                         minutes_before=1, minutes_after=1):
        """通过索引分析场景"""
        scenario_keys = list(self.results.keys())
        if 1 <= scenario_index <= len(scenario_keys):
            scenario_key = scenario_keys[scenario_index - 1]
            save_prefix = f"scenario_{scenario_index:02d}_{scenario_key}"

            self.analyze_scenario(scenario_key, center_event, minutes_before, minutes_after, save_prefix)
        else:
            print(f"[ERROR] 索引超出范围，可用范围: 1-{len(scenario_keys)}")


def main():
    """主函数"""
    print("[START] 会遇场景分析可视化工具")

    try:
        # 手动指定地图范围 - 根据你的实际水域调整这些数值
        manual_extent = [121.05, 121.35, 31.516, 31.784]  # [min_lon, max_lon, min_lat, max_lat]
        analyzer = EncounterAnalysisVisualizer(manual_extent=manual_extent)

        print(f"[MAP] 当前使用的地图范围: {analyzer.map_extent}")

        # 如果需要修改范围，可以使用：
        # analyzer.set_manual_extent(120.8, 121.3, 31.4, 31.9)

        # 列出场景
        analyzer.list_scenarios()

        # 分析示例
        if analyzer.results:
            print(f"\n[DEMO] 分析第1个场景")
            analyzer.analyze_by_index(
                scenario_index=90,
                center_event='encounter',
                minutes_before=10,
                minutes_after=10
            )

            print("\n[INFO] 使用说明:")
            print("1. analyzer.list_scenarios() - 查看所有场景")
            print("2. analyzer.analyze_by_index(索引, center_event, 前后分钟数)")
            print("3. analyzer.set_manual_extent(min_lon, max_lon, min_lat, max_lat) - 设置地图范围")
            print("4. center_event: 'encounter'(会遇时刻) 或 'maneuver'(机动时刻)")
            print("5. 结果保存到 vis/encounter_analysis/ 目录")

    except Exception as e:
        print(f"[ERROR] 分析失败: {e}")


if __name__ == '__main__':
    main()
