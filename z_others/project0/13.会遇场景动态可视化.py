import os
import pickle
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
from matplotlib.patches import Circle
from matplotlib.lines import Line2D
import seaborn as sns
import matplotlib
import warnings
from PIL import Image
import pandas as pd
from datetime import datetime, timedelta

# 忽略字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

# 设置中文字体和样式 - 更通用的字体配置
def setup_chinese_font():
    """设置中文字体显示"""
    try:
        # 尝试不同的中文字体
        chinese_fonts = [
            'Microsoft YaHei',  # 微软雅黑
            'SimHei',           # 黑体
            'SimSun',           # 宋体
            'KaiTi',            # 楷体
            'FangSong',         # 仿宋
            'STSong',           # 华文宋体
            'STKaiti',          # 华文楷体
            'STHeiti',          # 华文黑体
            'Arial Unicode MS', # Arial Unicode
            'DejaVu Sans'       # 备选
        ]
        
        import matplotlib.font_manager as fm
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        # 找到第一个可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break
        
        if selected_font:
            plt.rcParams['font.sans-serif'] = [selected_font]
            print(f"✅ 使用字体: {selected_font}")
        else:
            # 如果没有找到中文字体，使用系统默认字体并禁用unicode minus处理
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
            print("⚠️  未找到中文字体，将使用英文显示")
            
    except Exception as e:
        print(f"⚠️  字体设置失败: {e}，使用默认字体")
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']

# 设置字体
setup_chinese_font()
plt.rcParams['axes.unicode_minus'] = False

sns.set_style("whitegrid")

class AnimatedEncounterVisualizer:
    """
    会遇场景动态轨迹可视化器
    用于生成船舶会遇场景的动画
    """
    
    def __init__(self, results_path='result/random_encounter_tracks.pkl', 
                 map_path='data/map0.png', geo_info_path='data/geo_info.pkl'):
        """
        初始化动态可视化器
        
        :param results_path: 随机会遇轨迹结果文件路径
        :param map_path: 底图文件路径
        :param geo_info_path: 地理信息文件路径
        """
        self.results_path = results_path
        self.map_path = map_path
        self.geo_info_path = geo_info_path
        
        self.results = self._load_results()
        self.map_img, self.map_extent = self._load_map_and_geo_info()
        
        self.colors = {
            'overtaking': {'own': '#FF6B6B', 'encounter': '#4ECDC4'},
            'crossing': {'own': '#45B7D1', 'encounter': '#FFA07A'}
        }
        
        # 动画相关参数
        self.animation = None
        self.fig = None
        self.ax = None
        self.current_scenario_data = None
        
    def _load_results(self):
        """加载随机会遇轨迹结果"""
        if not os.path.exists(self.results_path):
            raise FileNotFoundError(f"未找到结果文件: {self.results_path}")
        
        with open(self.results_path, 'rb') as f:
            results = pickle.load(f)
        
        print(f"✅ 成功加载 {len(results)} 个会遇场景")
        return results
    
    def _load_map_and_geo_info(self):
        """加载底图和地理信息"""
        map_img = None
        map_extent = None
        
        # 加载底图
        if os.path.exists(self.map_path):
            try:
                map_img = Image.open(self.map_path)
                print(f"✅ 成功加载底图: {self.map_path}")
            except Exception as e:
                print(f"⚠️  加载底图失败: {e}")
        else:
            print(f"⚠️  底图文件不存在: {self.map_path}")
        
        # 加载地理信息
        if os.path.exists(self.geo_info_path):
            try:
                with open(self.geo_info_path, 'rb') as f:
                    geo_info = pickle.load(f)
                    
                # 尝试提取地理范围信息
                if isinstance(geo_info, dict):
                    if 'extent' in geo_info:
                        map_extent = geo_info['extent']
                    elif 'bounds' in geo_info:
                        map_extent = geo_info['bounds']
                    elif all(key in geo_info for key in ['min_lon', 'max_lon', 'min_lat', 'max_lat']):
                        map_extent = [geo_info['min_lon'], geo_info['max_lon'], 
                                    geo_info['min_lat'], geo_info['max_lat']]
                        
                if map_extent:
                    print(f"✅ 地理范围: {map_extent}")
                else:
                    print("⚠️  未能确定地理范围，将从轨迹数据推断")
                    
            except Exception as e:
                print(f"⚠️  加载地理信息失败: {e}")
        else:
            print(f"⚠️  地理信息文件不存在: {self.geo_info_path}")
        
        return map_img, map_extent
    
    def _get_complete_data_extent(self):
        """从所有轨迹数据中计算完整的地理范围"""
        all_lons = []
        all_lats = []
        
        # 收集所有场景的轨迹数据
        for data in self.results.values():
            own_track = data['own_track']
            encounter_track = data['encounter_track']
            
            if not own_track.empty:
                all_lons.extend(own_track['Lon'].dropna().tolist())
                all_lats.extend(own_track['Lat'].dropna().tolist())
            
            if not encounter_track.empty:
                all_lons.extend(encounter_track['Lon'].dropna().tolist())
                all_lats.extend(encounter_track['Lat'].dropna().tolist())
        
        if all_lons and all_lats:
            # 添加适当的边距确保所有数据都能显示
            margin_lon = (max(all_lons) - min(all_lons)) * 0.05
            margin_lat = (max(all_lats) - min(all_lats)) * 0.05
            
            extent = [
                min(all_lons) - margin_lon,  # min_lon
                max(all_lons) + margin_lon,  # max_lon
                min(all_lats) - margin_lat,  # min_lat
                max(all_lats) + margin_lat   # max_lat
            ]
            print(f"[MAP] 完整数据地理范围: {extent}")
            return extent
        
        return None
    
    def _get_data_extent(self, own_track, encounter_track):
        """从轨迹数据中计算地理范围"""
        all_lons = []
        all_lats = []
        
        if not own_track.empty:
            all_lons.extend(own_track['Lon'].tolist())
            all_lats.extend(own_track['Lat'].tolist())
        
        if not encounter_track.empty:
            all_lons.extend(encounter_track['Lon'].tolist())
            all_lats.extend(encounter_track['Lat'].tolist())
        
        if all_lons and all_lats:
            # 添加一些边距
            margin_lon = (max(all_lons) - min(all_lons)) * 0.1
            margin_lat = (max(all_lats) - min(all_lats)) * 0.1
            
            extent = [
                min(all_lons) - margin_lon,  # min_lon
                max(all_lons) + margin_lon,  # max_lon
                min(all_lats) - margin_lat,  # min_lat
                max(all_lats) + margin_lat   # max_lat
            ]
            return extent
        
        return None
    
    def _get_fixed_map_extent(self):
        """获取固定的地图显示范围"""
        # 优先使用地理信息文件中的范围
        if self.map_extent:
            print(f"[MAP] 使用地理信息文件范围: {self.map_extent}")
            return self.map_extent
        
        # 如果没有地理信息，使用所有数据的完整范围
        complete_extent = self._get_complete_data_extent()
        if complete_extent:
            print(f"[MAP] 使用完整数据范围: {complete_extent}")
            return complete_extent
        
        # 如果都没有，返回None
        print("[MAP] 无法确定地图范围")
        return None
    
    def _setup_map_background(self, ax, own_track=None, encounter_track=None):
        """设置底图背景"""
        # 获取固定的地图范围
        extent = self._get_fixed_map_extent()
        
        if self.map_img is not None and extent:
            # 显示底图
            ax.imshow(self.map_img, extent=extent, aspect='auto', alpha=0.6, zorder=0)
            print(f"[MAP] 底图已显示，固定范围: {extent}")
            return extent
        elif extent:
            # 即使没有底图，也使用固定范围
            print(f"[MAP] 使用固定坐标范围（无底图）: {extent}")
            return extent
        
        return None
    
    def _prepare_animation_data(self, scenario_key):
        """准备动画数据"""
        if scenario_key not in self.results:
            raise ValueError(f"未找到场景: {scenario_key}")
        
        data = self.results[scenario_key]
        own_track = data['own_track'].copy()
        encounter_track = data['encounter_track'].copy()
        
        if own_track.empty or encounter_track.empty:
            raise ValueError(f"场景 {scenario_key} 轨迹数据为空")
        
        print(f"🔍 场景 {scenario_key} 数据检查:")
        print(f"   本船轨迹点数: {len(own_track)}")
        print(f"   他船轨迹点数: {len(encounter_track)}")
        print(f"   本船列名: {list(own_track.columns)}")
        print(f"   他船列名: {list(encounter_track.columns)}")
        
        # 确保必要的列存在
        required_cols = ['PosTime', 'Lon', 'Lat']
        for col in required_cols:
            if col not in own_track.columns:
                raise ValueError(f"本船轨迹缺少必要列: {col}")
            if col not in encounter_track.columns:
                raise ValueError(f"他船轨迹缺少必要列: {col}")
        
        # 确保时间列是datetime格式
        try:
            if not pd.api.types.is_datetime64_any_dtype(own_track['PosTime']):
                own_track['PosTime'] = pd.to_datetime(own_track['PosTime'])
            if not pd.api.types.is_datetime64_any_dtype(encounter_track['PosTime']):
                encounter_track['PosTime'] = pd.to_datetime(encounter_track['PosTime'])
        except Exception as e:
            print(f"⚠️  时间格式转换失败: {e}")
            print(f"   本船时间样例: {own_track['PosTime'].iloc[0] if not own_track.empty else 'N/A'}")
            print(f"   他船时间样例: {encounter_track['PosTime'].iloc[0] if not encounter_track.empty else 'N/A'}")
            raise
        
        # 按时间排序
        own_track = own_track.sort_values('PosTime').reset_index(drop=True)
        encounter_track = encounter_track.sort_values('PosTime').reset_index(drop=True)
        
        # 获取时间范围 - 更安全的方式
        try:
            own_times = own_track['PosTime'].dropna()
            encounter_times = encounter_track['PosTime'].dropna()
            
            if own_times.empty and encounter_times.empty:
                raise ValueError("没有有效的时间数据")
            
            all_times = pd.concat([own_times, encounter_times], ignore_index=True).sort_values().unique()
            
            if len(all_times) == 0:
                raise ValueError("合并后没有有效时间点")
                
        except Exception as e:
            print(f"⚠️  时间数据处理失败: {e}")
            raise
        
        print(f"   时间范围: {all_times[0]} 到 {all_times[-1]}")
        print(f"   总时间点数: {len(all_times)}")
        
        # 为每个时间点准备数据
        animation_data = []
        
        for i, time_point in enumerate(all_times):
            try:
                frame_data = {'time': time_point, 'frame_idx': i}
                
                # 获取本船在该时间点的位置
                own_at_time = own_track[own_track['PosTime'] <= time_point]
                if not own_at_time.empty:
                    latest_own = own_at_time.iloc[-1]
                    frame_data['own_pos'] = (float(latest_own['Lon']), float(latest_own['Lat']))
                    frame_data['own_sog'] = float(latest_own.get('SOG', 0))
                    frame_data['own_cog'] = float(latest_own.get('COG', 0))
                else:
                    frame_data['own_pos'] = None
                    frame_data['own_sog'] = 0
                    frame_data['own_cog'] = 0
                
                # 获取他船在该时间点的位置
                encounter_at_time = encounter_track[encounter_track['PosTime'] <= time_point]
                if not encounter_at_time.empty:
                    latest_encounter = encounter_at_time.iloc[-1]
                    frame_data['encounter_pos'] = (float(latest_encounter['Lon']), float(latest_encounter['Lat']))
                    frame_data['encounter_sog'] = float(latest_encounter.get('SOG', 0))
                    frame_data['encounter_cog'] = float(latest_encounter.get('COG', 0))
                else:
                    frame_data['encounter_pos'] = None
                    frame_data['encounter_sog'] = 0
                    frame_data['encounter_cog'] = 0
                
                # 获取到当前时间的轨迹
                frame_data['own_track_so_far'] = own_track[own_track['PosTime'] <= time_point].copy()
                frame_data['encounter_track_so_far'] = encounter_track[encounter_track['PosTime'] <= time_point].copy()
                
                # 标记关键时间点
                try:
                    maneuver_time = pd.to_datetime(data['maneuver_time'])
                    encounter_time = pd.to_datetime(data['encounter_time'])
                    frame_data['is_maneuver_time'] = abs((time_point - maneuver_time).total_seconds()) < 60  # 1分钟容差
                    frame_data['is_encounter_time'] = abs((time_point - encounter_time).total_seconds()) < 60  # 1分钟容差
                except Exception as e:
                    print(f"⚠️  关键时间点处理失败: {e}")
                    frame_data['is_maneuver_time'] = False
                    frame_data['is_encounter_time'] = False
                
                animation_data.append(frame_data)
                
            except Exception as e:
                print(f"⚠️  处理时间点 {i} ({time_point}) 时出错: {e}")
                continue
        
        if not animation_data:
            raise ValueError("没有生成有效的动画数据")
        
        print(f"✅ 成功生成 {len(animation_data)} 帧动画数据")
        return animation_data, data
    
    def _update_frame(self, frame_idx, animation_data, colors):
        """更新动画帧"""
        try:
            if frame_idx >= len(animation_data):
                print(f"⚠️  帧索引 {frame_idx} 超出范围 {len(animation_data)}")
                return []
            
            frame_data = animation_data[frame_idx]
            
            # 更新轨迹
            own_track_so_far = frame_data['own_track_so_far']
            encounter_track_so_far = frame_data['encounter_track_so_far']
            
            # 安全地更新本船轨迹
            if not own_track_so_far.empty and 'Lon' in own_track_so_far.columns and 'Lat' in own_track_so_far.columns:
                lons = own_track_so_far['Lon'].dropna().values
                lats = own_track_so_far['Lat'].dropna().values
                if len(lons) > 0 and len(lats) > 0 and len(lons) == len(lats):
                    self.own_track_line.set_data(lons, lats)
                else:
                    self.own_track_line.set_data([], [])
            else:
                self.own_track_line.set_data([], [])
            
            # 安全地更新他船轨迹
            if not encounter_track_so_far.empty and 'Lon' in encounter_track_so_far.columns and 'Lat' in encounter_track_so_far.columns:
                lons = encounter_track_so_far['Lon'].dropna().values
                lats = encounter_track_so_far['Lat'].dropna().values
                if len(lons) > 0 and len(lats) > 0 and len(lons) == len(lats):
                    self.encounter_track_line.set_data(lons, lats)
                else:
                    self.encounter_track_line.set_data([], [])
            else:
                self.encounter_track_line.set_data([], [])
            
            # 更新当前位置 - 修复数组维度问题
            if frame_data['own_pos'] and len(frame_data['own_pos']) == 2:
                # 确保传递的是正确的2D数组格式
                pos = np.array([[float(frame_data['own_pos'][0]), float(frame_data['own_pos'][1])]])
                self.own_current_pos.set_offsets(pos)
            else:
                self.own_current_pos.set_offsets(np.empty((0, 2)))
            
            if frame_data['encounter_pos'] and len(frame_data['encounter_pos']) == 2:
                # 确保传递的是正确的2D数组格式
                pos = np.array([[float(frame_data['encounter_pos'][0]), float(frame_data['encounter_pos'][1])]])
                self.encounter_current_pos.set_offsets(pos)
            else:
                self.encounter_current_pos.set_offsets(np.empty((0, 2)))
            
            # 更新关键时间点标记
            if frame_data.get('is_maneuver_time', False) and frame_data['own_pos']:
                pos = np.array([[float(frame_data['own_pos'][0]), float(frame_data['own_pos'][1])]])
                self.maneuver_marker.set_offsets(pos)
                self.maneuver_marker.set_alpha(1.0)
            else:
                self.maneuver_marker.set_offsets(np.empty((0, 2)))
                self.maneuver_marker.set_alpha(0.0)
            
            if frame_data.get('is_encounter_time', False):
                if frame_data['own_pos'] and frame_data['encounter_pos']:
                    # 在两船中间显示会遇标记
                    mid_x = (float(frame_data['own_pos'][0]) + float(frame_data['encounter_pos'][0])) / 2
                    mid_y = (float(frame_data['own_pos'][1]) + float(frame_data['encounter_pos'][1])) / 2
                    pos = np.array([[mid_x, mid_y]])
                    self.encounter_marker.set_offsets(pos)
                    self.encounter_marker.set_alpha(1.0)
                else:
                    self.encounter_marker.set_offsets(np.empty((0, 2)))
                    self.encounter_marker.set_alpha(0.0)
            else:
                self.encounter_marker.set_offsets(np.empty((0, 2)))
                self.encounter_marker.set_alpha(0.0)
            
            # 更新时间文本
            current_time = frame_data['time']
            self.time_text.set_text(f"Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\nFrame: {frame_idx+1}/{len(animation_data)}")
            
            # 更新状态文本 - 移除emoji字符
            status_lines = []
            if frame_data.get('is_maneuver_time', False):
                status_lines.append("MANEUVERING")
            if frame_data.get('is_encounter_time', False):
                status_lines.append("ENCOUNTER MOMENT")
            
            if frame_data['own_pos']:
                status_lines.append(f"Own Ship SOG: {frame_data.get('own_sog', 0):.1f} knots")
            if frame_data['encounter_pos']:
                status_lines.append(f"Encounter Ship SOG: {frame_data.get('encounter_sog', 0):.1f} knots")
                
            self.status_text.set_text('\n'.join(status_lines))
            
            return [self.own_track_line, self.encounter_track_line, 
                    self.own_current_pos, self.encounter_current_pos,
                    self.maneuver_marker, self.encounter_marker,
                    self.time_text, self.status_text]
                    
        except Exception as e:
            print(f"⚠️  更新帧 {frame_idx} 时出错: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def animate_single_scenario(self, scenario_key, save_path=None, fps=2, use_map=True):
        """
        为单个会遇场景生成动画
        
        :param scenario_key: 场景键值
        :param save_path: 保存路径（.gif或.mp4）
        :param fps: 帧率
        :param use_map: 是否使用底图
        """
        try:
            print(f"[PROCESSING] 开始处理场景: {scenario_key}")
            
            # 准备数据
            animation_data, scenario_data = self._prepare_animation_data(scenario_key)
            self.current_scenario_data = scenario_data
            
            if not animation_data:
                print(f"[ERROR] 场景 {scenario_key} 没有有效的动画数据")
                return None
            
            # 创建图形
            self.fig, self.ax = plt.subplots(1, 1, figsize=(14, 10))
            
            # 获取固定的地图范围
            fixed_extent = self._get_fixed_map_extent()
            
            # 设置底图背景
            if use_map:
                extent = self._setup_map_background(self.ax)
            else:
                extent = fixed_extent
            
            # 获取颜色
            encounter_type = scenario_data['encounter_type']
            colors = self.colors[encounter_type]
            
            # 设置图形属性
            encounter_type_english = 'Overtaking' if encounter_type == 'overtaking' else 'Crossing'
            self.ax.set_title(f'{encounter_type_english} Encounter Animation - Scenario {scenario_data["scenario_id"]:02d}\n'
                            f'Maneuvering: {scenario_data["maneuver_mmsi"]} | Encounter: {scenario_data["encounter_mmsi"]}', 
                            fontsize=14, fontweight='bold')
            
            self.ax.set_xlabel('Longitude (°)', fontsize=12)
            self.ax.set_ylabel('Latitude (°)', fontsize=12)
            
            if not use_map or self.map_img is None:
                self.ax.grid(True, alpha=0.3)
            
            self.ax.set_aspect('equal', adjustable='box')
            
            # 设置固定的坐标轴范围 - 确保全程显示完整水域
            if extent:
                self.ax.set_xlim(extent[0], extent[1])
                self.ax.set_ylim(extent[2], extent[3])
                print(f"[MAP] 固定坐标轴范围: X({extent[0]:.4f}, {extent[1]:.4f}), Y({extent[2]:.4f}, {extent[3]:.4f})")
            else:
                # 如果没有固定范围，使用当前场景数据范围
                own_track = scenario_data['own_track']
                encounter_track = scenario_data['encounter_track']
                all_lons = list(own_track['Lon'].dropna()) + list(encounter_track['Lon'].dropna())
                all_lats = list(own_track['Lat'].dropna()) + list(encounter_track['Lat'].dropna())
                
                if all_lons and all_lats:
                    margin_lon = (max(all_lons) - min(all_lons)) * 0.2
                    margin_lat = (max(all_lats) - min(all_lats)) * 0.2
                    self.ax.set_xlim(min(all_lons) - margin_lon, max(all_lons) + margin_lon)
                    self.ax.set_ylim(min(all_lats) - margin_lat, max(all_lats) + margin_lat)
                    print(f"[MAP] 使用场景数据范围（有额外边距）")
            
            # 初始化动画对象
            self.own_track_line, = self.ax.plot([], [], color=colors['own'], 
                                              linewidth=3, alpha=0.8, 
                                              label=f'Maneuvering Ship {scenario_data["maneuver_mmsi"]}',
                                              zorder=5)
            self.encounter_track_line, = self.ax.plot([], [], color=colors['encounter'], 
                                                    linewidth=3, alpha=0.8,
                                                    label=f'Encounter Ship {scenario_data["encounter_mmsi"]}',
                                                    zorder=5)
            
            # 当前位置标记
            self.own_current_pos = self.ax.scatter([], [], color=colors['own'], s=120, 
                                                 marker='o', edgecolor='white', linewidth=2,
                                                 label='Current Position', zorder=8)
            self.encounter_current_pos = self.ax.scatter([], [], color=colors['encounter'], s=120,
                                                       marker='o', edgecolor='white', linewidth=2, zorder=8)
            
            # 关键时间点标记
            self.maneuver_marker = self.ax.scatter([], [], color='red', s=200, 
                                                 marker='*', edgecolor='white', linewidth=2,
                                                 label='Maneuver Time', zorder=9, alpha=0)
            self.encounter_marker = self.ax.scatter([], [], color='orange', s=200,
                                                  marker='D', edgecolor='white', linewidth=2,
                                                  label='Encounter Time', zorder=9, alpha=0)
            
            # 时间文本
            self.time_text = self.ax.text(0.02, 0.02, '', transform=self.ax.transAxes,
                                        fontsize=10, bbox=dict(boxstyle="round,pad=0.3", 
                                                              facecolor="white", alpha=0.8),
                                        zorder=10)
            
            # 状态文本
            self.status_text = self.ax.text(0.02, 0.92, '', transform=self.ax.transAxes,
                                          fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
                                                                facecolor="lightblue", alpha=0.8),
                                          zorder=10)
            
            self.ax.legend(fontsize=10, loc='upper right', fancybox=True, shadow=True)
            
            # 创建动画
            def animate_frame(frame_idx):
                return self._update_frame(frame_idx, animation_data, colors)
            
            print(f"[ANIMATION] 创建动画，总帧数: {len(animation_data)}")
            self.animation = animation.FuncAnimation(
                self.fig, animate_frame, frames=len(animation_data),
                interval=1000//fps, blit=False, repeat=True
            )
            
            # 保存动画
            if save_path:
                print(f"[SAVING] 正在保存动画: {save_path}")
                try:
                    if save_path.endswith('.gif'):
                        self.animation.save(save_path, writer='pillow', fps=fps, dpi=80)
                    elif save_path.endswith('.mp4'):
                        self.animation.save(save_path, writer='ffmpeg', fps=fps, dpi=80)
                    else:
                        # 默认保存为gif
                        save_path += '.gif'
                        self.animation.save(save_path, writer='pillow', fps=fps, dpi=80)
                    print(f"[SUCCESS] 动画保存成功: {save_path}")
                except Exception as save_error:
                    print(f"[ERROR] 保存动画失败: {save_error}")
                    return None
            
            return self.animation
            
        except Exception as e:
            print(f"[ERROR] 生成动画时出错: {e}")
            import traceback
            traceback.print_exc()
            if self.fig:
                plt.close(self.fig)
            return None
    
    def animate_multiple_scenarios(self, scenario_keys=None, save_dir='vis/animations', 
                                 fps=2, use_map=True, max_scenarios=5):
        """
        为多个场景生成动画
        
        :param scenario_keys: 场景键列表，None表示选择前几个
        :param save_dir: 保存目录
        :param fps: 帧率
        :param use_map: 是否使用底图
        :param max_scenarios: 最大场景数
        """
        os.makedirs(save_dir, exist_ok=True)
        
        if scenario_keys is None:
            scenario_keys = list(self.results.keys())[:max_scenarios]
        
        success_count = 0
        for i, scenario_key in enumerate(scenario_keys):
            print(f"[PROCESSING] 处理动画 {i+1}/{len(scenario_keys)}: {scenario_key}")
            
            save_path = os.path.join(save_dir, f"{scenario_key}_animation.gif")
            
            try:
                self.animate_single_scenario(scenario_key, save_path, fps=fps, use_map=use_map)
                success_count += 1
                
                # 清理
                if self.fig:
                    plt.close(self.fig)
                    
            except Exception as e:
                print(f"处理场景 {scenario_key} 动画时出错: {e}")
                if self.fig:
                    plt.close(self.fig)
                continue
        
        print(f"[SUCCESS] 成功生成 {success_count}/{len(scenario_keys)} 个动画，保存到目录: {save_dir}")
    
    def show_animation(self, scenario_key, fps=2, use_map=True):
        """
        显示动画（交互式）
        
        :param scenario_key: 场景键值
        :param fps: 帧率
        :param use_map: 是否使用底图
        """
        # 设置matplotlib为交互式后端
        matplotlib.use('TkAgg')  # 或其他交互式后端
        
        try:
            animation_obj = self.animate_single_scenario(scenario_key, save_path=None, 
                                                       fps=fps, use_map=use_map)
            if animation_obj:
                plt.show()
                return animation_obj
        except Exception as e:
            print(f"显示动画时出错: {e}")
            return None


def main():
    """主函数：演示动态可视化功能"""
    print("[START] 开始会遇场景动态轨迹可视化...")
    
    # 确保输出目录存在
    os.makedirs('vis/animations', exist_ok=True)
    
    try:
        # 初始化动态可视化器
        visualizer = AnimatedEncounterVisualizer()
        
        # 选择几个代表性场景生成动画
        scenario_keys = list(visualizer.results.keys())[:3]  # 选择前3个场景
        
        print(f"将为以下场景生成动画: {scenario_keys}")
        
        # 生成多个场景的动画
        visualizer.animate_multiple_scenarios(
            scenario_keys=scenario_keys,
            save_dir='vis/animations',
            fps=3,  # 3帧每秒
            use_map=True,
            max_scenarios=3
        )
        
        print("\n[SUCCESS] 动态可视化完成！")
        print("生成的文件:")
        print("- vis/animations/ (各场景动画GIF文件)")
        print("\n[INFO] 提示:")
        print("- 可以调整fps参数改变动画速度")
        print("- 支持保存为.gif或.mp4格式")
        print("- 使用show_animation()方法可以交互式显示")
        
    except FileNotFoundError as e:
        print(f"[ERROR] 错误: {e}")
        print("请先运行 11.船舶避让场景提取.py 生成随机会遇轨迹数据")
    except Exception as e:
        print(f"[ERROR] 动态可视化过程中出现错误: {e}")
    finally:
        # 确保关闭所有matplotlib图形
        plt.close('all')


if __name__ == '__main__':
    # 设置非交互式后端用于批量生成
    matplotlib.use('Agg')
    main() 