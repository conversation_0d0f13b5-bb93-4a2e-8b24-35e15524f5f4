import os
import pickle
import numpy as np
import pandas as pd
from collections import defaultdict
from tqdm import tqdm


class GridStatistics:
    """网格统计分析器"""
    
    def __init__(self, grid_size_meters=500):
        """
        初始化网格统计器
        
        Args:
            grid_size_meters: 网格大小（米），默认500米
        """
        self.grid_size_meters = grid_size_meters
        self.grid_size_degrees = None  # 将根据经纬度计算
        self.bounds = None
        self.grid_shape = None
        self.grids = {}
        
    def set_bounds_from_data(self, tras_df):
        """
        根据数据自动设置边界
        
        Args:
            tras_df: 轨迹数据DataFrame
            buffer_degrees: 边界缓冲区（度），默认0.01度
        """
        min_lon = 121.05
        max_lon = 121.35
        min_lat = 31.516667
        max_lat = 31.783333
        
        self.bounds = {
            'min_lon': min_lon,
            'max_lon': max_lon,
            'min_lat': min_lat,
            'max_lat': max_lat
        }
        
        # 计算网格度数（近似）
        # 纬度1度 ≈ 111km，经度在中纬度地区约为 111km * cos(lat)
        center_lat = (min_lat + max_lat) / 2
        lat_degree_meters = 111000  # 纬度1度的米数
        lon_degree_meters = 111000 * np.cos(np.radians(center_lat))  # 经度1度的米数
        
        self.grid_size_degrees = {
            'lat': self.grid_size_meters / lat_degree_meters,
            'lon': self.grid_size_meters / lon_degree_meters
        }
        
        # 计算网格数量
        lat_grids = int(np.ceil((max_lat - min_lat) / self.grid_size_degrees['lat']))
        lon_grids = int(np.ceil((max_lon - min_lon) / self.grid_size_degrees['lon']))
        
        self.grid_shape = (lat_grids, lon_grids)
        
        print(f"网格设置: {lat_grids} x {lon_grids} = {lat_grids * lon_grids} 个网格")
        print(f"网格大小: 纬度 {self.grid_size_degrees['lat']:.6f}°, 经度 {self.grid_size_degrees['lon']:.6f}°")
        print(f"覆盖范围: 纬度 [{min_lat:.6f}, {max_lat:.6f}], 经度 [{min_lon:.6f}, {max_lon:.6f}]")
    
    def get_grid_index(self, lat, lon):
        """
        获取经纬度对应的网格索引
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            tuple: (lat_idx, lon_idx) 或 None（如果超出边界）
        """
        if (lat < self.bounds['min_lat'] or lat > self.bounds['max_lat'] or
            lon < self.bounds['min_lon'] or lon > self.bounds['max_lon']):
            return None
            
        lat_idx = int((lat - self.bounds['min_lat']) / self.grid_size_degrees['lat'])
        lon_idx = int((lon - self.bounds['min_lon']) / self.grid_size_degrees['lon'])
        
        # 确保不超出边界
        lat_idx = min(lat_idx, self.grid_shape[0] - 1)
        lon_idx = min(lon_idx, self.grid_shape[1] - 1)
        
        return (lat_idx, lon_idx)
    
    def get_grid_center(self, lat_idx, lon_idx):
        """
        获取网格中心点坐标
        
        Args:
            lat_idx: 纬度网格索引
            lon_idx: 经度网格索引
            
        Returns:
            tuple: (center_lat, center_lon)
        """
        center_lat = self.bounds['min_lat'] + (lat_idx + 0.5) * self.grid_size_degrees['lat']
        center_lon = self.bounds['min_lon'] + (lon_idx + 0.5) * self.grid_size_degrees['lon']
        return (center_lat, center_lon)


def extract_encounter_trajectories(encounters, tras_df):
    """
    提取会遇相关的轨迹数据
    
    Args:
        encounters: 会遇列表
        tras_df: 完整轨迹数据
        
    Returns:
        dict: 包含轨迹数据的字典
    """
    if not encounters:
        return {'trajectory_points': [], 'encounter_info': []}
    
    print(f"正在提取 {len(encounters)} 个会遇的轨迹数据...")
    
    # 收集所有涉及的船舶和时间
    involved_ships = set()
    time_ranges = []
    
    for encounter in encounters:
        involved_ships.update(encounter['s_pair'])
        encounter_times = encounter.get('encounter_times', [encounter['pos_time']])
        if encounter_times:
            time_ranges.append((min(encounter_times), max(encounter_times)))
    
    print(f"涉及船舶数量: {len(involved_ships)}")
    
    # 提取相关轨迹点
    trajectory_points = []
    encounter_info = []
    
    for encounter in tqdm(encounters, desc='提取会遇轨迹'):
        ship1_mmsi, ship2_mmsi = encounter['s_pair']
        encounter_times = encounter.get('encounter_times', [encounter['pos_time']])
        encounter_type = encounter.get('encounter_type', 'unknown')
        
        if not encounter_times:
            continue
            
        # 为每个时间点提取两船的轨迹
        for time_point in encounter_times:
            # 提取ship1的轨迹点
            ship1_data = tras_df[(tras_df['MMSI'] == ship1_mmsi) & 
                                (tras_df['PosTime'] == time_point)]
            for _, row in ship1_data.iterrows():
                trajectory_points.append({
                    'lat': row['Lat'],
                    'lon': row['Lon'],
                    'mmsi': row['MMSI'],
                    'time': row['PosTime'],
                    'cog': row['Cog'],
                    'sog': row['Sog']
                })
                encounter_info.append({
                    'encounter_id': len(encounter_info),
                    'encounter_type': encounter_type,
                    's_pair': encounter['s_pair'],
                    'encounter_time': encounter['pos_time']
                })
            
            # 提取ship2的轨迹点
            ship2_data = tras_df[(tras_df['MMSI'] == ship2_mmsi) & 
                                (tras_df['PosTime'] == time_point)]
            for _, row in ship2_data.iterrows():
                trajectory_points.append({
                    'lat': row['Lat'],
                    'lon': row['Lon'],
                    'mmsi': row['MMSI'],
                    'time': row['PosTime'],
                    'cog': row['Cog'],
                    'sog': row['Sog']
                })
                encounter_info.append({
                    'encounter_id': len(encounter_info),
                    'encounter_type': encounter_type,
                    's_pair': encounter['s_pair'],
                    'encounter_time': encounter['pos_time']
                })
    
    result = {
        'trajectory_points': trajectory_points,
        'encounter_info': encounter_info,
        'involved_ships': list(involved_ships),
        'total_points': len(trajectory_points)
    }
    
    print(f"提取完成: {len(trajectory_points)} 个轨迹点")
    return result


def calculate_grid_statistics(trajectory_data, grid_stats, encounter_type_name):
    """
    计算网格统计
    
    Args:
        trajectory_data: 轨迹数据
        grid_stats: GridStatistics对象
        encounter_type_name: 会遇类型名称
        
    Returns:
        dict: 网格统计结果
    """
    print(f"正在计算 {encounter_type_name} 的网格统计...")
    
    trajectory_points = trajectory_data['trajectory_points']
    encounter_info = trajectory_data['encounter_info']
    
    if not trajectory_points:
        print(f"警告: {encounter_type_name} 没有轨迹点数据")
        return {
            'grid_counts': np.zeros(grid_stats.grid_shape),
            'point_assignments': [],
            'total_points': 0,
            'valid_points': 0
        }
    
    # 初始化网格计数
    grid_counts = np.zeros(grid_stats.grid_shape)
    point_assignments = []
    valid_points = 0
    
    # 将轨迹点分配到网格
    for i, point in enumerate(tqdm(trajectory_points, desc=f'分配 {encounter_type_name} 轨迹点到网格')):
        lat, lon = point['lat'], point['lon']
        grid_idx = grid_stats.get_grid_index(lat, lon)
        
        if grid_idx is not None:
            lat_idx, lon_idx = grid_idx
            grid_counts[lat_idx, lon_idx] += 1
            valid_points += 1
            
            point_assignments.append({
                'point_index': i,
                'lat': lat,
                'lon': lon,
                'grid_lat_idx': lat_idx,
                'grid_lon_idx': lon_idx,
                'mmsi': point['mmsi'],
                'time': point['time'],
                'encounter_type': encounter_info[i]['encounter_type'] if i < len(encounter_info) else 'unknown'
            })
    
    result = {
        'grid_counts': grid_counts,
        'point_assignments': point_assignments,
        'total_points': len(trajectory_points),
        'valid_points': valid_points,
        'coverage_rate': valid_points / len(trajectory_points) if trajectory_points else 0
    }
    
    print(f"{encounter_type_name} 统计完成:")
    print(f"  - 总轨迹点: {result['total_points']}")
    print(f"  - 有效轨迹点: {result['valid_points']}")
    print(f"  - 覆盖率: {result['coverage_rate']:.2%}")
    print(f"  - 非空网格数: {np.sum(grid_counts > 0)}")
    print(f"  - 最大网格密度: {np.max(grid_counts)}")
    
    return result



def save_grid_results(grid_stats, crossing_result, overtaking_result, output_dir="result0"):
    """
    保存网格统计结果
    
    Args:
        grid_stats: GridStatistics对象
        crossing_result: 交叉会遇统计结果
        overtaking_result: 追越会遇统计结果
        output_dir: 输出目录
    """
    print("正在保存网格统计结果...")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整结果
    complete_results = {
        'grid_settings': {
            'grid_size_meters': grid_stats.grid_size_meters,
            'grid_size_degrees': grid_stats.grid_size_degrees,
            'bounds': grid_stats.bounds,
            'grid_shape': grid_stats.grid_shape
        },
        'crossing_encounters': crossing_result,
        'overtaking_encounters': overtaking_result,
        'summary': {
            'total_crossing_points': crossing_result['total_points'],
            'total_overtaking_points': overtaking_result['total_points'],
            'total_points': crossing_result['total_points'] + overtaking_result['total_points'],
            'crossing_grids': np.sum(crossing_result['grid_counts'] > 0),
            'overtaking_grids': np.sum(overtaking_result['grid_counts'] > 0),
            'total_grids': grid_stats.grid_shape[0] * grid_stats.grid_shape[1],
            'grid_coverage_crossing': np.sum(crossing_result['grid_counts'] > 0) / (grid_stats.grid_shape[0] * grid_stats.grid_shape[1]),
            'grid_coverage_overtaking': np.sum(overtaking_result['grid_counts'] > 0) / (grid_stats.grid_shape[0] * grid_stats.grid_shape[1])
        }
    }
    
    # 保存到pickle文件
    result_file = os.path.join(output_dir, 'grid_statistics_results.pkl')
    with open(result_file, 'wb') as f:
        pickle.dump(complete_results, f)
    
    print(f"网格统计结果已保存: {result_file}")
    
    # 保存网格数据到CSV（方便其他工具使用）
    save_grid_csv(grid_stats, crossing_result, overtaking_result, output_dir)
    
    return complete_results


def save_grid_csv(grid_stats, crossing_result, overtaking_result, output_dir):
    """保存网格数据到CSV文件"""
    grid_data = []
    
    for lat_idx in range(grid_stats.grid_shape[0]):
        for lon_idx in range(grid_stats.grid_shape[1]):
            center_lat, center_lon = grid_stats.get_grid_center(lat_idx, lon_idx)
            
            crossing_count = crossing_result['grid_counts'][lat_idx, lon_idx]
            overtaking_count = overtaking_result['grid_counts'][lat_idx, lon_idx]
            
            if crossing_count > 0 or overtaking_count > 0:  # 只保存有数据的网格
                grid_data.append({
                    'lat_idx': lat_idx,
                    'lon_idx': lon_idx,
                    'center_lat': center_lat,
                    'center_lon': center_lon,
                    'crossing_count': int(crossing_count),
                    'overtaking_count': int(overtaking_count),
                    'total_count': int(crossing_count + overtaking_count)
                })
    
    # 保存到CSV
    if grid_data:
        df = pd.DataFrame(grid_data)
        csv_file = os.path.join(output_dir, 'grid_statistics.csv')
        df.to_csv(csv_file, index=False)
        print(f"网格数据已保存到CSV: {csv_file}")


def print_grid_summary(grid_stats, crossing_result, overtaking_result):
    """打印网格统计摘要"""
    total_grids = grid_stats.grid_shape[0] * grid_stats.grid_shape[1]
    crossing_active_grids = np.sum(crossing_result['grid_counts'] > 0)
    overtaking_active_grids = np.sum(overtaking_result['grid_counts'] > 0)
    
    print("\n" + "="*60)
    print("网格统计分析摘要")
    print("="*60)
    
    print(f"网格设置:")
    print(f"  - 网格大小: {grid_stats.grid_size_meters}米")
    print(f"  - 网格数量: {grid_stats.grid_shape[0]} x {grid_stats.grid_shape[1]} = {total_grids}")
    print(f"  - 覆盖范围: 纬度 [{grid_stats.bounds['min_lat']:.6f}, {grid_stats.bounds['max_lat']:.6f}]")
    print(f"                经度 [{grid_stats.bounds['min_lon']:.6f}, {grid_stats.bounds['max_lon']:.6f}]")
    
    print(f"\n交叉会遇统计:")
    print(f"  - 轨迹点数量: {crossing_result['total_points']}")
    print(f"  - 活跃网格数: {crossing_active_grids} ({crossing_active_grids/total_grids:.2%})")
    print(f"  - 最大网格密度: {np.max(crossing_result['grid_counts'])}")
    print(f"  - 平均网格密度: {np.mean(crossing_result['grid_counts'][crossing_result['grid_counts'] > 0]):.2f}")
    
    print(f"\n追越会遇统计:")
    print(f"  - 轨迹点数量: {overtaking_result['total_points']}")
    print(f"  - 活跃网格数: {overtaking_active_grids} ({overtaking_active_grids/total_grids:.2%})")
    print(f"  - 最大网格密度: {np.max(overtaking_result['grid_counts'])}")
    print(f"  - 平均网格密度: {np.mean(overtaking_result['grid_counts'][overtaking_result['grid_counts'] > 0]):.2f}")
    
    print(f"\n总体统计:")
    total_points = crossing_result['total_points'] + overtaking_result['total_points']
    combined_active_grids = np.sum((crossing_result['grid_counts'] + overtaking_result['grid_counts']) > 0)
    print(f"  - 总轨迹点数: {total_points}")
    print(f"  - 总活跃网格: {combined_active_grids} ({combined_active_grids/total_grids:.2%})")
    
    if total_points > 0:
        print(f"  - 交叉会遇占比: {crossing_result['total_points']/total_points:.2%}")
        print(f"  - 追越会遇占比: {overtaking_result['total_points']/total_points:.2%}")


def main_grid_analysis(extraction_results, grid_size_meters=500, save_results=True):
    """
    主要网格分析函数
    
    Args:
        extraction_results: 从encounter_extraction模块得到的提取结果
        grid_size_meters: 网格大小（米）
        save_results: 是否保存结果
        
    Returns:
        dict: 网格分析结果
    """
    print("=== 开始网格统计分析 ===")
    
    # 获取数据
    cross_related_encounters = extraction_results['cross_related_encounters']
    non_cross_encounters = extraction_results['non_cross_encounters']
    tras_df = extraction_results['tras_df']
    
    print(f"交叉会遇数量: {len(cross_related_encounters)}")
    print(f"追越会遇数量: {len(non_cross_encounters)}")
    
    # 初始化网格统计器
    grid_stats = GridStatistics(grid_size_meters=grid_size_meters)
    grid_stats.set_bounds_from_data(tras_df)
    
    # 提取轨迹数据
    print("\n1. 提取交叉会遇轨迹...")
    crossing_trajectories = extract_encounter_trajectories(cross_related_encounters, tras_df)
    
    print("\n2. 提取追越会遇轨迹...")
    overtaking_trajectories = extract_encounter_trajectories(non_cross_encounters, tras_df)
    
    # 计算网格统计
    print("\n3. 计算网格统计...")
    crossing_result = calculate_grid_statistics(crossing_trajectories, grid_stats, "交叉会遇")
    overtaking_result = calculate_grid_statistics(overtaking_trajectories, grid_stats, "追越会遇")
    
    # 打印摘要
    print_grid_summary(grid_stats, crossing_result, overtaking_result)
    
    # 保存结果
    if save_results:
        print("\n4. 保存结果...")
        complete_results = save_grid_results(grid_stats, crossing_result, overtaking_result)
    
    print("=== 网格统计分析完成 ===")
    
    return {
        'grid_settings': grid_stats,
        'crossing_result': crossing_result,
        'overtaking_result': overtaking_result,
        'crossing_trajectories': crossing_trajectories,
        'overtaking_trajectories': overtaking_trajectories
    }


if __name__ == '__main__':
    # 加载encounter_extraction模块保存的结果
    try:
        # 首先尝试加载最新的提取结果
        extraction_file = "result0/cache/cache_step3b_merged_encounters.pkl"
        if os.path.exists(extraction_file):
            print(f"正在加载会遇提取结果: {extraction_file}")
            with open(extraction_file, 'rb') as f:
                merged_encounters = pickle.load(f)
            
            # 加载轨迹数据
            print("正在加载轨迹数据...")
            tras_df = pd.read_parquet('data/tras_2024_3_inter.parquet')
            
            # 重构extraction_results格式
            cross_related_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'crossing']
            non_cross_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'overtaking']
            
            extraction_results = {
                'encounters': merged_encounters,
                'cross_related_encounters': cross_related_encounters,
                'non_cross_encounters': non_cross_encounters,
                'tras_df': tras_df,
                'cross_trajectory_map': {},
                'cross_mmsi_count': set(),
                'summary': {
                    'total_encounters': len(merged_encounters),
                    'cross_related_count': len(cross_related_encounters),
                    'non_cross_count': len(non_cross_encounters),
                    'cross_trajectory_count': 0,
                    'cross_ship_count': 0
                }
            }
            
            print(f"加载完成: {len(merged_encounters)} 个会遇过程")
            print(f"  - 交叉会遇: {len(cross_related_encounters)}")
            print(f"  - 追越会遇: {len(non_cross_encounters)}")
            
        else:
            print(f"错误: 未找到会遇提取结果文件: {extraction_file}")
            print("请先运行 01.encounter_extraction.py 进行会遇提取")
            exit(1)
            
    except Exception as e:
        print(f"加载会遇提取结果时出错: {e}")
        print("请确保已运行 01.encounter_extraction.py 并成功生成结果文件")
        exit(1)
    
    # 执行网格统计分析
    print("\n" + "="*50)
    grid_results = main_grid_analysis(extraction_results, grid_size_meters=50)
    print(f"\n网格统计分析完成！结果已保存到 result0/ 目录")
