import pickle
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import random


def extract_trajectories_from_ship_dc2(Scene, ships):
    """
    :param Scene: 一个二维数组，每行表示一个时间点：
                  第一列为时间，第二列为该时刻的船舶数量，剩余列为各船舶在 ships 中的索引。
    :param ships: 所有船舶的轨迹数据，列表，每个元素是一个 NumPy 数组，
                  形状为 (N, 6)，每列分别为 [时间, MMSI, x坐标, y坐标, 航向(角度), 速度(kn)]。
    :return: 一个新的轨迹数据列表，按场景分组。
    """
    extracted_trajectories = []  # 存储提取的轨迹数据
    unique_times = np.unique(Scene[:, 0])  # 提取所有唯一时间点

    for time in unique_times:
        # 提取当前时间的所有行
        current_time_rows = Scene[Scene[:, 0] == time]
        current_time = int(time)
        involved_ship_indices = current_time_rows[:, 2:].flatten()  # 提取所有参与的船舶索引
        involved_ship_indices = involved_ship_indices[involved_ship_indices >= 0]  # 排除无效索引

        # 提取当前时刻的船舶轨迹点
        current_trajectories = []
        for idx in involved_ship_indices:
            idx = int(idx)
            if 0 <= idx < len(ships):  # 检查索引是否有效
                ship_data = ships[idx]
                current_data = ship_data[ship_data[:, 0] == current_time]  # 提取当前时刻的数据点
                if current_data.size > 0:
                    current_trajectories.append(current_data[0])  # 存储点（去掉多维嵌套）

        extracted_trajectories.append(np.array(current_trajectories))  # 按时间点分组
    # print(extracted_trajectories)
    return extracted_trajectories


def visualize_dynamic_scenarios(extracted_trajectories, id, interval=100, save_gif=False):
    """
    动态可视化从 SHIP_dc2 提取出的轨迹数据，支持动态船舶加入和离开。

    :param extracted_trajectories: 提取的轨迹数据，列表，每个元素是一个时间点的船舶轨迹。
    :param id: 场景编号，用于 GIF 文件命名。
    :param interval: 动画帧之间的时间间隔（毫秒）。
    :param save_gif: 是否保存动画为 GIF 文件，默认为 False。
    """
    # 计算所有轨迹点的范围
    all_x = []
    all_y = []
    for frame in extracted_trajectories:
        for data in frame:
            all_x.append(data[2])  # x 坐标
            all_y.append(data[3])  # y 坐标
    x_min, x_max = min(all_x), max(all_x)
    y_min, y_max = min(all_y), max(all_y)

    # 设置颜色和标记
    fig, ax = plt.subplots(figsize=(10, 8))
    # ax.set_xlim(21408766.668, 21437684.708)
    # ax.set_ylim(3372326.773, 3390637.606)
    ax.set_xlim(x_min - 100, x_max + 100)  # 加入一定的边距
    ax.set_ylim(y_min - 100, y_max + 100)
    ax.set_xlabel("X Position")
    ax.set_ylabel("Y Position")
    ax.set_title(f"Dynamic Visualization of Ship Encounter Scenarios (Scene {id})")

    # 生成所有船舶的颜色和标签
    all_ship_ids = {int(data[1]) for frame in extracted_trajectories for data in frame if len(data) > 0}
    all_ship_ids = sorted(list(all_ship_ids))
    colors = plt.cm.tab20(np.linspace(0, 1, len(all_ship_ids)))
    color_map = {ship_id: colors[i] for i, ship_id in enumerate(all_ship_ids)}

    # 初始化散点和轨迹线
    scatter_plots = {}
    trajectory_lines = {}

    for ship_id in all_ship_ids:
        scatter, = ax.plot([], [], 'o', color=color_map[ship_id], label=f"Ship {ship_id}")
        line, = ax.plot([], [], '-', color=color_map[ship_id], alpha=0.5)
        scatter_plots[ship_id] = scatter
        trajectory_lines[ship_id] = {"line": line, "x": [], "y": []}

    ax.legend(loc="upper right")

    # 初始化轨迹数据（避免保存 GIF 时轨迹混乱）
    def init():
        for ship_id in all_ship_ids:
            trajectory_lines[ship_id]["x"] = []
            trajectory_lines[ship_id]["y"] = []
            trajectory_lines[ship_id]["last_point"] = (None, None)
            scatter_plots[ship_id].set_data([], [])
            trajectory_lines[ship_id]["line"].set_data([], [])
        return list(scatter_plots.values()) + [line["line"] for line in trajectory_lines.values()]

    # 更新函数
    def update(frame):
        current_data = extracted_trajectories[frame]

        ax.set_title(f"Dynamic Visualization at Time Frame {frame} (Scene {id})")

        # 动态更新每艘船的点和轨迹
        for ship_id, scatter in scatter_plots.items():
            # 找到当前帧中该船的数据
            ship_data = [data for data in current_data if int(data[1]) == ship_id]
            if len(ship_data) > 0:
                ship_data = ship_data[0]  # 提取唯一的数据点
                x, y = ship_data[2], ship_data[3]
                trajectory_lines[ship_id]["x"].append(x)
                trajectory_lines[ship_id]["y"].append(y)
                scatter.set_data([x], [y])  # 更新当前点
                trajectory_lines[ship_id]["line"].set_data(trajectory_lines[ship_id]["x"],
                                                           trajectory_lines[ship_id]["y"])
            else:
                # 如果当前帧没有新数据，保留最后的点和轨迹
                last_x, last_y = trajectory_lines[ship_id]["last_point"]
                if last_x is not None and last_y is not None:
                    scatter.set_data([last_x], [last_y])
                    trajectory_lines[ship_id]["line"].set_data(trajectory_lines[ship_id]["x"],
                                                               trajectory_lines[ship_id]["y"])

        return list(scatter_plots.values()) + [line["line"] for line in trajectory_lines.values()]

    # 创建动画
    ani = FuncAnimation(
        fig, update, init_func=init, frames=len(extracted_trajectories), interval=interval, blit=True, repeat=False
    )

    # 保存为 GIF（可选）
    if save_gif:
        ani.save(f"vis/dynamic_scenarios_{id}.gif", writer="pillow", fps=1000 // interval)

    # 显示动画
    plt.show()


if __name__ == '__main__':
    with open('data/2024_3_array.pkl', 'rb') as f:
        ships_array = pickle.load(f)

    with open('result/2024_3/Ship_dc2_simplified.pkl', 'rb') as f:
        Ship_dc2 = pickle.load(f)


    random_integer = random.randint(0, len(Ship_dc2) - 1)  # 注意：索引从 0 开始
    random_scene = Ship_dc2[random_integer]
    print(random_integer)

    # 调用可视化函数
    # 提取轨迹数据
    extracted_trajectories = extract_trajectories_from_ship_dc2(random_scene, ships_array)

    # 可视化提取的轨迹数据
    visualize_dynamic_scenarios(extracted_trajectories, random_integer, save_gif=False)
