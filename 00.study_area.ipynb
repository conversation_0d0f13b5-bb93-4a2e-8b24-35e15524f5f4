#%%
import pandas as pd
df = pd.read_parquet('data/tras_2024_1_3_inter.parquet')
#%%
import datashader as ds
import datashader.transfer_functions as tf
import numpy as np
import pandas as pd
from PIL import Image
from matplotlib import pyplot as plt
from matplotlib.colors import ListedColormap
from matplotlib.ticker import FormatStrFormatter, MultipleLocator

plt.rcParams['font.family'] = 'Times New Roman'
# plt.rcParams['font.size'] = 12  # 设置默认字体大小
plt.rcParams['font.weight'] = 'bold'  # 设置默认字体加粗

Lat = [31.516, 31.784]  # 纬度范围
Lon = [121.05, 121.35]  # 经度范围
# 创建 Canvas
canvas = ds.Canvas(plot_width=1820, plot_height=1326)

# df1 = df.loc[(df['Type']>=60)& (df['Type']<70)]
# df1 = df.loc[(df['Type']>=70)& (df['Type']<80)]
# df1 = df.loc[(df['Type']>=80)& (df['Type']<90)]
df1 = df
agg = canvas.points(df1, 'Lon', 'Lat', agg=ds.count())

# 使用 datashader 绘制
# # 生成红色渐变
# gradient = plt.cm.Reds(np.linspace(0, 1, 256)) 
# 生成蓝色渐变
# gradient = plt.cm.Blues(np.linspace(0, 1, 256))
# 其他渐变[viridis, plasma, inferno, magma, cividis]
gradient = plt.cm.viridis(np.linspace(0, 1, 256))

cmap = ListedColormap(gradient)
img = tf.shade(agg, cmap=cmap, how='log')

# 加载背景底图
background_image = Image.open('data/map0.png')  # 替换为你的底图文件路径

# 将 Datashader 图像转换为 PIL 图像
datashader_img = img.to_pil()

# 确保两张图片的尺寸一致
background_image = background_image.resize(datashader_img.size)

# 创建一个新图层，合并两张图
combined_img = Image.new("RGBA", datashader_img.size)
combined_img.paste(background_image, (0, 0))  # 将底图粘贴到新图层
combined_img.paste(datashader_img, (0, 0), datashader_img)  # 将 Datashader 图像粘贴到新图层

fig, ax = plt.subplots(dpi=300)
ax.imshow(combined_img, extent=[Lon[0], Lon[1], Lat[0], Lat[1]])

# 添加坐标轴
ax.set_xlabel('Longitude (°)', fontsize=13, fontdict={'family': 'Times New Roman', 'weight': 'bold'})
ax.set_ylabel('Latitude (°)', fontsize=13, fontdict={'family': 'Times New Roman', 'weight': 'bold'})

# 设置网格透明度和颜色
ax.grid(True, color='gray', alpha=0.3, linestyle='--')  # 设置网格颜色为灰色，透明度为0.5

# 设置刻度朝内，四周都要有刻度
ax.tick_params(axis='both', direction='in', length=6, width=1, top=True, right=True)  # 刻度朝内，并在顶部和右侧也有刻度

# 设置框线的颜色和透明度
for spine in ax.spines.values():
    spine.set_edgecolor('black')  # 设置框线颜色为黑色
    spine.set_alpha(0.7)  # 设置框线透明度为0.7

# 调整横纵坐标的小数点位数
ax.xaxis.set_major_formatter(FormatStrFormatter('%.1f'))  # 横坐标显示三位小数
ax.yaxis.set_major_formatter(FormatStrFormatter('%.1f'))  # 纵坐标显示三位小数

# 设置横纵坐标的间隔
ax.xaxis.set_major_locator(MultipleLocator(0.1))  # 横坐标
ax.yaxis.set_major_locator(MultipleLocator(0.1))  # 纵坐标

# 设置想要显示的经纬度范围
ax.set_xlim(Lon[0], Lon[1])
ax.set_ylim(Lat[0], Lat[1])

# 添加坐标轴线
ax.axhline(0, color='black', linewidth=1)

plt.savefig('vis/study_area.svg', format='svg',
            bbox_inches='tight', pad_inches=0.1, dpi=300)
plt.close()