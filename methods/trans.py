import numpy as np


class Trans:
    @staticmethod
    def Gauss2tolonLat(x_list, y_list):
        iPI = 0.0174532925199433  # 弧度转换系数
        a = 6378245.0
        f = 1.0 / 298.257223563  # 更精确的椭球体扁率
        ZoneWide = 6

        # NumPy 数组化输入
        x_list = np.array(x_list)
        y_list = np.array(y_list)

        ProjNo = np.floor_divide(x_list, 1000000)
        longitude0 = (ProjNo - 1) * ZoneWide + ZoneWide / 2
        longitude0 *= iPI

        X0 = ProjNo * 1000000 + 500000
        xval = x_list - X0

        e2 = f * (2 - f)
        e1 = (1.0 - np.sqrt(1 - e2)) / (1.0 + np.sqrt(1 - e2))
        ee = e2 / (1 - e2)
        M = y_list
        u = M / (a * (1 - e2 / 4 - 3 * e2 ** 2 / 64 - 5 * e2 ** 3 / 256))

        # fai 的计算涉及多项式和三角函数的组合
        fai = u + (3 * e1 / 2 - 27 * e1 ** 3 / 32) * np.sin(2 * u) + \
              (21 * e1 ** 2 / 16 - 55 * e1 ** 4 / 32) * np.sin(4 * u) + \
              (151 * e1 ** 3 / 96) * np.sin(6 * u) + (1097 * e1 ** 4 / 512) * np.sin(8 * u)

        C = ee * np.cos(fai) ** 2
        T = np.tan(fai) ** 2
        NN = a / np.sqrt(1.0 - e2 * np.sin(fai) ** 2)
        R = a * (1 - e2) / np.power((1 - e2 * np.sin(fai) ** 2), 1.5)
        D = xval / NN

        # 经纬度的最终计算
        latitude = fai - (NN * np.tan(fai) / R) * (
                D ** 2 / 2 - (5 + 3 * T + 10 * C - 4 * C ** 2 - 9 * ee) * D ** 4 / 24 + (
                61 + 90 * T + 298 * C + 45 * T ** 2 - 252 * ee - 3 * C ** 2) * D ** 6 / 720)
        longitude = longitude0 + (D - (1 + 2 * T + C) * D ** 3 / 6 + (
                5 - 2 * C + 28 * T - 3 * C ** 2 + 8 * ee + 24 * T ** 2) * D ** 5 / 120) / np.cos(fai)

        # 转换为度并四舍五入到小数点后六位
        output_longitude = np.round(longitude / iPI, 6)
        output_latitude = np.round(latitude / iPI, 6)

        return output_longitude, output_latitude

    @staticmethod
    def LonLat2Gauss(lon_list, lat_list):
        # 确保输入是 numpy 数组以利用向量化
        lon_list = np.array(lon_list)
        lat_list = np.array(lat_list)

        iPI = np.pi / 180.0
        a = 6378245.0
        f = 1.0 / 298.3
        ZoneWide = 6

        ProjNo = np.floor(lon_list / ZoneWide)
        longitude0 = (ProjNo * ZoneWide + ZoneWide / 2) * iPI
        longitude1 = lon_list * iPI
        latitude1 = lat_list * iPI

        e2 = 2 * f - f ** 2
        ee = e2 / (1.0 - e2)
        NN = a / np.sqrt(1.0 - e2 * np.sin(latitude1) ** 2)
        T = np.tan(latitude1) ** 2
        C = ee * np.cos(latitude1) ** 2
        A = (longitude1 - longitude0) * np.cos(latitude1)

        # M 的计算更加复杂，涉及到纬度的多项式函数
        M = a * ((1 - e2 / 4 - 3 * e2 ** 2 / 64 - 5 * e2 ** 3 / 256) * latitude1
                 - (3 * e2 / 8 + 3 * e2 ** 2 / 32 + 45 * e2 ** 3 / 1024) * np.sin(2 * latitude1)
                 + (15 * e2 ** 2 / 256 + 45 * e2 ** 3 / 1024) * np.sin(4 * latitude1)
                 - (35 * e2 ** 3 / 3072) * np.sin(6 * latitude1))

        X = NN * (A + (1 - T + C) * A ** 3 / 6 + (5 - 18 * T + T ** 2 + 72 * C - 58 * ee) * A ** 5 / 120)
        Y = M + NN * np.tan(latitude1) * (A ** 2 / 2 + (5 - T + 9 * C + 4 * C ** 2) * A ** 4 / 24 + (
                61 - 58 * T + T ** 2 + 600 * C - 330 * ee) * A ** 6 / 720)

        X0 = 1000000 * (ProjNo + 1) + 500000
        Y0 = 0
        xval = np.round(X + X0, 3)
        yval = np.round(Y + Y0, 3)

        return xval, yval

    def Gauss2Grid(self, x_list, y_list, size):
        lonlat1 = [121.867, 29.717]
        lonlat2 = [122.367, 30.033]
        x1, y1 = self.LonLat2Gauss(lonlat1[0], lonlat1[1])
        x2, y2 = self.LonLat2Gauss(lonlat2[0], lonlat2[1])

        grid_size_x = (x2 - x1) / size[0]  # 计算x方向的网格大小
        grid_size_y = (y2 - y1) / size[1]  # 计算y方向的网格大小

        # 将高斯坐标转换为网格坐标
        x = (x_list - x1) / grid_size_x
        y = (y_list - y1) / grid_size_y

        # 将浮点型转换为整数
        x = np.array(x, dtype=int)
        y = np.array(y, dtype=int)

        # 边界检查，防止溢出
        x = np.clip(x, 0, size[0] - 1)
        y = np.clip(y, 0, size[1] - 1)

        return x, size[1] - 1 - y

    def LonLat2Grid(self, lon, lat, size):
        x, y = self.LonLat2Gauss(lon, lat)
        return self.Gauss2Grid(x, y, size)

    # def Gauss2Grid(self, x_list, y_list, navigability_matrix):
    #     lonlat1 = [121.867, 29.717]
    #     lonlat2 = [122.367, 30.033]
    #     x1, y1 = self.LonLat2Gauss(lonlat1[0], lonlat1[1])
    #     x2, y2 = self.LonLat2Gauss(lonlat2[0], lonlat2[1])
    #
    #     grid_size_x = (x2 - x1) / navigability_matrix.shape[1]  # 计算x方向的网格大小
    #     grid_size_y = (y2 - y1) / navigability_matrix.shape[0]  # 计算y方向的网格大小
    #
    #     x = (x_list - x1) / grid_size_x
    #     x = np.array(x, dtype=int)
    #     y = (y_list - y1) / grid_size_y
    #     y = np.array(y, dtype=int)
    #     navigability = navigability_matrix[navigability_matrix.shape[0]-y, x]
    #
    #     return x, y, navigability
