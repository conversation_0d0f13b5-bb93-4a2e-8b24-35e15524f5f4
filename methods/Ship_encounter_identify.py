# 船舶会遇检测算法
import math
import random
from typing import List, Dict, Tuple, Optional

import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, FancyArrowPatch
from scipy.interpolate import interp1d
from scipy.optimize import minimize_scalar

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class EnhancedWaterwayEncounterDetector:
    """
    增强版基于航道中心线投影的船舶会遇检测器
    支持航道区域拟合和改进的可视化
    """

    def __init__(self, waterway_points: List[Tuple[float, float]],
                 waterway_boundary: Optional[List[Tuple[float, float]]] = None,
                 tcpa_threshold: int = 300,
                 use_fitted_curve: bool = False,
                 subregions: Optional[Dict] = None):
        """
        初始化检测器

        参数:
        waterway_points: 航道中心线的点坐标列表 [(lon1, lat1), (lon2, lat2), ...]
        waterway_boundary: 航道边界的点坐标列表，如果提供则用于生成测试数据
        tcpa_threshold: TCPA阈值（秒），默认300秒
        use_fitted_curve: 是否使用拟合曲线进行投影计算，默认False使用原始线段
        subregions: 子区域定义字典 {'subregion_1': [(lon,lat),...], 'subregion_2': [...], ...}
        """
        self.KNOTS_TO_MS = 0.514444
        self.R_EARTH = 6371000
        self.tcpa_threshold = tcpa_threshold
        # 处理航道中心线
        self.waterway_points = waterway_points
        self.use_fitted_curve = use_fitted_curve

        # 只有在需要时才创建拟合函数
        if use_fitted_curve:
            self.waterway_function = self._fit_waterway_curve()
            self.waterway_length = self._calculate_waterway_length_fitted()
        else:
            self.waterway_function = None
            self.waterway_length = self._calculate_waterway_length_original()

        # 处理航道边界
        self.waterway_boundary = waterway_boundary
        self.waterway_polygon = self._create_waterway_polygon() if waterway_boundary else None
        
        # 新增：四个子区域定义
        self.subregions = subregions or {}

    def _fit_waterway_curve(self) -> Dict:
        """
        将航道中心线点拟合成连续的参数化曲线
        """
        if len(self.waterway_points) < 2:
            raise ValueError("航道中心线至少需要2个点")

        # 提取经纬度
        lons = [point[0] for point in self.waterway_points]
        lats = [point[1] for point in self.waterway_points]

        # 计算累积弧长作为参数
        distances = [0]
        for i in range(1, len(self.waterway_points)):
            dist = self._calculate_distance_between_points(
                self.waterway_points[i - 1], self.waterway_points[i]
            )
            distances.append(distances[-1] + dist)

        # 归一化到[0, 1]
        max_distance = distances[-1]
        if max_distance == 0:
            raise ValueError("航道中心线点重合")

        normalized_distances = [d / max_distance for d in distances]

        # 创建插值函数
        lon_func = interp1d(normalized_distances, lons, kind='linear')
        lat_func = interp1d(normalized_distances, lats, kind='linear')

        return {
            'lon_func': lon_func,
            'lat_func': lat_func,
            'total_length': max_distance,
            'normalized_distances': normalized_distances
        }

    def _create_waterway_polygon(self) -> Optional[Polygon]:
        """
        根据航道边界点创建多边形区域
        """
        if not self.waterway_boundary or len(self.waterway_boundary) < 3:
            return None

        try:
            points = self.waterway_boundary
            if points[0] != points[-1]:
                points.append(points[0])
            return Polygon(points, alpha=0.3, facecolor='lightblue', edgecolor='blue')
        except Exception as e:
            print(f"Error creating waterway polygon: {e}")
            raise

    def _point_in_polygon(self, point: Tuple[float, float]) -> bool:
        """
        判断点是否在航道多边形内
        """
        if not self.waterway_boundary:
            return True

        x, y = point
        n = len(self.waterway_boundary)
        inside = False

        p1x, p1y = self.waterway_boundary[0]
        for i in range(1, n + 1):
            p2x, p2y = self.waterway_boundary[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _point_in_polygon_subregion(self, point: Tuple[float, float], polygon_coords: List[Tuple[float, float]]) -> bool:
        """
        判断点是否在指定多边形内（射线法）
        
        参数:
        point: (lon, lat) 待检测点
        polygon_coords: [(lon1,lat1), (lon2,lat2), ...] 多边形顶点坐标
        
        返回:
        bool: True if 点在多边形内
        """
        if not polygon_coords or len(polygon_coords) < 3:
            return False
            
        x, y = point
        n = len(polygon_coords)
        inside = False

        p1x, p1y = polygon_coords[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_coords[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _determine_ship_subregion(self, ship: Dict) -> Optional[str]:
        """
        确定船舶所属的子区域
        
        参数:
        ship: 船舶信息字典，包含 'lon', 'lat'
        
        返回:
        str: 子区域标识 ('subregion_1', 'subregion_2', 'subregion_3', 'subregion_4') 或 None
        """
        ship_point = (ship['lon'], ship['lat'])
        
        # 按优先级顺序检查各子区域
        for subregion_id in ['subregion_1', 'subregion_2', 'subregion_3', 'subregion_4']:
            if subregion_id in self.subregions:
                polygon_coords = self.subregions[subregion_id]
                if self._point_in_polygon_subregion(ship_point, polygon_coords):
                    return subregion_id
        
        return None  # 不在任何子区域内

    def _calculate_distance_between_points(self, point1: Tuple[float, float],
                                           point2: Tuple[float, float]) -> float:
        """计算两点间距离（米）"""
        lon1, lat1 = point1
        lon2, lat2 = point2

        d_lon = math.radians(lon2 - lon1)
        d_lat = math.radians(lat2 - lat1)
        lat1_rad = math.radians(lat1)

        dx = d_lon * self.R_EARTH * math.cos(lat1_rad)
        dy = d_lat * self.R_EARTH
        return math.sqrt(dx ** 2 + dy ** 2)

    def _calculate_waterway_length_fitted(self) -> float:
        """计算拟合航道总长度"""
        return self.waterway_function['total_length']

    def _calculate_waterway_length_original(self) -> float:
        """计算原始航道总长度"""
        total_length = 0
        for i in range(len(self.waterway_points) - 1):
            total_length += self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )
        return total_length

    def _project_point_to_segment(self, point: Tuple[float, float],
                                  seg_start: Tuple[float, float],
                                  seg_end: Tuple[float, float]) -> Dict:
        """
        将点投影到线段上
        """
        px, py = point
        x1, y1 = seg_start
        x2, y2 = seg_end

        # 线段向量
        dx = x2 - x1
        dy = y2 - y1

        if dx == 0 and dy == 0:
            # 线段退化为点
            distance = self._calculate_distance_between_points(point, seg_start)
            return {
                'projection_point': seg_start,
                'distance': distance,
                'parameter': 0
            }

        # 计算投影参数
        t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)

        # 限制在线段范围内
        t = max(0, min(1, t))

        # 计算投影点
        proj_x = x1 + t * dx
        proj_y = y1 + t * dy

        # 计算距离
        distance = self._calculate_distance_between_points(point, (proj_x, proj_y))

        return {
            'projection_point': (proj_x, proj_y),
            'distance': distance,
            'parameter': t
        }

    def _project_ship_to_waterway_original(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到原始航道中心线上（使用线段）
        """
        ship_lon, ship_lat = ship['lon'], ship['lat']

        min_distance = float('inf')
        best_projection = None
        best_segment = 0
        best_parameter = 0

        total_length = 0
        segment_lengths = []

        # 计算各线段长度
        for i in range(len(self.waterway_points) - 1):
            segment_length = self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )
            segment_lengths.append(segment_length)
            total_length += segment_length

        cumulative_length = 0

        # 对每个线段进行投影
        for i in range(len(self.waterway_points) - 1):
            p1 = self.waterway_points[i]
            p2 = self.waterway_points[i + 1]

            # 计算点到线段的投影
            projection_info = self._project_point_to_segment(
                (ship_lon, ship_lat), p1, p2
            )

            if projection_info['distance'] < min_distance:
                min_distance = projection_info['distance']
                best_projection = projection_info['projection_point']
                best_segment = i

                # 计算全局参数
                segment_parameter = projection_info['parameter']
                best_parameter = (cumulative_length + segment_parameter * segment_lengths[i]) / total_length

            cumulative_length += segment_lengths[i]

        return {
            'parameter': best_parameter,
            'distance_to_centerline': min_distance,
            'projection_point': best_projection,
            'segment_index': best_segment
        }

    def _project_ship_to_waterway_fitted(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到拟合的航道中心线上
        """
        ship_lon, ship_lat = ship['lon'], ship['lat']

        # 定义目标函数：船舶到航道中心线上某点的距离
        def distance_to_centerline(t):
            if t < 0:
                t = 0
            elif t > 1:
                t = 1

            try:
                centerline_lon = float(self.waterway_function['lon_func'](t))
                centerline_lat = float(self.waterway_function['lat_func'](t))
                return self._calculate_distance_between_points(
                    (ship_lon, ship_lat), (centerline_lon, centerline_lat)
                )
            except:
                return float('inf')

        # 使用优化算法找到最近点
        result = minimize_scalar(distance_to_centerline, bounds=(0, 1), method='bounded')

        optimal_t = result.x
        min_distance = result.fun

        # 计算投影点坐标
        projection_lon = float(self.waterway_function['lon_func'](optimal_t))
        projection_lat = float(self.waterway_function['lat_func'](optimal_t))

        return {
            'parameter': optimal_t,
            'distance_to_centerline': min_distance,
            'projection_point': (projection_lon, projection_lat)
        }

    def _project_ship_to_waterway(self, ship: Dict) -> Dict:
        """
        将船舶位置投影到航道中心线上
        根据use_fitted_curve参数选择使用原始线段还是拟合曲线
        """
        if self.use_fitted_curve:
            return self._project_ship_to_waterway_fitted(ship)
        else:
            return self._project_ship_to_waterway_original(ship)

    def _determine_ship_side(self, ship: Dict) -> str:
        """
        判断船舶在航道中心线的左侧还是右侧
        返回: 'left' 或 'right'
        """
        ship_lon, ship_lat = ship['lon'], ship['lat']
        projection_point = ship['projection_point']
        parameter = ship['parameter']
        
        # 获取航道在投影点处的方向向量
        waterway_direction_rad = math.radians(self._get_waterway_direction_at_parameter(parameter))
        
        # 航道方向向量 (单位向量)
        waterway_dx = math.sin(waterway_direction_rad)
        waterway_dy = math.cos(waterway_direction_rad)
        
        # 从投影点到船舶的向量
        ship_dx = ship_lon - projection_point[0]
        ship_dy = ship_lat - projection_point[1]
        
        # 使用向量叉积判断左右
        # 叉积 = waterway_vector × ship_vector (2D叉积的z分量)
        cross_product = waterway_dx * ship_dy - waterway_dy * ship_dx
        
        # cross_product > 0: 船舶在航道右侧 (从航道起点看)
        # cross_product < 0: 船舶在航道左侧 (从航道起点看)
        return 'right' if cross_product > 0 else 'left'

    def _determine_ship_direction(self, ship: Dict) -> str:
        """
        判断船舶是上行还是下行
        返回: 'upstream' (上行) 或 'downstream' (下行)
        """
        ship_heading = ship['cog']
        waterway_direction = self._get_waterway_direction_at_parameter(ship['parameter'])
        
        # 计算船舶航向与航道方向的夹角
        angle_diff = abs(ship_heading - waterway_direction)
        angle_diff = min(angle_diff, 360 - angle_diff)  # 取较小的角度
        
        # 如果夹角小于90度，认为是下行（顺航道方向）
        # 如果夹角大于90度，认为是上行（逆航道方向）
        return 'downstream' if angle_diff < 90 else 'upstream'

    def _ships_can_encounter(self, ship1: Dict, ship2: Dict) -> bool:
        """
        判断两艘船舶是否可能构成会遇关系
        条件：1. 必须在航道同一侧  2. 必须是同向行驶
        """
        # 判断是否在同一侧
        side1 = self._determine_ship_side(ship1)
        side2 = self._determine_ship_side(ship2)
        
        if side1 != side2:
            return False  # 不在同一侧，不构成会遇
        
        # 判断是否同向
        direction1 = self._determine_ship_direction(ship1)
        direction2 = self._determine_ship_direction(ship2)
        
        if direction1 != direction2:
            return False  # 不同向，不构成会遇
        
        return True

    def _sort_ships_by_waterway_position(self, ships: List[Dict]) -> List[Dict]:
        """
        根据船舶在航道中心线上的投影位置对船舶进行排序
        并添加侧别和方向信息
        """
        ships_with_projection = []

        for ship in ships:
            projection_info = self._project_ship_to_waterway(ship)
            ship_with_projection = ship.copy()
            ship_with_projection.update(projection_info)
            
            # 添加侧别和方向信息
            ship_with_projection['side'] = self._determine_ship_side(ship_with_projection)
            ship_with_projection['direction'] = self._determine_ship_direction(ship_with_projection)
            
            ships_with_projection.append(ship_with_projection)

        # 按参数位置排序
        ships_with_projection.sort(key=lambda x: x['parameter'])

        return ships_with_projection

    def _classify_ships_by_subregion(self, ships: List[Dict]) -> Dict[str, List[Dict]]:
        """
        根据子区域对船舶进行分类
        
        参数:
        ships: 船舶列表
        
        返回:
        Dict[str, List[Dict]]: {subregion_id: [ships_in_this_region]}
        """
        grouped_ships = {}
        
        for ship in ships:
            # 确定船舶所属子区域
            subregion = self._determine_ship_subregion(ship)
            
            if subregion:  # 只处理在子区域内的船舶
                ship['subregion'] = subregion  # 添加子区域信息
                
                if subregion not in grouped_ships:
                    grouped_ships[subregion] = []
                grouped_ships[subregion].append(ship)
        
        return grouped_ships

    def _sort_ships_in_subregion(self, ships: List[Dict]) -> List[Dict]:
        """
        对同一子区域内的船舶进行排序（仍然可以按航道投影位置排序）
        """
        ships_with_projection = []
        
        for ship in ships:
            # 计算投影信息（用于排序）
            projection_info = self._project_ship_to_waterway(ship)
            ship_with_projection = ship.copy()
            ship_with_projection.update(projection_info)
            
            # 添加侧别和方向信息（用于进一步筛选）
            ship_with_projection['side'] = self._determine_ship_side(ship_with_projection)
            ship_with_projection['direction'] = self._determine_ship_direction(ship_with_projection)
            
            ships_with_projection.append(ship_with_projection)

        # 按航道位置参数排序
        ships_with_projection.sort(key=lambda x: x['parameter'])
        return ships_with_projection

    def _calculate_tcpa(self, ship1: Dict, ship2: Dict) -> float:
        """计算两船的TCPA"""
        # 坐标转换
        d_lon = math.radians(ship2['lon'] - ship1['lon'])
        d_lat = math.radians(ship2['lat'] - ship1['lat'])
        lat1_rad = math.radians(ship1['lat'])

        px = d_lon * self.R_EARTH * math.cos(lat1_rad)
        py = d_lat * self.R_EARTH

        # 速度向量转换
        sog1_ms = ship1['sog'] * self.KNOTS_TO_MS
        sog2_ms = ship2['sog'] * self.KNOTS_TO_MS

        cog1_rad = math.radians(ship1['cog'])
        cog2_rad = math.radians(ship2['cog'])

        v1x = sog1_ms * math.sin(cog1_rad)
        v1y = sog1_ms * math.cos(cog1_rad)
        v2x = sog2_ms * math.sin(cog2_rad)
        v2y = sog2_ms * math.cos(cog2_rad)

        # 相对速度
        vrx = v2x - v1x
        vry = v2y - v1y
        vr_sq = vrx ** 2 + vry ** 2

        if vr_sq == 0:
            return float('inf')  # 相对静止

        # TCPA计算
        dot_product = px * vrx + py * vry
        tcpa = -dot_product / vr_sq

        return tcpa

    def _get_waterway_direction_at_parameter(self, parameter: float) -> float:
        """
        获取航道在指定参数位置处的方向
        """
        if self.use_fitted_curve and self.waterway_function:
            # 使用拟合曲线计算方向
            dt = 0.01
            t1 = max(0, min(1, parameter - dt / 2))
            t2 = max(0, min(1, parameter + dt / 2))

            try:
                lon1 = float(self.waterway_function['lon_func'](t1))
                lat1 = float(self.waterway_function['lat_func'](t1))
                lon2 = float(self.waterway_function['lon_func'](t2))
                lat2 = float(self.waterway_function['lat_func'](t2))

                direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
                if direction < 0:
                    direction += 360
                return direction
            except:
                pass

        # 使用原始线段计算方向
        # 找到参数对应的线段
        cumulative_length = 0
        total_length = self._calculate_waterway_length_original()
        target_length = parameter * total_length

        for i in range(len(self.waterway_points) - 1):
            segment_length = self._calculate_distance_between_points(
                self.waterway_points[i], self.waterway_points[i + 1]
            )

            if cumulative_length + segment_length >= target_length:
                # 在这个线段上
                lon1, lat1 = self.waterway_points[i]
                lon2, lat2 = self.waterway_points[i + 1]

                direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
                if direction < 0:
                    direction += 360
                return direction

            cumulative_length += segment_length

        # 如果到达末尾，使用最后一个线段的方向
        if len(self.waterway_points) >= 2:
            lon1, lat1 = self.waterway_points[-2]
            lon2, lat2 = self.waterway_points[-1]
            direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
            if direction < 0:
                direction += 360
            return direction

        return 0  # 默认值

    def _is_ship_moving_along_waterway(self, ship: Dict, tolerance_angle: float = 45.0) -> bool:
        """
        判断船舶是否沿着航道方向航行
        """
        # 获取航道在投影点处的方向
        waterway_direction = self._get_waterway_direction_at_parameter(ship['parameter'])

        # 计算船舶航向与航道方向的夹角
        ship_heading = ship['cog']
        angle_diff = abs(ship_heading - waterway_direction)
        angle_diff = min(angle_diff, 360 - angle_diff)  # 取较小的角度

        # 也考虑反向航行的情况
        reverse_angle_diff = abs(angle_diff - 180)

        return min(angle_diff, reverse_angle_diff) <= tolerance_angle

    def detect_encounters_efficient(self, ships: List[Dict]) -> Dict[int, List[int]]:
        """
        基于子区域的高效会遇检测方法
        只检测同一子区域内同侧同向的船舶会遇关系
        """
        if not ships:
            return {}

        encounter_results = {ship['id']: [] for ship in ships}
        
        # 如果没有定义子区域，回退到原始方法
        if not self.subregions:
            return self._detect_encounters_original(ships)
        
        # 1. 按子区域分组船舶
        ships_by_subregion = self._classify_ships_by_subregion(ships)
        
        # 2. 在每个子区域内进行会遇检测
        for subregion_id, subregion_ships in ships_by_subregion.items():
            if len(subregion_ships) < 2:
                continue  # 子区域内船舶数量不足，跳过
                
            # 2.1 对子区域内船舶排序并添加投影信息
            sorted_subregion_ships = self._sort_ships_in_subregion(subregion_ships)
            
            # 2.2 过滤沿航道航行的船舶
            waterway_ships = [ship for ship in sorted_subregion_ships
                              if self._is_ship_moving_along_waterway(ship)]
            
            if len(waterway_ships) < 2:
                continue
                
            # 2.3 进一步按侧别和方向分组（在子区域内）
            grouped_ships = {}
            for ship in waterway_ships:
                key = f"{ship['side']}_{ship['direction']}"
                if key not in grouped_ships:
                    grouped_ships[key] = []
                grouped_ships[key].append(ship)
            
            # 2.4 检测同组内的会遇关系
            for group_key, group_ships in grouped_ships.items():
                group_ships.sort(key=lambda x: x['parameter'])
                
                for i in range(len(group_ships)):
                    current_ship = group_ships[i]
                    
                    # 检查前一艘船
                    if i > 0:
                        prev_ship = group_ships[i - 1]
                        tcpa = self._calculate_tcpa(current_ship, prev_ship)
                        if 0 < tcpa <= self.tcpa_threshold:
                            encounter_results[current_ship['id']].append(prev_ship['id'])

                    # 检查后一艘船
                    if i < len(group_ships) - 1:
                        next_ship = group_ships[i + 1]
                        tcpa = self._calculate_tcpa(current_ship, next_ship)
                        if 0 < tcpa <= self.tcpa_threshold:
                            encounter_results[current_ship['id']].append(next_ship['id'])

        return encounter_results

    def _detect_encounters_original(self, ships: List[Dict]) -> Dict[int, List[int]]:
        """
        原始的会遇检测方法（兼容性保证）
        当没有定义子区域时使用此方法
        """
        if not ships:
            return {}

        # 1. 对船舶按航道位置排序并添加侧别方向信息
        sorted_ships = self._sort_ships_by_waterway_position(ships)

        # 2. 过滤掉不沿航道航行的船舶
        waterway_ships = [ship for ship in sorted_ships
                          if self._is_ship_moving_along_waterway(ship)]

        encounter_results = {ship['id']: [] for ship in ships}

        # 3. 按侧别和方向分组船舶
        grouped_ships = {}
        for ship in waterway_ships:
            key = f"{ship['side']}_{ship['direction']}"
            if key not in grouped_ships:
                grouped_ships[key] = []
            grouped_ships[key].append(ship)

        # 4. 在每个组内检查会遇关系
        for group_key, group_ships in grouped_ships.items():
            # 按航道位置重新排序
            group_ships.sort(key=lambda x: x['parameter'])
            
            # 只检查同组内相邻船舶的会遇关系
            for i in range(len(group_ships)):
                current_ship = group_ships[i]
                
                # 检查前一艘船
                if i > 0:
                    prev_ship = group_ships[i - 1]
                    tcpa = self._calculate_tcpa(current_ship, prev_ship)
                    if 0 < tcpa <= self.tcpa_threshold:
                        encounter_results[current_ship['id']].append(prev_ship['id'])

                # 检查后一艘船
                if i < len(group_ships) - 1:
                    next_ship = group_ships[i + 1]
                    tcpa = self._calculate_tcpa(current_ship, next_ship)
                    if 0 < tcpa <= self.tcpa_threshold:
                        encounter_results[current_ship['id']].append(next_ship['id'])

        return encounter_results

        # 1. 对船舶按航道位置排序并添加侧别方向信息
        sorted_ships = self._sort_ships_by_waterway_position(ships)

        # 2. 过滤掉不沿航道航行的船舶
        waterway_ships = [ship for ship in sorted_ships
                          if self._is_ship_moving_along_waterway(ship)]

        encounter_results = {ship['id']: [] for ship in ships}

        # 3. 按侧别和方向分组船舶
        grouped_ships = {}
        for ship in waterway_ships:
            key = f"{ship['side']}_{ship['direction']}"
            if key not in grouped_ships:
                grouped_ships[key] = []
            grouped_ships[key].append(ship)

        # 4. 在每个组内检查会遇关系
        for group_key, group_ships in grouped_ships.items():
            # 按航道位置重新排序
            group_ships.sort(key=lambda x: x['parameter'])
            
            # 只检查同组内相邻船舶的会遇关系
            for i in range(len(group_ships)):
                current_ship = group_ships[i]
                
                # 检查前一艘船
                if i > 0:
                    prev_ship = group_ships[i - 1]
                    tcpa = self._calculate_tcpa(current_ship, prev_ship)
                    if 0 < tcpa <= self.tcpa_threshold:
                        encounter_results[current_ship['id']].append(prev_ship['id'])

                # 检查后一艘船
                if i < len(group_ships) - 1:
                    next_ship = group_ships[i + 1]
                    tcpa = self._calculate_tcpa(current_ship, next_ship)
                    if 0 < tcpa <= self.tcpa_threshold:
                        encounter_results[current_ship['id']].append(next_ship['id'])

        return encounter_results

    def get_ship_positions_on_waterway(self, ships: List[Dict]) -> List[Dict]:
        """
        获取所有船舶在航道上的位置信息
        """
        return self._sort_ships_by_waterway_position(ships)

    def generate_ships_in_waterway(self, num_ships: int = 10,
                                   speed_range: Tuple[float, float] = (6, 12)) -> List[Dict]:
        """
        在航道区域内生成测试船舶
        """
        if not self.waterway_boundary:
            return self._generate_ships_near_centerline(num_ships, speed_range)

        ships = []
        attempts = 0
        max_attempts = num_ships * 10

        # 计算航道边界的范围
        lons = [point[0] for point in self.waterway_boundary]
        lats = [point[1] for point in self.waterway_boundary]
        min_lon, max_lon = min(lons), max(lons)
        min_lat, max_lat = min(lats), max(lats)

        ship_id = 1
        while len(ships) < num_ships and attempts < max_attempts:
            attempts += 1

            # 在边界矩形内随机生成点
            lon = random.uniform(min_lon, max_lon)
            lat = random.uniform(min_lat, max_lat)

            # 检查是否在航道多边形内
            if self._point_in_polygon((lon, lat)):
                # 计算该位置在航道中心线上的投影，以确定航道方向
                temp_ship = {'lon': lon, 'lat': lat}
                projection_info = self._project_ship_to_waterway(temp_ship)

                # 获取航道方向
                waterway_direction = self._get_waterway_direction_at_parameter(projection_info['parameter'])

                # 船舶航向沿着航道方向或相反方向
                if random.random() < 0.7:  # 70%概率沿航道方向
                    base_cog = waterway_direction
                else:  # 30%概率反向
                    base_cog = (waterway_direction + 180) % 360

                # 添加随机偏差
                cog = (base_cog + random.uniform(-20, 20)) % 360

                ship = {
                    'id': ship_id,
                    'lon': lon,
                    'lat': lat,
                    'sog': random.uniform(speed_range[0], speed_range[1]),
                    'cog': cog
                }
                ships.append(ship)
                ship_id += 1

        return ships

    def _generate_ships_near_centerline(self, num_ships: int,
                                        speed_range: Tuple[float, float]) -> List[Dict]:
        """
        在航道中心线附近生成船舶（当没有边界时使用）
        """
        ships = []

        for i in range(num_ships):
            # 随机选择航道中心线上的一个位置
            segment_idx = random.randint(0, len(self.waterway_points) - 2)
            t = random.uniform(0, 1)

            # 在线段上插值
            lon1, lat1 = self.waterway_points[segment_idx]
            lon2, lat2 = self.waterway_points[segment_idx + 1]

            center_lon = lon1 + t * (lon2 - lon1)
            center_lat = lat1 + t * (lat2 - lat1)

            # 添加随机偏移
            lon = center_lon + random.uniform(-0.002, 0.002)
            lat = center_lat + random.uniform(-0.002, 0.002)

            # 计算航道方向
            waterway_direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
            if waterway_direction < 0:
                waterway_direction += 360

            # 船舶航向
            if random.random() < 0.7:
                base_cog = waterway_direction
            else:
                base_cog = (waterway_direction + 180) % 360

            cog = (base_cog + random.uniform(-20, 20)) % 360

            ship = {
                'id': i + 1,
                'lon': lon,
                'lat': lat,
                'sog': random.uniform(speed_range[0], speed_range[1]),
                'cog': cog
            }
            ships.append(ship)

        return ships

    def _draw_ship_arrow(self, ax, ship: Dict, color: str, scale: float = 0.0015):
        """绘制船舶位置和航向箭头"""
        lon, lat = ship['lon'], ship['lat']
        cog, sog = ship['cog'], ship['sog']

        # 绘制船舶位置点
        ax.scatter(lon, lat, c=color, s=12, zorder=5, edgecolors='black', linewidth=1.5)

        # 绘制船舶ID
        ax.annotate(f"{ship['id']}", (lon, lat), xytext=(6, 6),
                    textcoords='offset points', fontsize=9, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.8))

        # 计算箭头终点（箭头长度与速度成比例）
        arrow_length = sog * scale
        cog_rad = math.radians(cog)
        end_lon = lon + arrow_length * math.sin(cog_rad)
        end_lat = lat + arrow_length * math.cos(cog_rad)

        # 绘制航向箭头
        arrow = FancyArrowPatch((lon, lat), (end_lon, end_lat),
                                arrowstyle='->', mutation_scale=2,
                                color=color, linewidth=1, zorder=1)
        ax.add_patch(arrow)

    def visualize_enhanced_waterway_and_ships(self, ships: List[Dict],
                                              encounters: Optional[Dict[int, List[int]]] = None,
                                              show_tcpa: bool = True):
        """
        增强版可视化航道中心线和船舶位置
        """
        fig, ax1 = plt.subplots(1, 1, figsize=(12, 8))
        ax1.set_title('航道实际位置图', fontsize=16, fontweight='bold')

        # 绘制航道边界（如果有）
        if self.waterway_polygon:
            ax1.add_patch(self.waterway_polygon)

        # 绘制航道中心线
        waterway_lons = [point[0] for point in self.waterway_points]
        waterway_lats = [point[1] for point in self.waterway_points]
        ax1.plot(waterway_lons, waterway_lats, linewidth=2,
                 label='航道中心线', zorder=3)

        # 获取船舶投影信息
        ships_with_projection = self.get_ship_positions_on_waterway(ships)

        # 绘制船舶和投影
        for ship in ships_with_projection:
            # 确定船舶颜色
            has_encounter = encounters and encounters.get(ship['id'], [])
            color = 'red' if has_encounter else 'blue'

            # 绘制船舶位置和航向箭头
            self._draw_ship_arrow(ax1, ship, color)

            # 绘制投影线
            ax1.plot([ship['lon'], ship['projection_point'][0]],
                    [ship['lat'], ship['projection_point'][1]],
                    color='gray', alpha=0.3, linewidth=1, linestyle=':')

        # 绘制会遇关系线
        if encounters:
            for ship in ships_with_projection:
                ship_id = ship['id']
                for encounter_id in encounters.get(ship_id, []):
                    encounter_ship = next((s for s in ships_with_projection
                                         if s['id'] == encounter_id), None)
                    if encounter_ship:
                        # 绘制会遇关系线
                        ax1.plot([ship['lon'], encounter_ship['lon']],
                                [ship['lat'], encounter_ship['lat']],
                                'purple', alpha=0.8, linewidth=2, linestyle='--',
                                zorder=4)

                        # 显示TCPA值
                        if show_tcpa:
                            tcpa = self._calculate_tcpa(ship, encounter_ship)
                            mid_lon = (ship['lon'] + encounter_ship['lon']) / 2
                            mid_lat = (ship['lat'] + encounter_ship['lat']) / 2
                            ax1.annotate(f"TCPA:{tcpa:.1f}s", (mid_lon, mid_lat),
                                       fontsize=9, ha='center', va='center',
                                       bbox=dict(boxstyle='round,pad=0.3',
                                               facecolor='yellow', alpha=0.9))

        ax1.set_xlabel('经度 (度)', fontsize=12)
        ax1.set_ylabel('纬度 (度)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_aspect('equal')

        plt.tight_layout()
        plt.show()

    def get_subregion_ship_distribution(self, ships: List[Dict]) -> Dict:
        """
        获取船舶在各子区域的分布统计
        """
        distribution = {}
        ships_by_subregion = self._classify_ships_by_subregion(ships)
        
        for subregion_id, subregion_ships in ships_by_subregion.items():
            distribution[subregion_id] = {
                'ship_count': len(subregion_ships),
                'ship_ids': [ship['id'] for ship in subregion_ships]
            }
        
        # 统计不在任何子区域的船舶
        ships_with_subregion = set()
        for ships_list in ships_by_subregion.values():
            ships_with_subregion.update(ship['id'] for ship in ships_list)
        
        all_ship_ids = set(ship['id'] for ship in ships)
        ships_outside = all_ship_ids - ships_with_subregion
        
        if ships_outside:
            distribution['outside_subregions'] = {
                'ship_count': len(ships_outside),
                'ship_ids': list(ships_outside)
            }
        
        return distribution

    def has_subregions_defined(self) -> bool:
        """
        检查是否定义了子区域
        """
        return bool(self.subregions)

    def get_subregion_names(self) -> List[str]:
        """
        获取所有已定义的子区域名称
        """
        return list(self.subregions.keys())
