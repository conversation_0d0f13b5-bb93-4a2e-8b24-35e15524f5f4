def is_point_in_polygon(point_lat, point_lon, polygon_coords):
    """
    检测某个经纬度坐标是否在一系列经纬度坐标围成的区域内

    使用射线法(Ray Casting Algorithm)：
    从待测点向右发射水平射线，统计与多边形边界的交点个数
    奇数个交点 = 点在多边形内，偶数个交点 = 点在多边形外

    参数:
    point_lat: 待检测点的纬度
    point_lon: 待检测点的经度
    polygon_coords: 多边形顶点坐标列表，格式为 [(lat1, lon1), (lat2, lon2), ...]
                   注意：多边形应该是闭合的（首尾相连）

    返回:
    bool: True表示点在多边形内，False表示在多边形外
    """
    if len(polygon_coords) < 3:
        raise ValueError("多边形至少需要3个顶点")

    # 确保多边形是闭合的
    if polygon_coords[0] != polygon_coords[-1]:
        polygon_coords = polygon_coords + [polygon_coords[0]]

    x, y = point_lon, point_lat  # 待测点坐标
    n = len(polygon_coords)
    inside = False

    # 遍历多边形的每条边
    p1x, p1y = polygon_coords[0][1], polygon_coords[0][0]  # 第一个顶点(lon, lat)

    for i in range(1, n):
        p2x, p2y = polygon_coords[i][1], polygon_coords[i][0]  # 当前顶点(lon, lat)

        # 检查射线是否与当前边相交
        if y > min(p1y, p2y):  # 点的纬度大于边的最小纬度
            if y <= max(p1y, p2y):  # 点的纬度小于等于边的最大纬度
                if x <= max(p1x, p2x):  # 点的经度小于等于边的最大经度
                    if p1y != p2y:  # 边不是水平的
                        # 计算射线与边的交点的经度
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x

                    if p1x == p2x or x <= xinters:  # 边是垂直的，或点在交点左侧
                        inside = not inside  # 切换内外状态

        p1x, p1y = p2x, p2y  # 移动到下一条边

    return inside


def is_point_in_polygon_optimized(point_lat, point_lon, polygon_coords):
    """
    优化版本：处理边界情况，提高精度

    参数同上
    """
    if len(polygon_coords) < 3:
        raise ValueError("多边形至少需要3个顶点")

    # 确保多边形是闭合的
    coords = polygon_coords[:]
    if coords[0] != coords[-1]:
        coords.append(coords[0])

    x, y = point_lon, point_lat
    n = len(coords)
    inside = False

    j = n - 1  # 最后一个顶点的索引

    for i in range(n):
        xi, yi = coords[i][1], coords[i][0]  # 当前顶点
        xj, yj = coords[j][1], coords[j][0]  # 前一个顶点

        # 检查点是否正好在顶点上
        if abs(xi - x) < 1e-10 and abs(yi - y) < 1e-10:
            return True

        # 检查射线与边的相交
        if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
            inside = not inside

        j = i

    return inside


def calculate_polygon_area(polygon_coords):
    """
    计算多边形面积（平方度）
    使用鞋带公式(Shoelace formula)

    参数:
    polygon_coords: 多边形顶点坐标列表 [(lat1, lon1), (lat2, lon2), ...]

    返回:
    float: 多边形面积（平方度）
    """
    if len(polygon_coords) < 3:
        return 0

    # 确保多边形闭合
    coords = polygon_coords[:]
    if coords[0] != coords[-1]:
        coords.append(coords[0])

    area = 0
    n = len(coords)

    for i in range(n - 1):
        lat1, lon1 = coords[i]
        lat2, lon2 = coords[i + 1]
        area += (lon1 * lat2 - lon2 * lat1)

    return abs(area) / 2


def get_polygon_bounds(polygon_coords):
    """
    获取多边形的边界框

    参数:
    polygon_coords: 多边形顶点坐标列表

    返回:
    dict: 包含最小/最大经纬度的字典
    """
    if not polygon_coords:
        return None

    lats = [coord[0] for coord in polygon_coords]
    lons = [coord[1] for coord in polygon_coords]

    return {
        'min_lat': min(lats),
        'max_lat': max(lats),
        'min_lon': min(lons),
        'max_lon': max(lons)
    }


def is_point_in_polygon_with_bounds_check(point_lat, point_lon, polygon_coords, bounds=None):
    """
    带边界框预检查的点在多边形判断（提高性能）

    参数:
    point_lat: 待检测点纬度
    point_lon: 待检测点经度
    polygon_coords: 多边形坐标列表
    bounds: 预计算的边界框，如果为None则自动计算

    返回:
    bool: True表示点在多边形内
    """
    # 边界框预检查
    if bounds is None:
        bounds = get_polygon_bounds(polygon_coords)

    # 如果点不在边界框内，肯定不在多边形内
    if (point_lat < bounds['min_lat'] or point_lat > bounds['max_lat'] or
            point_lon < bounds['min_lon'] or point_lon > bounds['max_lon']):
        return False

    # 进行精确的多边形检查
    return is_point_in_polygon_optimized(point_lat, point_lon, polygon_coords)
