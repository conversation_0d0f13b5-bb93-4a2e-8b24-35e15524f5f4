# 穿越场景提取算法
import numpy as np
from shapely.geometry import Polygon, LineString, Point
from tqdm import tqdm


class CrossSceneExtraction:
    def __init__(self, trajectory_list, tras_df, geo_information):
        self.trajectory_list = trajectory_list
        self.tras_df = tras_df

        self.channel_side1 = geo_information['channel_side1']
        self.channel_side2 = geo_information['channel_side2']
        self.anchorage = geo_information['anchorage']
        self.channel_polygon = geo_information['channel_boundary']

    def alls_indentify(self, own_ship_list):
        # idx = random.randrange(len(own_ship_list))
        # own_ship = self.trajectory_list[own_ship_list[idx]]
        own_ship = self.trajectory_list[9829]
        ts, te = own_ship['PosTime'].values[0], own_ship['PosTime'].values[-1]
        # print(own_ship_list[idx], ts, te)
        print(9829, ts, te)
        all_ships = self.tras_df.loc[(self.tras_df['PosTime'] >= ts) & (self.tras_df['PosTime'] <= te)]
        all_ships_dicts = []
        for postime in tqdm(all_ships['PosTime'].unique(), desc='step2'):
            # 'PosTime', 'MMSI', 'Lon', 'Lat', 'Cog', 'Sog', 'Type', 'Width', 'Length'
            all_ships_array = np.array(all_ships[all_ships['PosTime'] == postime])
            all_ships_dicts.append([{
                'PosTime': postime,
                'id': int(all_ships_array[i][1]),
                'lon': float(all_ships_array[i][2]),
                'lat': float(all_ships_array[i][3]),
                'cog': float(all_ships_array[i][4]),
                'sog': float(all_ships_array[i][5])} for i in range(len(all_ships_array))])
        return all_ships_dicts

    def cross_owns_indentify(self):
        own_ship_list = []
        for k, tra in enumerate(tqdm(self.trajectory_list, desc='step1')):
            track = [(Lon, lat) for Lon, lat in zip(tra['Lon'], tra['Lat'])]
            if len(track) < 2:
                continue  # 跳过点数不足2的轨迹
            # if (self.is_channel_truly_crossed(track, self.channel_side1, self.channel_side2, consider_direction=True)
            #         & self.is_in_area(track, self.anchorage)):
            if self.is_channel_truly_crossed(track, self.channel_side1, self.channel_side2,
                                             consider_direction=False):
                own_ship_list.append(k)
        return own_ship_list

    def is_channel_truly_crossed(self, track_points, channel_side1, channel_side2, consider_direction=True):
        """判断轨迹是否先穿过side1再穿过side2"""
        track_line = LineString(track_points)
        cross1 = self.find_cross_points(track_line, channel_side1)
        cross2 = self.find_cross_points(track_line, channel_side2)

        # 必须两条线都有交点
        if not cross1 or not cross2:
            return False

        # 找出所有交点对应的轨迹索引
        idx1_list = []
        for pt in cross1:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx1_list.append(min_idx)

        idx2_list = []
        for pt in cross2:
            min_idx = min(range(len(track_points)), key=lambda i: Point(track_points[i]).distance(pt))
            idx2_list.append(min_idx)

        # 只考虑有方向的情况
        if consider_direction:
            # 找到最早的side1交点
            first_side1_idx = min(idx1_list)
            # 检查是否存在在这个side1交点之后的side2交点
            later_side2_indices = [idx for idx in idx2_list if idx > first_side1_idx]
            return len(later_side2_indices) > 0
        else:
            # 如果不考虑方向，只要两条线都穿过就行
            return True

    @staticmethod
    def find_cross_points(track_line, side_edges):
        """返回轨迹线与一组边的所有交点"""
        # cross_points = []
        # for i in range(len(side_edges) - 1):
        #     edge = LineString([side_edges[i], side_edges[i + 1]])
        #     inter = track_line.intersection(edge)
        #     if not inter.is_empty:
        #         if inter.geom_type == 'Point':
        #             cross_points.append(inter)
        #         elif inter.geom_type == 'MultiPoint':
        #             cross_points.extend(list(inter.geoms))
        #         elif inter.geom_type == 'LineString':
        #             # 处理线段重叠情况，取端点
        #             cross_points.extend([Point(inter.coords[0]), Point(inter.coords[-1])])
        #         elif inter.geom_type == 'GeometryCollection':
        #             # 处理所有几何类型
        #             for g in inter.geoms:
        #                 if g.geom_type == 'Point':
        #                     cross_points.append(g)
        #                 elif g.geom_type == 'LineString':
        #                     cross_points.extend([Point(g.coords[0]), Point(g.coords[-1])])
        #
        # # 去除重复点（距离小于1米认为是同一点）
        # unique_points = []
        # for pt in cross_points:
        #     is_duplicate = False
        #     for existing_pt in unique_points:
        #         if pt.distance(existing_pt) < 1.0:  # 1米容差
        #             is_duplicate = True
        #             break
        #     if not is_duplicate:
        #         unique_points.append(pt)
        #
        # return unique_points
        boundary_line = LineString(side_edges)

        # 直接计算交点
        intersection = track_line.intersection(boundary_line)

        # 处理交点
        cross_points = []
        if not intersection.is_empty:
            if intersection.geom_type == 'Point':
                cross_points.append(intersection)
            elif intersection.geom_type == 'MultiPoint':
                cross_points.extend(list(intersection.geoms))

        return cross_points

    @staticmethod
    def is_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        pt = track_points[0]
        if poly.contains(Point(pt)):
            return True
        else:
            return False

    @staticmethod
    def all_in_area(track_points, polygon_points):
        poly = Polygon(polygon_points)
        for pt in track_points:
            if not poly.contains(Point(pt)):
                return False
        return True
