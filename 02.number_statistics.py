import os
import pickle
from collections import defaultdict
from pathlib import Path

import numpy as np
import pandas as pd
from tqdm import tqdm


class StatisticsCacheManager:
    """统计分析缓存管理器"""

    def __init__(self, cache_dir="result0/cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

    def get_cache_path(self, step_name):
        """获取缓存文件路径"""
        return self.cache_dir / f"cache_{step_name}.pkl"

    def is_cache_valid(self, step_name):
        """检查缓存是否有效"""
        cache_path = self.get_cache_path(step_name)
        return cache_path.exists()

    def load_cache(self, step_name):
        """加载缓存"""
        cache_path = self.get_cache_path(step_name)
        with open(cache_path, 'rb') as f:
            return pickle.load(f)

    def save_cache(self, step_name, data):
        """保存缓存"""
        cache_path = self.get_cache_path(step_name)
        with open(cache_path, 'wb') as f:
            pickle.dump(data, f)
        print(f"统计缓存已保存: {cache_path}")


# ================================
# 向量化计算函数
# ================================

def calculate_distances_vectorized(positions1, positions2):
    """完全向量化的距离计算"""
    if len(positions1) != len(positions2) or len(positions1) == 0:
        return np.array([])

    # 转换为numpy数组
    pos1 = np.array(positions1)  # shape: (n, 2) [lat, lon]
    pos2 = np.array(positions2)  # shape: (n, 2) [lat, lon]

    # 提取经纬度
    lat1, lon1 = pos1[:, 0], pos1[:, 1]
    lat2, lon2 = pos2[:, 0], pos2[:, 1]

    # 转换为弧度
    lat1, lon1, lat2, lon2 = np.radians([lat1, lon1, lat2, lon2])

    # 向量化的 haversine 公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2) ** 2
    distances = 2 * np.arcsin(np.sqrt(a)) * 6371000  # 地球半径6371km，返回米

    return distances


# ================================
# 距离统计函数
# ================================

def calculate_encounter_distances(encounters, tras_df):
    """
    计算会遇距离统计 - 基于连续会遇过程的轨迹数据
    不再设置时间窗口，直接使用会遇过程中的轨迹点，使用向量化计算
    """
    results = {
        'encounter_pairs': [],
        'start_distances': [],
        'min_distances': [],
        'min_distance_times': [],
        'end_distances': []
    }

    if len(encounters) == 0:
        return results

    print("正在预处理轨迹数据...")

    # 提取所有涉及的船舶MMSI
    involved_mmsis = set()
    for encounter in encounters:
        involved_mmsis.update(encounter['s_pair'])

    # 按MMSI分组并建立时间索引
    ship_trajectories = {}
    filtered_df = tras_df[tras_df['MMSI'].isin(involved_mmsis)]
    grouped = filtered_df.groupby('MMSI')

    for mmsi, group in tqdm(grouped, desc='建立船舶轨迹索引'):
        sorted_group = group.sort_values('PosTime')
        time_to_data = {}
        for _, row in sorted_group.iterrows():
            time_to_data[row['PosTime']] = {
                'Lat': row['Lat'],
                'Lon': row['Lon'],
                'Cog': row['Cog'],
                'Sog': row['Sog']
            }
        ship_trajectories[mmsi] = {
            'time_index': time_to_data,
            'times': set(sorted_group['PosTime'])
        }

    # 处理每个会遇过程
    print("正在计算距离...")
    for encounter in tqdm(encounters, desc='计算距离'):
        s_pair = encounter['s_pair']
        ship1_mmsi, ship2_mmsi = s_pair[0], s_pair[1]

        if ship1_mmsi not in ship_trajectories or ship2_mmsi not in ship_trajectories:
            continue

        ship1_traj = ship_trajectories[ship1_mmsi]
        ship2_traj = ship_trajectories[ship2_mmsi]

        # 直接使用会遇过程中记录的时间点
        encounter_times = encounter.get('encounter_times', [])
        if len(encounter_times) < 2:
            # 兜底逻辑：如果没有encounter_times，使用单个时间点
            encounter_times = [encounter['pos_time']]

        # 找到两船都有数据的时间点
        valid_times = []
        for time_point in encounter_times:
            if (time_point in ship1_traj['times'] and 
                time_point in ship2_traj['times']):
                valid_times.append(time_point)

        if len(valid_times) < 1:
            continue

        # 排序时间点
        valid_times = sorted(valid_times)

        # 向量化提取位置数据
        ship1_positions = []
        ship2_positions = []

        for time_point in valid_times:
            ship1_pos = ship1_traj['time_index'][time_point]
            ship2_pos = ship2_traj['time_index'][time_point]
            ship1_positions.append((ship1_pos['Lat'], ship1_pos['Lon']))
            ship2_positions.append((ship2_pos['Lat'], ship2_pos['Lon']))

        # 向量化计算所有距离
        distances = calculate_distances_vectorized(ship1_positions, ship2_positions)

        if len(distances) == 0:
            continue

        # 计算统计量
        min_distance_idx = np.argmin(distances)
        min_distance = distances[min_distance_idx]
        min_distance_time = valid_times[min_distance_idx]

        start_distance = distances[0]
        end_distance = distances[-1]

        # 保存结果
        results['encounter_pairs'].append(s_pair)
        results['start_distances'].append(start_distance)
        results['min_distances'].append(min_distance)
        results['min_distance_times'].append(min_distance_time)
        results['end_distances'].append(end_distance)

    return results


def calculate_distance_statistics(cross_related_encounters, non_cross_encounters, tras_df, cache_manager, use_cache=True):
    """计算距离统计"""
    step_name = "number_statistics_distance_stats"

    if use_cache and cache_manager.is_cache_valid(step_name):
        print("从缓存加载距离统计结果...")
        return cache_manager.load_cache(step_name)

    print("正在计算距离统计...")

    print("正在计算含穿越船会遇的距离统计...")
    cross_distance_stats = calculate_encounter_distances(cross_related_encounters, tras_df)

    print("正在计算不含穿越船会遇的距离统计...")
    non_cross_distance_stats = calculate_encounter_distances(non_cross_encounters, tras_df)

    result = {
        'cross_distance_stats': cross_distance_stats,
        'non_cross_distance_stats': non_cross_distance_stats
    }

    if use_cache:
        cache_manager.save_cache(step_name, result)

    return result


# ================================
# 时间统计函数
# ================================

def calculate_encounter_hourly_statistics(encounters):
    """计算按小时的会遇数量统计"""
    import time

    hourly_encounters = defaultdict(int)

    for encounter in tqdm(encounters, desc='统计会遇时间分布'):
        pos_time = encounter['pos_time']
        if isinstance(pos_time, (int, float)):
            time_struct = time.localtime(pos_time)
            hour = time_struct.tm_hour
        else:
            hour = pos_time.hour
        hourly_encounters[hour] += 1

    hourly_stats = {}
    for hour in range(24):
        hourly_stats[hour] = {
            'encounter_count': hourly_encounters[hour]
        }

    return hourly_stats


def calculate_ship_hourly_statistics(tras_df):
    """计算按小时的船舶平均统计"""
    from collections import defaultdict
    from tqdm import tqdm
    import time
    from datetime import datetime

    # 按日期+小时分组统计
    daily_hourly_ships = defaultdict(lambda: defaultdict(set))

    for _, row in tqdm(tras_df.iterrows(), desc='统计船舶时间分布', total=len(tras_df)):
        pos_time = row['PosTime']

        if isinstance(pos_time, (int, float)):
            dt = datetime.fromtimestamp(pos_time)
            date_key = dt.strftime('%Y-%m-%d')
            hour = dt.hour
        else:
            date_key = pos_time.strftime('%Y-%m-%d')
            hour = pos_time.hour

        mmsi = row['MMSI']
        daily_hourly_ships[date_key][hour].add(mmsi)

    # 计算每小时平均值，保持原有输出格式
    hourly_stats = {}
    for hour in range(24):
        hour_counts = []
        for date_key in daily_hourly_ships:
            if hour in daily_hourly_ships[date_key]:
                hour_counts.append(len(daily_hourly_ships[date_key][hour]))
            else:
                hour_counts.append(0)

        # 计算平均值
        avg_count = sum(hour_counts) / len(hour_counts) if hour_counts else 0

        hourly_stats[hour] = {
            'ship_count': int(avg_count) # 保留两位小数
        }

    return hourly_stats


# ================================
# 主要统计分析函数
# ================================

def analyze_encounter_statistics(extraction_results, use_cache=True):
    """
    分析会遇统计数据
    
    Args:
        extraction_results: 从encounter_extraction.extract_encounters()得到的结果
        use_cache: 是否使用缓存
    
    Returns:
        dict: 完整的统计分析结果
    """
    # 初始化统计缓存管理器
    cache_manager = StatisticsCacheManager()

    # 从提取结果中获取数据
    encounters = extraction_results['encounters']
    cross_related_encounters = extraction_results['cross_related_encounters']
    non_cross_encounters = extraction_results['non_cross_encounters']
    tras_df = extraction_results['tras_df']
    cross_trajectory_map = extraction_results['cross_trajectory_map']
    cross_mmsi_count = extraction_results['cross_mmsi_count']

    print(f"\n=== 开始统计分析 ===")
    print(f"分析 {len(encounters)} 个会遇过程")

    # 计算距离统计
    distance_result = calculate_distance_statistics(cross_related_encounters, non_cross_encounters, tras_df,
                                                  cache_manager, use_cache)
    cross_distance_stats = distance_result['cross_distance_stats']
    non_cross_distance_stats = distance_result['non_cross_distance_stats']

    # 计算时间段统计
    print("正在计算时间段统计...")
    cross_hourly_stats = calculate_encounter_hourly_statistics(cross_related_encounters)
    non_cross_hourly_stats = calculate_encounter_hourly_statistics(non_cross_encounters)
    overall_ship_stats = calculate_ship_hourly_statistics(tras_df)

    # 统计会遇类型分布
    encounter_types = {}
    for encounter in encounters:
        encounter_type = encounter.get('encounter_type', 'unknown')
        encounter_types[encounter_type] = encounter_types.get(encounter_type, 0) + 1

    # 整合结果
    analysis_results = {
        'cross_related_encounters': {
            **cross_distance_stats,
            'hourly_stats': cross_hourly_stats
        },
        'non_cross_encounters': {
            **non_cross_distance_stats,
            'hourly_stats': non_cross_hourly_stats
        },
        'overall_hourly_stats': overall_ship_stats,
        'encounter_types': encounter_types,
        'summary': {
            'total_encounters': len(encounters),
            'cross_related_count': len(cross_related_encounters),
            'non_cross_count': len(non_cross_encounters),
            'cross_trajectory_count': len([k for k in cross_trajectory_map.keys()]),
            'cross_ship_count': len(cross_mmsi_count)
        }
    }

    return analysis_results


def save_analysis_results(analysis_results, output_dir="result0"):
    """保存分析结果到文件"""
    print("正在保存结果...")
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整结果到pickle文件
    output_file = os.path.join(output_dir, 'number_statistics_results.pkl')
    with open(output_file, 'wb') as f:
        pickle.dump(analysis_results, f)
    
    print(f"结果已保存到: {output_file}")
    
    # 保存主要统计数据到CSV文件
    save_statistics_csv(analysis_results, output_dir)


def save_statistics_csv(analysis_results, output_dir):
    """保存统计数据到CSV文件"""
    
    # 1. 保存距离统计
    cross_distances = analysis_results['cross_related_encounters']
    non_cross_distances = analysis_results['non_cross_encounters']
    
    if cross_distances.get('min_distances') or non_cross_distances.get('min_distances'):
        distance_data = []
        
        # 交叉会遇距离数据
        if cross_distances.get('min_distances'):
            for i in range(len(cross_distances['min_distances'])):
                distance_data.append({
                    'encounter_type': 'crossing',
                    'encounter_id': i,
                    'ship_pair': str(cross_distances['encounter_pairs'][i]),
                    'start_distance': cross_distances['start_distances'][i],
                    'min_distance': cross_distances['min_distances'][i],
                    'end_distance': cross_distances['end_distances'][i],
                    'min_distance_time': cross_distances['min_distance_times'][i]
                })
        
        # 追越会遇距离数据
        if non_cross_distances.get('min_distances'):
            for i in range(len(non_cross_distances['min_distances'])):
                distance_data.append({
                    'encounter_type': 'overtaking',
                    'encounter_id': i,
                    'ship_pair': str(non_cross_distances['encounter_pairs'][i]),
                    'start_distance': non_cross_distances['start_distances'][i],
                    'min_distance': non_cross_distances['min_distances'][i],
                    'end_distance': non_cross_distances['end_distances'][i],
                    'min_distance_time': non_cross_distances['min_distance_times'][i]
                })
        
        if distance_data:
            df_distances = pd.DataFrame(distance_data)
            distance_csv = os.path.join(output_dir, 'number_statistics_distances.csv')
            df_distances.to_csv(distance_csv, index=False)
            print(f"距离统计数据已保存到CSV: {distance_csv}")
    
    # 2. 保存时间分布统计
    hourly_data = []
    cross_hourly = analysis_results['cross_related_encounters'].get('hourly_stats', {})
    non_cross_hourly = analysis_results['non_cross_encounters'].get('hourly_stats', {})
    overall_hourly = analysis_results['overall_hourly_stats']
    
    for hour in range(24):
        hourly_data.append({
            'hour': hour,
            'crossing_encounters': cross_hourly.get(hour, {}).get('encounter_count', 0),
            'overtaking_encounters': non_cross_hourly.get(hour, {}).get('encounter_count', 0),
            'total_ships': overall_hourly.get(hour, {}).get('ship_count', 0)
        })
    
    df_hourly = pd.DataFrame(hourly_data)
    hourly_csv = os.path.join(output_dir, 'number_statistics_hourly.csv')
    df_hourly.to_csv(hourly_csv, index=False)
    print(f"时间分布统计已保存到CSV: {hourly_csv}")
    
    # 3. 保存总体摘要
    summary_data = [{
        'metric': 'total_encounters',
        'value': analysis_results['summary']['total_encounters']
    }, {
        'metric': 'crossing_encounters',
        'value': analysis_results['summary']['cross_related_count']
    }, {
        'metric': 'overtaking_encounters', 
        'value': analysis_results['summary']['non_cross_count']
    }]
    
    if cross_distances.get('min_distances'):
        summary_data.extend([{
            'metric': 'crossing_avg_min_distance',
            'value': np.mean(cross_distances['min_distances'])
        }, {
            'metric': 'crossing_min_distance',
            'value': min(cross_distances['min_distances'])
        }, {
            'metric': 'crossing_max_distance',
            'value': max(cross_distances['min_distances'])
        }])
    
    if non_cross_distances.get('min_distances'):
        summary_data.extend([{
            'metric': 'overtaking_avg_min_distance',
            'value': np.mean(non_cross_distances['min_distances'])
        }, {
            'metric': 'overtaking_min_distance',
            'value': min(non_cross_distances['min_distances'])
        }, {
            'metric': 'overtaking_max_distance',
            'value': max(non_cross_distances['min_distances'])
        }])
    
    df_summary = pd.DataFrame(summary_data)
    summary_csv = os.path.join(output_dir, 'number_statistics_summary.csv')
    df_summary.to_csv(summary_csv, index=False)
    print(f"统计摘要已保存到CSV: {summary_csv}")


def print_analysis_summary(analysis_results):
    """打印分析结果摘要"""
    encounters = analysis_results['summary']['total_encounters']
    cross_related_count = analysis_results['summary']['cross_related_count']
    non_cross_count = analysis_results['summary']['non_cross_count']
    encounter_types = analysis_results['encounter_types']
    
    cross_distance_stats = analysis_results['cross_related_encounters']
    non_cross_distance_stats = analysis_results['non_cross_encounters']

    # 输出统计摘要
    print(f"\n=== 会遇分析结果 ===")
    print(f"总会遇数量: {encounters}")

    if encounters > 0:  # 添加除零检查
        print(f"含穿越船会遇: {cross_related_count} ({cross_related_count / encounters * 100:.2f}%)")
        print(f"不含穿越船会遇: {non_cross_count} ({non_cross_count / encounters * 100:.2f}%)")

        print(f"\n会遇类型分布:")
        for encounter_type, count in encounter_types.items():
            type_name = {'crossing': '交叉', 'overtaking': '追越', 'unknown': '未知'}.get(encounter_type, encounter_type)
            print(f"  {type_name}: {count} ({count / encounters * 100:.2f}%)")
    else:
        print("警告：没有检测到有效的会遇过程！")
        print("可能原因：")
        print("1. 会遇合并算法过于严格")
        print("2. TCPA/DCPA阈值设置不当")
        print("3. 数据采样频率不足")

    print(f"\n含穿越船会遇距离统计:")
    if cross_distance_stats.get('min_distances'):
        min_distances = cross_distance_stats['min_distances']
        start_distances = cross_distance_stats['start_distances']
        end_distances = cross_distance_stats['end_distances']
        
        print(f"  最近距离: 平均{np.mean(min_distances):.2f}m, "
              f"最小{min(min_distances):.2f}m, "
              f"最大{max(min_distances):.2f}m")
        print(f"  开始距离: 平均{np.mean(start_distances):.2f}m")
        print(f"  结束距离: 平均{np.mean(end_distances):.2f}m")
    else:
        print("  无距离数据")

    print(f"\n不含穿越船会遇距离统计:")
    if non_cross_distance_stats.get('min_distances'):
        min_distances = non_cross_distance_stats['min_distances']
        start_distances = non_cross_distance_stats['start_distances']
        end_distances = non_cross_distance_stats['end_distances']
        
        print(f"  最近距离: 平均{np.mean(min_distances):.2f}m, "
              f"最小{min(min_distances):.2f}m, "
              f"最大{max(min_distances):.2f}m")
        print(f"  开始距离: 平均{np.mean(start_distances):.2f}m")
        print(f"  结束距离: 平均{np.mean(end_distances):.2f}m")
    else:
        print("  无距离数据")


# ================================
# 主函数 - 统计分析流程
# ================================

def main_statistics(extraction_results, use_cache=True):
    """
    主要统计分析函数，基于提取结果进行统计分析
    
    Args:
        extraction_results: 从encounter_extraction模块得到的提取结果
        use_cache: 是否使用缓存
    
    Returns:
        dict: 完整的统计分析结果
    """
    print("=== 开始统计分析 ===")
    
    # 执行统计分析
    analysis_results = analyze_encounter_statistics(extraction_results, use_cache=use_cache)
    
    # 保存结果
    save_analysis_results(analysis_results)
    
    # 打印摘要
    print_analysis_summary(analysis_results)
    
    print("=== 统计分析完成 ===")
    return analysis_results


if __name__ == '__main__':
    # 加载encounter_extraction模块保存的结果
    try:
        # 首先尝试加载最新的提取结果
        extraction_file = "result0/cache/cache_step3b_merged_encounters.pkl"
        if os.path.exists(extraction_file):
            print(f"正在加载会遇提取结果: {extraction_file}")
            with open(extraction_file, 'rb') as f:
                merged_encounters = pickle.load(f)
            
            # 加载轨迹数据
            print("正在加载轨迹数据...")
            tras_df = pd.read_parquet('data/tras_2024_3_inter.parquet')
            
            # 重构extraction_results格式
            cross_related_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'crossing']
            non_cross_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'overtaking']
            
            extraction_results = {
                'encounters': merged_encounters,
                'cross_related_encounters': cross_related_encounters,
                'non_cross_encounters': non_cross_encounters,
                'tras_df': tras_df,
                'cross_trajectory_map': {},
                'cross_mmsi_count': set(),
                'summary': {
                    'total_encounters': len(merged_encounters),
                    'cross_related_count': len(cross_related_encounters),
                    'non_cross_count': len(non_cross_encounters),
                    'cross_trajectory_count': 0,
                    'cross_ship_count': 0
                }
            }
            
            print(f"加载完成: {len(merged_encounters)} 个会遇过程")
            print(f"  - 交叉会遇: {len(cross_related_encounters)}")
            print(f"  - 追越会遇: {len(non_cross_encounters)}")
            
        else:
            print(f"错误: 未找到会遇提取结果文件: {extraction_file}")
            print("请先运行 01.encounter_extraction.py 进行会遇提取")
            exit(1)
            
    except Exception as e:
        print(f"加载会遇提取结果时出错: {e}")
        print("请确保已运行 01.encounter_extraction.py 并成功生成结果文件")
        exit(1)
    
    # 执行统计分析
    print("\n" + "="*50)
    analysis_results = main_statistics(extraction_results, use_cache=True)
    print(f"\n数值统计分析完成！结果已保存到 result0/ 目录")
    print(f"\n主要输出文件:")
    print(f"  📊 完整结果: result0/number_statistics_results.pkl")
    print(f"  📋 CSV文件:")
    print(f"     - result0/number_statistics_distances.csv    (距离统计)")
    print(f"     - result0/number_statistics_hourly.csv       (时间分布)")
    print(f"     - result0/number_statistics_summary.csv      (统计摘要)")
    print(f"  🗃️  缓存文件: result0/cache/cache_number_statistics_distance_stats.pkl")