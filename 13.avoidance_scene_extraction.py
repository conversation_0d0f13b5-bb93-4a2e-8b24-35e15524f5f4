"""
船舶避让场景提取系统
基于10.scene_extraction.py的会遇场景识别结果，进一步提取避让场景

流程：
1. 读取Ship_dc2.pkl（一般会遇场景）
2. 识别穿越船，根据是否存在穿越船划分会遇场景
3. 对追越场景进行子航道筛选
4. 对所有符合条件的场景进行机动行为识别
5. 输出有机动行为的避让场景

数据结构与Ship_dc2.pkl保持一致：
每个场景为numpy数组，格式：[时间, 会遇船数, 本船MMSI, 目标船MMSI]
"""

import pickle
import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict

from methods.Cross_scene_extraction import CrossSceneExtraction
from methods.Ship_maneuvering_behavior_recognition import CorrectedZeroCrossingDetector
from methods.trans import Trans


class AvoidanceSceneExtractor:
    """避让场景提取器"""

    def __init__(self, data_time='2024_3'):
        """
        初始化避让场景提取器
        
        :param data_time: 数据时间标识
        """
        self.data_time = data_time

        # 加载基础数据
        print("正在加载基础数据...")
        self._load_basic_data()

        # 初始化组件
        self.cross_extractor = CrossSceneExtraction(self.trajectory_list, self.tras_df, self.geo_information)
        self.maneuvering_detector = CorrectedZeroCrossingDetector(magnitude_percentile=75)

        # 缓存变量
        self.crossing_ship_indices = None
        self.crossing_ship_mmsi_set = None
        self.maneuvering_index = {}

    def _load_basic_data(self):
        """加载基础数据"""
        # 加载轨迹数据
        # with open(f'data/{self.data_time}_array.pkl', 'rb') as f:
        #     self.ships_array = pickle.load(f)

        # 转换为trajectory_list格式（为了兼容CrossSceneExtraction）
        # self.trajectory_list = []
        # for ship_array in self.ships_array:
        #     df = pd.DataFrame(ship_array, columns=['PosTime', 'MMSI', 'X', 'Y', 'Cog', 'Sog'])
        #     # 添加Lon, Lat列（假设X, Y已经是经纬度坐标）
        #     df['Lon'] = df['X']
        #     df['Lat'] = df['Y']
        #     self.trajectory_list.append(df)
        with open(f'data/{self.data_time}_inter.pkl', 'rb') as f:
            self.trajectory_list = pickle.load(f)
        # 加载tras_df数据
        self.tras_df = pd.read_parquet(f'data/tras_{self.data_time}_inter.parquet')

        # 加载地理信息
        with open('data/geo_info.pkl', 'rb') as f:
            self.geo_information = pickle.load(f)

        # 转换地理信息中的经纬度点列表为Shapely几何对象
        self._convert_geo_objects()

        # 显示地理信息的关键字段
        print(f"地理信息字段: {list(self.geo_information.keys())}")

        # 检查穿越船识别所需的字段
        required_fields = ['channel_side1', 'channel_side2', 'anchorage', 'channel_boundary']
        missing_fields = [field for field in required_fields if field not in self.geo_information]
        if missing_fields:
            print(f"⚠️  缺少字段: {missing_fields}")
        else:
            print("✅ 穿越船识别所需字段完整")

    def _convert_geo_objects(self):
        """将地理信息中的经纬度点列表转换为Shapely几何对象"""
        from shapely.geometry import Polygon

        print("正在转换地理对象...")

        # 需要转换的字段列表（排除CrossSceneExtraction使用的字段）
        # channel_side1和channel_side2被CrossSceneExtraction使用，保持原始点列表格式
        geo_fields = ['channel_boundary', 'anchorage',
                      'subregion_1', 'subregion_2', 'subregion_3', 'subregion_4']

        converted_count = 0

        for field in geo_fields:
            if field in self.geo_information:
                points = self.geo_information[field]

                # 检查是否是点列表格式
                if isinstance(points, list) and len(points) > 0:
                    try:
                        # 检查第一个元素是否是坐标点
                        if isinstance(points[0], (tuple, list)) and len(points[0]) == 2:
                            # 转换为Polygon对象
                            if len(points) >= 3:  # 多边形至少需要3个点
                                polygon = Polygon(points)
                                self.geo_information[field] = polygon
                                converted_count += 1
                                print(f"✅ {field}: {len(points)}个点 -> Polygon对象")
                            else:
                                print(f"⚠️  {field}: 点数不足，无法构成多边形")
                        else:
                            print(f"⚠️  {field}: 数据格式不正确")
                    except Exception as e:
                        print(f"❌ {field}: 转换失败 - {e}")
                else:
                    print(f"⚠️  {field}: 不是有效的点列表")

        # 保持channel_side1和channel_side2为原始点列表格式
        for side_field in ['channel_side1', 'channel_side2']:
            if side_field in self.geo_information:
                print(f"✅ {side_field}: 保持原始点列表格式 (CrossSceneExtraction需要)")

        print(f"✅ 地理对象转换完成，共转换 {converted_count} 个对象")

    def step1_load_encounter_scenes(self):
        """Step 1: 加载会遇场景"""
        print("\n=== Step 1: 加载会遇场景 ===")

        scene_file = f'result/{self.data_time}/Ship_dc2_simplified.pkl'
        if not Path(scene_file).exists():
            scene_file = f'result/{self.data_time}/Ship_dc2.pkl'

        if not Path(scene_file).exists():
            raise FileNotFoundError(f"未找到会遇场景文件: {scene_file}")

        with open(scene_file, 'rb') as f:
            self.encounter_scenes = pickle.load(f)

        print(f"✅ 已加载 {len(self.encounter_scenes)} 个会遇场景")
        return self.encounter_scenes

    def step2_identify_crossing_ships(self):
        """Step 2: 识别穿越船舶（包含时间段信息）"""
        print("\n=== Step 2: 识别穿越船舶 ===")

        # 使用CrossSceneExtraction识别穿越船
        self.crossing_ship_indices = self.cross_extractor.cross_owns_indentify()

        # 获取穿越船的时间段信息：[(mmsi, start_time, end_time), ...]
        self.crossing_ship_time_segments = []
        self.crossing_ship_mmsi_set = set()

        for idx in self.crossing_ship_indices:
            if idx < len(self.trajectory_list):
                trajectory = self.trajectory_list[idx]
                mmsi = trajectory['MMSI'].iloc[0]
                start_time = trajectory['PosTime'].iloc[0]
                end_time = trajectory['PosTime'].iloc[-1]

                self.crossing_ship_time_segments.append((mmsi, start_time, end_time))
                self.crossing_ship_mmsi_set.add(mmsi)

        print(f"✅ 识别出 {len(self.crossing_ship_mmsi_set)} 艘穿越船舶")
        print(f"   穿越轨迹段数: {len(self.crossing_ship_indices)}")
        print(f"   穿越时间段数: {len(self.crossing_ship_time_segments)}")

    def step3_classify_encounter_scenes(self):
        """Step 3: 分类会遇场景（基于时间交集和航道位置）- 优化版本"""
        print("\n=== Step 3: 分类会遇场景 ===")

        self.crossing_scenes = []
        self.overtaking_scenes = []
        self.unclassified_scenes = []

        # 预构建优化索引
        self._build_optimization_indices()

        debug_stats = {
            'has_crossing': 0,
            'both_in_channel': 0,
            'position_not_found': 0,
            'total_checked': 0
        }

        for scene in tqdm(self.encounter_scenes, desc="场景分类"):
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])

            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            scene_times = np.unique(scene[:, 0])
            scene_start_time = int(scene_times[0])
            scene_end_time = int(scene_times[-1])

            debug_stats['total_checked'] += 1

            # 使用优化的穿越船检查
            is_crossing = self._has_crossing_ship_optimized(
                ship1_mmsi, ship2_mmsi, scene_start_time, scene_end_time
            )

            if is_crossing:
                debug_stats['has_crossing'] += 1
                self.crossing_scenes.append(scene)
            else:
                # 使用缓存的位置查询
                ship1_pos = self._get_ship_position_cached(ship1_mmsi, scene_start_time)
                ship2_pos = self._get_ship_position_cached(ship2_mmsi, scene_start_time)

                if ship1_pos is None or ship2_pos is None:
                    debug_stats['position_not_found'] += 1
                    self.unclassified_scenes.append(scene)
                elif self._both_ships_in_main_channel_cached(ship1_mmsi, ship2_mmsi, scene_start_time):
                    debug_stats['both_in_channel'] += 1
                    self.overtaking_scenes.append(scene)
                else:
                    self.unclassified_scenes.append(scene)

        # 显示统计信息...
        print(f"\n   调试统计:")
        print(f"   ├─ 总检查场景: {debug_stats['total_checked']}")
        print(f"   ├─ 有穿越船参与: {debug_stats['has_crossing']}")
        print(f"   ├─ 都在主航道: {debug_stats['both_in_channel']}")
        print(f"   └─ 位置信息缺失: {debug_stats['position_not_found']}")

        print(f"✅ 场景分类完成:")
        print(f"   交叉场景: {len(self.crossing_scenes)} 个")
        print(f"   追越场景: {len(self.overtaking_scenes)} 个")
        print(f"   未分类场景: {len(self.unclassified_scenes)} 个")

    def step4_filter_overtaking_by_subregion(self):
        """Step 4: 按子航道筛选追越场景"""
        print("\n=== Step 4: 按子航道筛选追越场景 ===")

        # 检查是否有子区域定义
        subregions = {}
        for i in range(1, 5):
            subregion_key = f'subregion_{i}'
            if subregion_key in self.geo_information:
                subregions[subregion_key] = self.geo_information[subregion_key]

        if not subregions:
            print("⚠️  未找到子区域定义，保留所有追越场景")
            self.filtered_overtaking_scenes = self.overtaking_scenes
            return

        print(f"✅ 找到 {len(subregions)} 个子区域: {list(subregions.keys())}")

        self.filtered_overtaking_scenes = []

        for scene in tqdm(self.overtaking_scenes, desc="子航道筛选"):
            # 获取场景中第一个时间点的船舶位置
            scene_times = np.unique(scene[:, 0])
            time_point = int(scene_times[0])
            
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])

            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            # 获取两船的位置信息
            ship1_pos = self._get_ship_position(ship1_mmsi, time_point)
            ship2_pos = self._get_ship_position(ship2_mmsi, time_point)

            if ship1_pos is None or ship2_pos is None:
                continue

            # 判断两船是否在同一子区域
            ship1_subregion = self._get_ship_subregion(ship1_pos, subregions)
            ship2_subregion = self._get_ship_subregion(ship2_pos, subregions)

            if ship1_subregion is not None and ship1_subregion == ship2_subregion:
                self.filtered_overtaking_scenes.append(scene)

        print(f"✅ 子航道筛选完成:")
        print(f"   筛选前追越场景: {len(self.overtaking_scenes)} 个")
        print(f"   筛选后追越场景: {len(self.filtered_overtaking_scenes)} 个")

    def step5_identify_maneuvering_behaviors(self):
        """Step 5: 识别机动行为"""
        print("\n=== Step 5: 识别机动行为 ===")

        # 获取所有需要检查机动行为的船舶
        all_idx_set = set()
        all_scenes = self.crossing_scenes + self.filtered_overtaking_scenes

        for scene in all_scenes:
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])
            
            all_idx_set.add(ship1_idx)
            all_idx_set.add(ship2_idx)

        print(f"需要检查机动行为的船舶数量: {len(all_idx_set)}")

        # 为每艘船识别机动行为
        for idx in tqdm(all_idx_set, desc="机动行为识别"):
            mmsi= self.mmsi_cache[idx]
            try:
                # 获取该船舶的轨迹数据
                ship_trajectory = self._get_ship_trajectory(idx)
                if ship_trajectory is None or len(ship_trajectory) < 10:
                    continue

                # 进行机动行为检测
                result = self.maneuvering_detector.detect_maneuvers(ship_trajectory, debug=False)
                maneuvering_output = result['maneuvering_output']
                maneuvering_df = maneuvering_output['maneuvering']

                # 构建机动索引：{时间: 机动类型集合}
                self.maneuvering_index[mmsi] = {}
                for _, row in maneuvering_df.iterrows():
                    pos_time = int(row['PosTime'])
                    maneuvering_type = row['maneuvering_type']

                    if pos_time not in self.maneuvering_index[mmsi]:
                        self.maneuvering_index[mmsi][pos_time] = set()

                    if maneuvering_type != 0:
                        self.maneuvering_index[mmsi][pos_time].add(maneuvering_type)

            except Exception as e:
                print(f"⚠️  船舶 {mmsi} 机动行为识别失败: {e}")
                continue

        print(f"✅ 机动行为识别完成，处理了 {len(self.maneuvering_index)} 艘船舶")

    def step6_filter_scenes_with_maneuvering(self):
        """Step 6: 提取机动时刻的船舶状态信息"""
        print("\n=== Step 6: 提取机动时刻的船舶状态信息 ===")

        self.crossing_avoidance_scenes = []
        self.overtaking_avoidance_scenes = []

        # 处理交叉避让场景
        for scene in tqdm(self.crossing_scenes, desc="交叉场景处理"):
            moments = self._extract_maneuvering_moments(scene, scene_type="crossing")
            self.crossing_avoidance_scenes.extend(moments)

        # 处理追越避让场景
        for scene in tqdm(self.filtered_overtaking_scenes, desc="追越场景处理"):
            moments = self._extract_maneuvering_moments(scene, scene_type="overtaking")
            self.overtaking_avoidance_scenes.extend(moments)

        print(f"✅ 机动时刻提取完成:")
        print(f"   交叉避让时刻: {len(self.crossing_avoidance_scenes)} 个")
        print(f"   追越避让时刻: {len(self.overtaking_avoidance_scenes)} 个")
        print(f"   总机动时刻: {len(self.crossing_avoidance_scenes) + len(self.overtaking_avoidance_scenes)} 个")

    def step7_save_results(self):
        """Step 7: 保存结果"""
        print("\n=== Step 7: 保存结果 ===")

        # 创建结果目录
        result_dir = Path(f"result/{self.data_time}")
        result_dir.mkdir(exist_ok=True)

        # 分别保存交叉避让和追越避让机动时刻
        crossing_file = result_dir / "crossing_avoidance_scenes.pkl"
        with open(crossing_file, 'wb') as f:
            pickle.dump(self.crossing_avoidance_scenes, f)

        overtaking_file = result_dir / "overtaking_avoidance_scenes.pkl"
        with open(overtaking_file, 'wb') as f:
            pickle.dump(self.overtaking_avoidance_scenes, f)

        print(f"✅ 结果保存完成:")
        print(f"   交叉避让文件: {crossing_file}")
        print(f"   追越避让文件: {overtaking_file}")
        print(f"   交叉避让时刻: {len(self.crossing_avoidance_scenes)} 个")
        print(f"   追越避让时刻: {len(self.overtaking_avoidance_scenes)} 个")

        return self.crossing_avoidance_scenes, self.overtaking_avoidance_scenes

    def _get_ship_position(self, mmsi, time_point):
        """获取指定时间点船舶的位置"""
        ship_data = self.tras_df[(self.tras_df['MMSI'] == mmsi) &
                                 (self.tras_df['PosTime'] == time_point)]
        if ship_data.empty:
            return None

        row = ship_data.iloc[0]
        return (float(row['Lon']), float(row['Lat']))

    def _get_ship_subregion(self, position, subregions):
        """判断船舶位置属于哪个子区域"""
        from shapely.geometry import Point

        lon, lat = position
        point = Point(lon, lat)

        for subregion_name, subregion_polygon in subregions.items():
            if point.within(subregion_polygon):
                return subregion_name

        return None

    def _get_ship_trajectory(self, idx):
        """获取船舶的完整轨迹数据"""
        ship_data = self.trajectory_list[idx]
        if ship_data.empty:
            return None

        # 按时间排序
        ship_data = ship_data.sort_values('PosTime')
        return ship_data

    def _has_crossing_ship_optimized(self, ship1_mmsi, ship2_mmsi, scene_start, scene_end):
        """优化的穿越船检查"""
        for mmsi in [ship1_mmsi, ship2_mmsi]:
            if mmsi in self.crossing_time_index:
                for crossing_start, crossing_end in self.crossing_time_index[mmsi]:
                    if scene_start < crossing_end and scene_end > crossing_start:
                        return True
        return False

    def _build_optimization_indices(self):
        """构建优化索引"""
        print("正在构建优化索引...")
        
        # 1. MMSI缓存：轨迹索引 -> MMSI
        self.mmsi_cache = {}
        for idx, trajectory in enumerate(self.trajectory_list):
            self.mmsi_cache[idx] = trajectory['MMSI'].iloc[0]
        
        # 2. 穿越船时间段索引：MMSI -> [(start, end), ...]
        self.crossing_time_index = defaultdict(list)
        for mmsi, start_time, end_time in self.crossing_ship_time_segments:
            self.crossing_time_index[mmsi].append((start_time, end_time))
        
        # 3. 位置查询缓存
        self.position_cache = {}

    def _get_ship_position_cached(self, mmsi, time_point):
        """带缓存的位置查询"""
        cache_key = (mmsi, time_point)
        if cache_key in self.position_cache:
            return self.position_cache[cache_key]
        
        result = self._get_ship_position(mmsi, time_point)
        self.position_cache[cache_key] = result
        return result

    def _both_ships_in_main_channel_cached(self, ship1_mmsi, ship2_mmsi, time_point):
        """带缓存的主航道检查"""
        ship1_pos = self._get_ship_position_cached(ship1_mmsi, time_point)
        ship2_pos = self._get_ship_position_cached(ship2_mmsi, time_point)
        
        if ship1_pos is None or ship2_pos is None:
            return False
        
        return (self._point_in_polygon(ship1_pos, self.geo_information['channel_boundary']) and
                self._point_in_polygon(ship2_pos, self.geo_information['channel_boundary']))

    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内"""
        from shapely.geometry import Point
        return Point(point[0], point[1]).within(polygon)

    def _extract_maneuvering_moments(self, scene, scene_type="crossing"):
        """提取场景中的机动时刻信息"""
        moments = []
        ship1_idx = int(scene[0, 2])
        ship2_idx = int(scene[0, 3])
        ship1_mmsi = self.mmsi_cache[ship1_idx]
        ship2_mmsi = self.mmsi_cache[ship2_idx]

        for row in scene:
            time_point = int(row[0])

            # 检查ship1是否有机动
            if (ship1_mmsi in self.maneuvering_index and
                    time_point in self.maneuvering_index[ship1_mmsi] and
                    len(self.maneuvering_index[ship1_mmsi][time_point]) > 0):
                moment = self._create_moment_data(ship1_mmsi, ship2_mmsi, time_point, scene_type)
                if moment:
                    moments.append(moment)

            # 检查ship2是否有机动
            if (ship2_mmsi in self.maneuvering_index and
                    time_point in self.maneuvering_index[ship2_mmsi] and
                    len(self.maneuvering_index[ship2_mmsi][time_point]) > 0):
                moment = self._create_moment_data(ship2_mmsi, ship1_mmsi, time_point, scene_type)
                if moment:
                    moments.append(moment)

        return moments

    def _create_moment_data(self, maneuvering_mmsi, other_mmsi, time_point, scene_type="crossing"):
        """创建机动时刻的数据"""
        # 获取机动船信息
        maneuvering_ship = self._get_ship_info(maneuvering_mmsi, time_point)
        # 获取非机动船信息
        other_ship = self._get_ship_info(other_mmsi, time_point)
        
        if maneuvering_ship is None or other_ship is None:
            return None
        
        # 对于追越场景，需要检查位置关系
        if scene_type == "overtaking":
            if self._is_maneuvering_ship_ahead_of_other(maneuvering_ship, other_ship):
                # 机动船位于非机动船前方，不保留这种情况
                return None
            
        return [maneuvering_ship, other_ship]

    def _is_maneuvering_ship_ahead_of_other(self, maneuvering_ship, other_ship):
        """
        判断机动船是否位于非机动船的前方（航向方向上）
        
        Args:
            maneuvering_ship: 机动船信息字典
            other_ship: 非机动船信息字典
            
        Returns:
            bool: True表示机动船位于非机动船前方，False表示不是
        """
        # 获取船舶位置和航向
        man_lon, man_lat = maneuvering_ship['lon'], maneuvering_ship['lat']
        other_lon, other_lat = other_ship['lon'], other_ship['lat']
        other_cog = other_ship['cog']  # 非机动船的航向
        
        # 计算从非机动船到机动船的方向向量
        dx = man_lon - other_lon
        dy = man_lat - other_lat
        
        # 将航向角度转换为弧度（注意：船舶航向是正北为0度，顺时针增加）
        # 需要转换为数学坐标系（正东为0度，逆时针增加）
        other_cog_rad = np.radians(90 - other_cog)
        
        # 计算航向方向向量
        cog_dx = np.cos(other_cog_rad)
        cog_dy = np.sin(other_cog_rad)
        
        # 计算两个向量的点积
        dot_product = dx * cog_dx + dy * cog_dy
        
        # 如果点积为正，说明机动船在非机动船的前方
        return dot_product > 0

    def _get_ship_info(self, mmsi, time_point):
        """获取指定时间点船舶的完整信息"""
        ship_data = self.tras_df[(self.tras_df['MMSI'] == mmsi) &
                                 (self.tras_df['PosTime'] == time_point)]
        if ship_data.empty:
            return None

        row = ship_data.iloc[0]
        return {
            'mmsi': int(mmsi),
            'lon': float(row['Lon']),
            'lat': float(row['Lat']),
            'cog': float(row['Cog']),
            'sog': float(row['Sog']),
            'length': float(row['Length']) if pd.notna(row['Length']) else 100.0,
            'width': float(row['Width']) if pd.notna(row['Width']) else 15.0,
            'time_point': time_point
        }

    def run_full_pipeline(self):
        """运行完整的避让场景提取流程"""
        print("🚢 开始船舶避让场景提取流程...")
        print("=" * 60)

        # 执行完整流程
        self.step1_load_encounter_scenes()
        self.step2_identify_crossing_ships()
        self.step3_classify_encounter_scenes()
        self.step4_filter_overtaking_by_subregion()
        self.step5_identify_maneuvering_behaviors()
        self.step6_filter_scenes_with_maneuvering()
        crossing_scenes, overtaking_scenes = self.step7_save_results()

        print("\n" + "=" * 60)
        print("🎉 避让场景提取完成！")
        print("=" * 60)

        # 显示详细统计
        self._print_classification_summary()

        return crossing_scenes, overtaking_scenes

    def _print_classification_summary(self):
        """打印分类结果摘要"""
        print("\n📊 分类结果摘要:")
        print(f"   原始会遇场景: {len(self.encounter_scenes)} 个")
        print(
            f"   ├─ 交叉场景: {len(self.crossing_scenes)} 个 ({len(self.crossing_scenes) / len(self.encounter_scenes) * 100:.1f}%)")
        print(
            f"   ├─ 追越场景: {len(self.overtaking_scenes)} 个 ({len(self.overtaking_scenes) / len(self.encounter_scenes) * 100:.1f}%)")
        print(
            f"   └─ 未分类场景: {len(self.unclassified_scenes)} 个 ({len(self.unclassified_scenes) / len(self.encounter_scenes) * 100:.1f}%)")

        print(f"\n   子航道筛选:")
        print(f"   ├─ 筛选前追越场景: {len(self.overtaking_scenes)} 个")
        print(
            f"   └─ 筛选后追越场景: {len(self.filtered_overtaking_scenes)} 个 (保留{len(self.filtered_overtaking_scenes) / max(1, len(self.overtaking_scenes)) * 100:.1f}%)")

        print(f"\n   机动时刻提取:")
        total_candidate_scenes = len(self.crossing_scenes) + len(self.filtered_overtaking_scenes)
        total_avoidance_moments = len(self.crossing_avoidance_scenes) + len(self.overtaking_avoidance_scenes)
        print(f"   ├─ 候选场景: {total_candidate_scenes} 个")
        print(f"   └─ 提取机动时刻: {total_avoidance_moments} 个")

        print(f"\n   穿越船信息:")
        print(f"   ├─ 穿越船数量: {len(self.crossing_ship_mmsi_set)} 艘")
        print(f"   └─ 穿越时间段: {len(self.crossing_ship_time_segments)} 个")


def main():
    """主函数"""
    # months = [1, 2, 3]
    months = [1]
    for month in tqdm(months, leave=True, desc='Month'):
        data_time = f'2024_{month}'

        # 创建避让场景提取器
        extractor = AvoidanceSceneExtractor(data_time)

        # 运行完整流程
        crossing_scenes, overtaking_scenes = extractor.run_full_pipeline()

        print(f"\n📊 最终统计:")
        print(f"   输入会遇场景: {len(extractor.encounter_scenes)} 个")
        print(f"   输出机动时刻: {len(crossing_scenes) + len(overtaking_scenes)} 个")
        print(f"   数据格式: [机动船信息, 非机动船信息]")


if __name__ == '__main__':
    main()
