"""
船舶领域椭圆可视化工具
从step5中提取的椭圆绘制功能，支持自定义椭圆参数
四种情况同时绘制到一张图上

使用方法：
1. 修改下面的椭圆参数配置
2. 运行脚本生成可视化图
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pickle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'

# ==================== 椭圆参数配置 ====================
# 自定义四种情况的椭圆参数 (长轴a, 短轴b)
CUSTOM_ELLIPSE_PARAMS = {
    '交叉_100m以下': {'a': 271, 'b': 192},
    '交叉_100-200m': {'a': 375, 'b': 210},
    '追越_100m以下': {'a': 180, 'b': 85},
    '追越_100-200m': {'a': 290, 'b': 120},
}

# 可视化配置
MAX_CIRCLE_RADIUS = 1000  # 最大圆半径（米）
AXIS_RANGE = 1200         # 坐标轴显示范围（米）

# 颜色配置
COLORS = {
    'ship_points': '#1f77b4',      # 蓝色点
    'max_circle': 'black',         # 黑色实线
    'axis_lines': 'red',           # 红色虚线
    'sector_lines': 'black',       # 黑色虚线
    'ellipse_colors': ['hotpink', 'orange', 'green', 'purple']  # 不同椭圆的颜色
}

# ================================================

class ShipDomainEllipseVisualizer:
    """船舶领域椭圆可视化器"""

    def __init__(self):
        """初始化可视化器"""
        self.density_results = {}
        self.sector_boundaries = {}
        self.ellipse_params = CUSTOM_ELLIPSE_PARAMS

        print("🎨 船舶领域椭圆可视化器初始化完成")

    def load_ship_data(self):
        """加载船舶分布数据和扇区边界数据（可选）"""
        print("\n=== 尝试加载船舶分布数据和扇区边界 ===")

        density_file = Path("result/probability_density/sector_boundaries_results.pkl")
        if density_file.exists():
            try:
                with open(density_file, 'rb') as f:
                    density_data = pickle.load(f)
                self.density_results = density_data['density_results']
                self.sector_boundaries = density_data['sector_boundaries']
                print(f"✅ 加载船舶分布数据: {len(self.density_results)} 个场景")
                print(f"✅ 加载扇区边界数据: {len(self.sector_boundaries)} 个场景")
                return True
            except Exception as e:
                print(f"⚠️ 加载数据失败: {e}")
        else:
            print("⚠️ 未找到数据文件，将只绘制椭圆")

        return False
    
    def plot_single_ellipse(self, ax, a, b, color='hotpink', label='船舶领域', alpha=1.0):
        """绘制单个椭圆
        
        Args:
            ax: matplotlib轴对象
            a: 纵向半轴长度（米）
            b: 横向半轴长度（米）
            color: 椭圆颜色
            label: 图例标签
            alpha: 透明度
        """
        # 生成椭圆点
        theta = np.linspace(0, 2*np.pi, 100)
        ellipse_x = b * np.cos(theta)  # 横向半轴
        ellipse_y = a * np.sin(theta)  # 纵向半轴
        
        ax.plot(ellipse_x, ellipse_y, color=color, linewidth=3, label=label, alpha=alpha)
    
    def plot_background_elements(self, ax, scene_key=None, alpha=1.0, draw_boundaries=True):
        """绘制背景元素（船舶分布点、圆圈、坐标轴、扇区边界等）

        Args:
            ax: matplotlib轴对象
            scene_key: 场景键名，用于获取对应的船舶分布数据
            alpha: 背景元素透明度
            draw_boundaries: 是否绘制扇区边界
        """
        # 绘制船舶分布点（如果有数据）
        if scene_key and scene_key in self.density_results:
            density_data = self.density_results[scene_key]
            x_coords = density_data['x_coords']
            y_coords = density_data['y_coords']

            # 过滤超过1000米的点
            distances = np.sqrt(x_coords**2 + y_coords**2)
            valid_mask = distances <= MAX_CIRCLE_RADIUS
            filtered_x = x_coords[valid_mask]
            filtered_y = y_coords[valid_mask]

            ax.scatter(filtered_x, filtered_y, c=COLORS['ship_points'],
                      s=2, alpha=0.3*alpha, zorder=1)

        # 绘制1000米圆（黑色实线）
        circle = plt.Circle((0, 0), MAX_CIRCLE_RADIUS, fill=False,
                           color=COLORS['max_circle'], linewidth=2, alpha=0.5*alpha)
        ax.add_patch(circle)

        # 绘制坐标轴直径（红色虚线）
        ax.plot([-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS], [0, 0],
                color=COLORS['axis_lines'], linestyle='--', linewidth=2, alpha=0.7*alpha)
        ax.plot([0, 0], [-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS],
                color=COLORS['axis_lines'], linestyle='--', linewidth=2, alpha=0.7*alpha)

        # 绘制扇区分割线（更淡）
        num_sectors = 36
        sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]

        for angle in sector_angles:
            # 跳过与红色虚线重叠的方向
            angle_deg = np.degrees(angle) % 360
            if angle_deg > 180:
                angle_deg -= 360

            is_overlapping = (abs(angle_deg) < 5 or abs(angle_deg - 90) < 5 or
                            abs(angle_deg + 90) < 5 or abs(abs(angle_deg) - 180) < 5)

            if not is_overlapping:
                x_end = MAX_CIRCLE_RADIUS * np.cos(angle)
                y_end = MAX_CIRCLE_RADIUS * np.sin(angle)

                ax.plot([0, x_end], [0, y_end],
                       color=COLORS['sector_lines'], linestyle='--',
                       linewidth=1, alpha=0.3*alpha, zorder=0)

        # 绘制扇区边界（绿色圆弧）
        if draw_boundaries and scene_key:
            self.draw_sector_boundaries(ax, scene_key, alpha=0.7*alpha)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow',
                markeredgewidth=2, zorder=5)

    def draw_sector_boundaries(self, ax, scene_key, alpha=0.7):
        """绘制扇区边界（绿色圆弧）

        Args:
            ax: matplotlib轴对象
            scene_key: 场景键名，用于获取对应的扇区边界数据
            alpha: 透明度
        """
        if scene_key not in self.sector_boundaries:
            return

        boundaries = self.sector_boundaries[scene_key]

        try:
            # 使用36扇区划分方式
            num_sectors = 36
            sector_width = 2 * np.pi / num_sectors  # 每个扇区10度

            # 为每个边界绘制对应扇区的圆弧
            for sector_name, boundary_info in boundaries.items():
                boundary_distance = boundary_info['boundary_distance']
                boundary_angle = boundary_info['boundary_angle']

                # 只显示在1000米范围内的边界
                if boundary_distance <= MAX_CIRCLE_RADIUS:
                    # 从扇区名称中提取扇区编号
                    sector_index = self._extract_sector_index(sector_name)

                    if sector_index is not None:
                        # 计算该扇区的角度范围
                        start_angle = sector_index * sector_width - np.pi
                        end_angle = (sector_index + 1) * sector_width - np.pi
                    else:
                        # 如果无法从名称提取，根据边界角度推断
                        sector_index = self._get_sector_index_from_angle(boundary_angle, num_sectors)
                        start_angle = sector_index * sector_width - np.pi
                        end_angle = (sector_index + 1) * sector_width - np.pi

                    # 处理跨越-π/π边界的扇区
                    if start_angle < -np.pi:
                        start_angle += 2 * np.pi
                    if end_angle > np.pi:
                        end_angle -= 2 * np.pi

                    # 生成该扇区范围内的圆弧
                    if start_angle < end_angle:
                        arc_angles = np.linspace(start_angle, end_angle, 15)  # 10度扇区用15个点足够
                    else:
                        # 跨越边界的情况
                        arc_angles1 = np.linspace(start_angle, np.pi, 8)
                        arc_angles2 = np.linspace(-np.pi, end_angle, 8)
                        arc_angles = np.concatenate([arc_angles1, arc_angles2])

                    arc_x = boundary_distance * np.cos(arc_angles)
                    arc_y = boundary_distance * np.sin(arc_angles)

                    # 绘制绿色圆弧
                    ax.plot(arc_x, arc_y, color='green', linewidth=3, alpha=alpha, zorder=3)

        except Exception as e:
            print(f"绘制扇区边界失败: {e}")

    def _extract_sector_index(self, sector_name):
        """从扇区名称中提取扇区编号"""
        try:
            import re
            # 尝试从扇区名称中提取数字
            match = re.search(r'扇区(\d+)', sector_name)
            if match:
                return int(match.group(1)) - 1  # 转换为0-based索引

            # 尝试其他可能的格式
            match = re.search(r'sector(\d+)', sector_name.lower())
            if match:
                return int(match.group(1)) - 1

            return None
        except:
            return None

    def _get_sector_index_from_angle(self, angle, num_sectors):
        """根据角度获取扇区索引"""
        try:
            # 将角度转换到[0, 2π)范围
            normalized_angle = angle + np.pi
            if normalized_angle < 0:
                normalized_angle += 2 * np.pi
            elif normalized_angle >= 2 * np.pi:
                normalized_angle -= 2 * np.pi

            # 计算扇区索引
            sector_width = 2 * np.pi / num_sectors
            sector_index = int(normalized_angle / sector_width)

            # 确保索引在有效范围内
            return max(0, min(num_sectors - 1, sector_index))

        except Exception as e:
            print(f"扇区索引计算失败: {e}")
            return 0
    
    def create_four_ellipses_comparison(self):
        """创建四种情况的椭圆对比图"""
        print("\n=== 生成四种椭圆对比图 ===")

        # 创建输出目录
        vis_dir = Path("vis/single_visualization/ellipse_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)

        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 12))

        # 绘制背景元素（使用第一个场景的数据作为背景，如果有的话）
        first_scene = list(self.ellipse_params.keys())[0] if self.ellipse_params else None
        self.plot_background_elements(ax, first_scene, alpha=0.5, draw_boundaries=True)

        # 绘制四个椭圆
        for i, (scene_name, params) in enumerate(self.ellipse_params.items()):
            a, b = params['a'], params['b']
            color = COLORS['ellipse_colors'][i % len(COLORS['ellipse_colors'])]

            # 创建标签
            scenario_type, length_interval = scene_name.split('_', 1)
            label = f'{scenario_type}-{length_interval} (a={a}m, b={b}m)'

            # 绘制椭圆
            self.plot_single_ellipse(ax, a, b, color=color, label=label)

        # 设置坐标轴
        ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)

        # 添加标签和图例
        ax.set_xlabel('横向距离 (m)', fontsize=12)
        ax.set_ylabel('纵向距离 (m)', fontsize=12)
        ax.set_title('船舶领域椭圆对比 - 四种情况（含扇区边界）', fontsize=16, fontweight='bold')
        ax.legend(loc='upper right', fontsize=10)

        plt.tight_layout()

        # 保存图片
        save_path = vis_dir / "ship_domain_four_ellipses_comparison_with_boundaries.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 四椭圆对比图（含扇区边界）保存至: {save_path}")
        return save_path
    
    def create_individual_ellipse_plots(self):
        """为每种情况创建单独的椭圆图"""
        print("\n=== 生成单独椭圆图 ===")

        vis_dir = Path("vis/single_visualization/ellipse_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)

        for scene_name, params in self.ellipse_params.items():
            # 创建图形
            fig, ax = plt.subplots(1, 1, figsize=(10, 10))

            # 绘制背景元素（包含扇区边界）
            self.plot_background_elements(ax, scene_name, draw_boundaries=True)

            # 绘制椭圆
            a, b = params['a'], params['b']
            self.plot_single_ellipse(ax, a, b, color='hotpink', label=f'船舶领域 (a={a}m, b={b}m)')

            # 设置坐标轴
            ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
            ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)

            # 添加标签和标题
            ax.set_xlabel('横向距离 (m)', fontsize=12)
            ax.set_ylabel('纵向距离 (m)', fontsize=12)

            scenario_type, length_interval = scene_name.split('_', 1)
            ax.set_title(f'{scenario_type} - {length_interval} 船舶领域椭圆（含扇区边界）',
                        fontsize=14, fontweight='bold')
            ax.legend(loc='upper right', fontsize=10)

            plt.tight_layout()

            # 保存图片
            filename = f"{scenario_type}_{length_interval}_ellipse_with_boundaries.png"
            save_path = vis_dir / filename
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"   {scene_name} 椭圆图（含扇区边界）保存至: {save_path}")
    
    def run_visualization(self):
        """运行完整的可视化流程"""
        print("🎨 开始船舶领域椭圆可视化...")
        print("=" * 60)
        
        # 显示当前椭圆参数配置
        print("📋 当前椭圆参数配置:")
        for scene_name, params in self.ellipse_params.items():
            scenario_type, length_interval = scene_name.split('_', 1)
            print(f"   {scenario_type:6} - {length_interval:10}: a={params['a']:3d}m, b={params['b']:3d}m")
        print("=" * 60)
        
        try:
            # 尝试加载船舶分布数据（可选）
            self.load_ship_data()
            
            # 生成四椭圆对比图
            comparison_path = self.create_four_ellipses_comparison()
            
            # 生成单独椭圆图
            self.create_individual_ellipse_plots()
            
            print("\n" + "=" * 60)
            print("🎉 船舶领域椭圆可视化完成！")
            print("📁 输出目录: vis/single_visualization/ellipse_visualization/")
            print(f"📊 主要对比图: {comparison_path}")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"\n❌ 可视化失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎨 船舶领域椭圆可视化工具")
    print("=" * 50)
    
    # 创建可视化器并运行
    visualizer = ShipDomainEllipseVisualizer()
    success = visualizer.run_visualization()
    
    if success:
        print("\n💡 使用提示:")
        print("   • 修改代码顶部的 CUSTOM_ELLIPSE_PARAMS 来调整椭圆参数")
        print("   • a 参数控制纵向半轴长度（前后方向）")
        print("   • b 参数控制横向半轴长度（左右方向）")
        print("   • 可以修改颜色配置来改变椭圆显示效果")
    else:
        print("\n❌ 可视化失败，请检查错误信息")


if __name__ == '__main__':
    main()
