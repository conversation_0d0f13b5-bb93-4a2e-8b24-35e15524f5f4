#%%
import pickle

with open('geo_info.pkl', 'rb') as f:
    geo_info = pickle.load(f)
#%%
for key, value in geo_info.items():
    print(key)
#%%
import pandas as pd

df0 = pd.read_csv('geo/航道中心线.csv')
df1 = pd.read_csv('geo/航道中心线北.csv')
df2 = pd.read_csv('geo/航道中心线南.csv')
df = pd.read_csv('geo/航道.csv')
channel_side1 = [(i,j) for i,j in zip(df1['Lon'].tolist(), df1['Lat'].tolist())]
channel_side2 = [(i,j) for i,j in zip(df2['Lon'].tolist(), df2['Lat'].tolist())]
channel_centerline = [(i,j) for i,j in zip(df0['Lon'].tolist(), df0['Lat'].tolist())]
channel_boundary = [(i,j) for i,j in zip(df['Lon'].tolist(), df['Lat'].tolist())]

geo_info['channel_side1'] = channel_side1
geo_info['channel_side2'] = channel_side2
geo_info['channel_centerline'] = channel_centerline
geo_info['channel_boundary'] = channel_boundary
#%%
geo_info['subregion_1']=[(121.3312833,31.5532), (121.3147,31.57083333),(121.30135,31.59043333),(121.2795333,31.61066667),(121.2623667,31.62598333),(121.2429,31.64145), (121.2049167,31.66736667),(121.1851333,31.68121667),(121.16435,31.69378333),(121.1444167,31.7051),(121.1273833,31.7211),(121.1115333,31.73631667),(121.1030167,31.74446667),(121.0717167,31.76303333),(121.0527833,31.7723),(121.05,31.77268333),(121.05,31.77446667),(121.05365,31.77396667),(121.0728833,31.7646),(121.1043,31.74588333),(121.1459,31.70641667),(121.166,31.69498333),(121.1854333,31.68323333),(121.2452333,31.64201667),(121.2595167,31.63068333),(121.26425,31.62695),(121.2802667,31.61268333),(121.3029667,31.59163333),(121.3164,31.57188333),(121.3329167,31.55433333)] # 推荐航道上行
geo_info['subregion_2']=[(121.3228,31.5466),(121.3068,31.56356667),(121.2904167,31.58548333),(121.27405,31.60398333),(121.2554,31.62155),(121.1818,31.67555),(121.1359667,31.70106667),(121.1152167,31.71771667),(121.0968,31.73483333),(121.07235,31.74993333),(121.0500167,31.76053333),(121.0500167,31.76251667),(121.0733833,31.75155),(121.09795,31.73641667),(121.1169833,31.71878333),(121.13725,31.7025),(121.1829667,31.67683333),(121.2571167,31.6226),(121.2759833,31.60466667),(121.2925,31.58616667),(121.3085167,31.5646),(121.3245333,31.5476),(121.3228,31.54658333)] # 推荐航道下行
geo_info['subregion_3']=[(121.3277667,31.55048333),(121.3115833,31.56778333),(121.29695,31.58823333),(121.2772,31.60831667),(121.2595,31.62451667),(121.2405333,31.63938333),(121.1834667,31.67936667),(121.1725333,31.68576667),(121.14115,31.70363333),(121.13065,31.71263333),(121.1200833,31.7219),(121.1006333,31.74026667),(121.0725,31.75735),(121.0511167,31.76755),(121.05,31.76776667),(121.0499833,31.7727),(121.0528,31.77231667),(121.0716667,31.76311667),(121.1033667,31.74426667),(121.1445167,31.70503333),(121.16615,31.6927),(121.1845,31.68165),(121.2433167,31.64108333),(121.2623167,31.626),(121.3004833,31.59138333),(121.3013,31.5905),(121.3150167,31.57033333),(121.3312833,31.55311667)] # 主航道上行
geo_info['subregion_4']=[(121.3244833,31.54775),(121.3084333,31.5647),(121.29295,31.5854),(121.2757,31.60515),(121.2567333,31.6229),(121.2367667,31.63801667),(121.1979833,31.6662),(121.1829,31.67683333),(121.1407167,31.70056667),(121.13705,31.70256667),(121.1162,31.71941667),(121.0980333,31.73635),(121.0734667,31.75146667),(121.0499667,31.76251667),(121.0499833,31.76778333),(121.0511167,31.76755),(121.07235,31.7574),(121.1005,31.7404),(121.12125,31.72086667),(121.1407667,31.70383333),(121.1632,31.69118333),(121.1832167,31.67953333),(121.2404,31.63953333),(121.2593667,31.62461667),(121.2773,31.60815),(121.2967167,31.58853333),(121.31155,31.56781667),(121.32785,31.55045)] # 主航道下行
#%%
with open('geo_info.pkl', 'wb') as f:
    pickle.dump(geo_info, f)
#%%
with open('geo_info.pkl', 'rb') as f:
    geo_info = pickle.load(f)
for key, value in geo_info.items():
    print(key)