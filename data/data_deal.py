import pickle

import pandas as pd
from tqdm import tqdm
import numpy as np
from methods.trans import Trans


def f1():
    trajectory_list = pickle.load(open("2024_1_inter.pkl", "rb"))
    tras_df = pd.concat(trajectory_list)
    tras_df.to_parquet('tras_2024_1_inter.parquet')


def f2():
    res = []
    trans = Trans()
    trajectory_list = pickle.load(open("2024_1_inter.pkl", "rb"))
    for tra in tqdm(trajectory_list):
        X, Y = trans.LonLat2Gauss(tra['Lon'].values, tra['Lat'].values)
        tra['X'], tra['Y'] = X, Y
        tra = tra.drop(['Lon', 'Lat'], axis=1)
        res.append(np.array(tra[['PosTime', 'MMSI', 'X', 'Y', 'Cog', 'Sog']]))
    pickle.dump(res, open("2024_1_array.pkl", "wb"))


def f3():
    trajectory_list_all = []
    res_all = []
    for i in tqdm([1,2,3]):
        trajectory_list = pickle.load(open(f"2024_{i}_inter.pkl", "rb"))
        trajectory_list_all.extend(trajectory_list)
    for tra in tqdm(trajectory_list_all):
        res_all.append(np.array(tra[['PosTime', 'MMSI', 'Lon', 'Lat', 'Cog', 'Sog']]))
    tras_df_all = pd.concat(trajectory_list_all)
    tras_df_all.to_parquet('tras_2024_1_3_inter.parquet')
    pickle.dump(res_all, open("2024_1_3_array.pkl", "wb"))


if __name__ == '__main__':
    f2()
