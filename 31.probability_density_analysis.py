"""
概率密度分析系统 - 船舶领域拟合第一步
基于机动时刻数据，划分扇区，计算概率密度，找到边界

核心功能：
1. 加载机动时刻数据
2. 提取相对位置数据
3. 划分扇区（8个扇区）
4. 计算概率密度分布
5. 使用累积密度变化率方法找到各扇区的概率密度边界
6. 可视化扇区划分和边界检测结果

边界检测方法：
- 累积密度变化率分析
  * 从离船舶最近的子区域开始
  * 逐步累积加入更远的子区域
  * 计算累积区域的密度变化率
  * 找到密度变化率最大的边界点

特殊处理：
- 追越避让场景：仅保留Y轴上半部分的边界点（Y >= 0），过滤掉下半部分边界点

输入：13.avoidance_scene_extraction.py：crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl
输出：sector_boundaries_results.pkl

使用方法：
python probability_density_analysis.py
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import math
from scipy import stats
from scipy.ndimage import gaussian_filter
from scipy.interpolate import griddata

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ProbabilityDensityAnalyzer:
    """概率密度分析器"""

    def __init__(self, data_months=['2024_1', '2024_2', '2024_3'], num_sectors=8, debug=False):
        """
        初始化概率密度分析器
        
        :param data_months: 数据月份列表
        :param num_sectors: 扇区数量
        :param debug: 是否开启调试模式
        """
        self.data_months = data_months if isinstance(data_months, list) else [data_months]
        self.num_sectors = num_sectors
        self.debug = debug

        # 船长区间定义 - 重构为3个区间
        self.length_intervals = [
            (0, 100, "100m以下"),
            (100, 200, "100-200m"),
            (200, 500, "200m以上")
        ]

        # 扇区角度范围
        self.sector_angles = self._calculate_sector_angles()

        # 数据存储
        self.crossing_moments = []
        self.overtaking_moments = []
        self.crossing_relative_data = {}
        self.overtaking_relative_data = {}

        # 分析结果
        self.density_results = {}
        self.sector_boundaries = {}

        print(f"🔍 概率密度分析器初始化完成")
        print(f"   数据月份: {self.data_months}")
        print(f"   扇区数量: {num_sectors}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")

    def _calculate_sector_angles(self):
        """计算扇区角度范围"""
        sector_width = 2 * np.pi / self.num_sectors
        angles = []

        for i in range(self.num_sectors):
            start_angle = i * sector_width - np.pi  # 从-π开始
            end_angle = (i + 1) * sector_width - np.pi
            angles.append((start_angle, end_angle, f"扇区{i + 1}"))

        return angles

    def load_maneuvering_moments(self):
        """加载机动时刻数据"""
        print("\n=== 加载机动时刻数据 ===")

        total_crossing = 0
        total_overtaking = 0

        for data_month in self.data_months:
            print(f"\n--- 加载 {data_month} 数据 ---")

            # 加载交叉避让机动时刻
            crossing_file = f'result/{data_month}/crossing_avoidance_scenes.pkl'
            if Path(crossing_file).exists():
                with open(crossing_file, 'rb') as f:
                    moments = pickle.load(f)
                self.crossing_moments.extend(moments)
                total_crossing += len(moments)
                print(f"   交叉避让: {len(moments)} 个时刻")
            else:
                print(f"⚠️  未找到: {crossing_file}")

            # 加载追越避让机动时刻（与交叉避让相同处理）
            overtaking_file = f'result/{data_month}/overtaking_avoidance_scenes.pkl'
            if Path(overtaking_file).exists():
                with open(overtaking_file, 'rb') as f:
                    moments = pickle.load(f)
                self.overtaking_moments.extend(moments)
                total_overtaking += len(moments)
                print(f"   追越避让: {len(moments)} 个时刻")
            else:
                print(f"⚠️  未找到: {overtaking_file}")

        print(f"\n✅ 机动时刻加载完成:")
        print(f"   交叉避让: {total_crossing} 个")
        print(f"   追越避让: {total_overtaking} 个")

    def extract_relative_positions(self):
        """提取相对位置数据"""
        print("\n=== 提取相对位置数据 ===")

        # 初始化数据结构
        for _, _, interval_name in self.length_intervals:
            self.crossing_relative_data[interval_name] = []
            self.overtaking_relative_data[interval_name] = []

        # 处理交叉避让
        print("处理交叉避让机动时刻...")
        crossing_processed = self._process_moments(self.crossing_moments, self.crossing_relative_data)

        # 处理追越避让
        print("处理追越避让机动时刻...")
        overtaking_processed = self._process_moments(self.overtaking_moments, self.overtaking_relative_data)

        print(f"✅ 相对位置提取完成:")
        print(f"   交叉避让: {crossing_processed} 个有效相对位置")
        print(f"   追越避让: {overtaking_processed} 个有效相对位置")

        # 打印统计
        self._print_data_stats()

    def _process_moments(self, moments, target_data_dict):
        """处理机动时刻，提取相对位置"""
        processed_count = 0

        for moment in tqdm(moments, desc="提取相对位置"):
            try:
                # 提取机动船和目标船
                maneuvering_ship = moment[0]  # 机动船（本船）
                target_ship = moment[1]  # 被避让船（目标船）

                # 数据质量检查
                if not self._validate_ship_data(maneuvering_ship, target_ship):
                    continue

                # 获取船长区间
                own_length = maneuvering_ship.get('length', 100.0)
                interval_name = self._get_length_interval(own_length)

                if interval_name is None:
                    continue

                # 计算相对位置
                relative_pos = self._calculate_relative_position(maneuvering_ship, target_ship)

                if relative_pos is not None and self._validate_relative_position(relative_pos):
                    target_data_dict[interval_name].append(relative_pos)
                    processed_count += 1

            except Exception as e:
                if self.debug:
                    print(f"处理机动时刻失败: {e}")
                continue

        return processed_count

    def _validate_ship_data(self, ship1, ship2):
        """验证船舶数据质量"""
        # 检查必要字段
        required_fields = ['mmsi', 'lon', 'lat', 'cog', 'sog', 'length']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if field not in ship or ship[field] is None:
                    return False

        # 检查数值合理性
        if (abs(ship1['lon']) > 180 or abs(ship1['lat']) > 90 or
                abs(ship2['lon']) > 180 or abs(ship2['lat']) > 90):
            return False

        # 检查船舶尺寸合理性
        if (ship1['length'] <= 0 or ship1['length'] > 500 or
                ship2['length'] <= 0 or ship2['length'] > 500):
            return False

        # 检查速度合理性（0-30节）
        if (ship1['sog'] < 0 or ship1['sog'] > 30 or
                ship2['sog'] < 0 or ship2['sog'] > 30):
            return False

        return True

    def _validate_relative_position(self, relative_pos):
        """验证相对位置的合理性"""
        distance = relative_pos['distance']

        # 只检查距离是否为正数
        if distance <= 0:
            return False

        return True

    def _calculate_relative_position(self, own_ship, target_ship):
        """计算相对位置"""
        try:
            # 经纬度转米（简化计算）
            lat_to_m = 111000
            lon_to_m = 111000 * math.cos(math.radians(own_ship['lat']))

            # 计算地理坐标差
            delta_lon = target_ship['lon'] - own_ship['lon']
            delta_lat = target_ship['lat'] - own_ship['lat']

            # 转为米制坐标
            x_geo = delta_lon * lon_to_m
            y_geo = delta_lat * lat_to_m

            # 转换到本船坐标系
            x_rel, y_rel = self._convert_to_ship_coordinate(x_geo, y_geo, own_ship['cog'])

            # 计算距离
            distance = math.sqrt(x_rel ** 2 + y_rel ** 2)

            return {
                'relative_x': x_rel,
                'relative_y': y_rel,
                'distance': distance,
                'own_ship_mmsi': own_ship['mmsi'],
                'target_ship_mmsi': target_ship['mmsi'],
                'own_ship_length': own_ship.get('length', 100.0),
                'time_point': own_ship['time_point']
            }

        except Exception as e:
            if self.debug:
                print(f"计算相对位置失败: {e}")
            return None

    @staticmethod
    def _convert_to_ship_coordinate(x_geo, y_geo, heading):
        """转换到船舶坐标系（船头向前为Y轴正方向）"""
        heading_rad = math.radians(-heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        x_ship = cos_h * x_geo + sin_h * y_geo
        y_ship = -sin_h * x_geo + cos_h * y_geo

        return x_ship, y_ship

    def _get_length_interval(self, length):
        """根据船长获取区间名称 - 修复边界判断"""
        # 特殊处理：确保所有合理长度的船舶都能被分类
        if length <= 0 or length > 600:  # 排除不合理的长度
            return None

        for i, (min_len, max_len, interval_name) in enumerate(self.length_intervals):
            if i == len(self.length_intervals) - 1:
                # 最后一个区间：包含上边界及以上所有船舶
                if length >= min_len:
                    return interval_name
            else:
                # 其他区间：左闭右开
                if min_len <= length < max_len:
                    return interval_name

        return None

    def _print_data_stats(self):
        """打印数据统计"""
        print(f"\n📊 相对位置数据统计:")
        for _, _, interval_name in self.length_intervals:
            crossing_count = len(self.crossing_relative_data[interval_name])
            overtaking_count = len(self.overtaking_relative_data[interval_name])
            print(f"   {interval_name}: 交叉{crossing_count}个, 追越{overtaking_count}个")

    def analyze_probability_density(self):
        """分析概率密度并找到扇区边界"""
        print("\n=== 概率密度分析与边界检测 ===")

        # 分析交叉避让
        print("\n--- 分析交叉避让场景 ---")
        for _, _, interval_name in self.length_intervals:
            relative_data = self.crossing_relative_data[interval_name]

            if len(relative_data) >= 20:
                self._analyze_scenario_density("交叉", interval_name, relative_data)

        # 分析追越避让
        print("\n--- 分析追越避让场景 ---")
        for _, _, interval_name in self.length_intervals:
            relative_data = self.overtaking_relative_data[interval_name]

            if len(relative_data) >= 20:  # 与交叉避让使用相同的最小数据量要求
                self._analyze_scenario_density("追越", interval_name, relative_data)

        print("✅ 概率密度分析与边界检测完成")

    def _analyze_scenario_density(self, scenario_type, length_interval, relative_data):
        """分析单个场景的概率密度"""
        print(f"📊 分析 {scenario_type}-{length_interval} 的概率密度...")

        # 提取坐标
        x_coords = np.array([p['relative_x'] for p in relative_data])
        y_coords = np.array([p['relative_y'] for p in relative_data])

        # 计算2D概率密度
        density_result = self._calculate_2d_density(x_coords, y_coords)

        if density_result is None:
            print(f"⚠️  {scenario_type}-{length_interval}: 密度计算失败")
            return

        # 按扇区分析
        sector_analysis = self._analyze_by_sectors(x_coords, y_coords, density_result, scenario_type)

        # 存储结果
        key = f"{scenario_type}_{length_interval}"
        self.density_results[key] = {
            'x_coords': x_coords,
            'y_coords': y_coords,
            'density_result': density_result,
            'sector_analysis': sector_analysis,
            'data_count': len(relative_data),
            'scenario_type': scenario_type,
            'length_interval': length_interval
        }

        # 提取扇区边界
        boundaries = self._extract_sector_boundaries(sector_analysis, scenario_type)
        if boundaries:
            self.sector_boundaries[key] = boundaries
            print(f"✅ {scenario_type}-{length_interval}: 找到{len(boundaries)}个扇区边界")
        else:
            print(f"⚠️  {scenario_type}-{length_interval}: 未找到有效边界")

    def _calculate_2d_density(self, x_coords, y_coords, grid_size=50):
        """计算2D概率密度"""
        try:
            # 确定网格范围
            x_min, x_max = np.min(x_coords), np.max(x_coords)
            y_min, y_max = np.min(y_coords), np.max(y_coords)

            # 扩展边界
            x_range = x_max - x_min
            y_range = y_max - y_min
            x_min -= x_range * 0.1
            x_max += x_range * 0.1
            y_min -= y_range * 0.1
            y_max += y_range * 0.1

            # 创建网格
            x_grid = np.linspace(x_min, x_max, grid_size)
            y_grid = np.linspace(y_min, y_max, grid_size)
            X, Y = np.meshgrid(x_grid, y_grid)

            # 计算核密度估计
            positions = np.vstack([X.ravel(), Y.ravel()])
            values = np.vstack([x_coords, y_coords])

            # 使用高斯核密度估计
            kernel = stats.gaussian_kde(values)
            density = np.reshape(kernel(positions).T, X.shape)

            # 高斯平滑
            density_smoothed = gaussian_filter(density, sigma=1.0)

            return {
                'X': X,
                'Y': Y,
                'density': density_smoothed,
                'x_grid': x_grid,
                'y_grid': y_grid,
                'x_range': (x_min, x_max),
                'y_range': (y_min, y_max)
            }

        except Exception as e:
            if self.debug:
                print(f"2D密度计算失败: {e}")
            return None

    def _analyze_by_sectors(self, x_coords, y_coords, density_result, scenario_type):
        """重构的扇区分析 - 基于子区域密度变化率"""
        # 1. 根据数据分布自适应调整扇区
        effective_sectors = self._get_adaptive_sectors(x_coords, y_coords, scenario_type)

        sector_results = {}

        for sector_info in effective_sectors:
            start_angle = sector_info['start_angle']
            end_angle = sector_info['end_angle']
            sector_name = sector_info['name']

            # 选择该扇区内的点
            angles = np.arctan2(y_coords, x_coords)

            # 处理角度跨越边界的情况
            if start_angle < end_angle:
                sector_mask = (angles >= start_angle) & (angles < end_angle)
            else:  # 跨越-π/π边界
                sector_mask = (angles >= start_angle) | (angles < end_angle)

            sector_x = x_coords[sector_mask]
            sector_y = y_coords[sector_mask]

            # 数据质量检查
            if not self._validate_sector_data_quality(sector_x, sector_y, scenario_type):
                sector_results[sector_name] = None
                continue

            # 使用新的子区域密度分析方法计算边界
            boundary_info = self._find_sector_boundary_by_subregions(
                sector_x, sector_y, start_angle, end_angle, scenario_type
            )

            sector_results[sector_name] = {
                'x_coords': sector_x,
                'y_coords': sector_y,
                'point_count': len(sector_x),
                'angle_range': (start_angle, end_angle),
                'boundary_info': boundary_info,
                'data_quality': self._assess_sector_data_quality(sector_x, sector_y)
            }

        return sector_results

    def _get_adaptive_sectors(self, x_coords, y_coords, scenario_type):
        """根据数据分布自适应调整扇区"""
        try:
            angles = np.arctan2(y_coords, x_coords)

            # 使用标准扇区处理（交叉和追越相同）
            sectors = []
            for i, (start_angle, end_angle, sector_name) in enumerate(self.sector_angles):
                # 检查该扇区是否有足够数据
                if start_angle < end_angle:
                    sector_mask = (angles >= start_angle) & (angles < end_angle)
                else:
                    sector_mask = (angles >= start_angle) | (angles < end_angle)

                sector_point_count = np.sum(sector_mask)

                # 如果数据点太少，考虑与相邻扇区合并
                if sector_point_count >= 5:  # 最小数据点要求
                    sectors.append({
                        'start_angle': start_angle,
                        'end_angle': end_angle,
                        'name': sector_name
                    })

            return sectors

        except Exception as e:
            if self.debug:
                print(f"自适应扇区调整失败: {e}")
            # 返回标准扇区
            return [{'start_angle': s[0], 'end_angle': s[1], 'name': s[2]}
                    for s in self.sector_angles]

    def _validate_sector_data_quality(self, sector_x, sector_y, scenario_type):
        """验证扇区数据质量"""
        min_points = 5  # 交叉和追越使用相同的最小点数要求

        if len(sector_x) < min_points:
            return False

        # 检查数据分布是否合理
        distances = np.sqrt(sector_x ** 2 + sector_y ** 2)

        # 距离范围检查
        if np.max(distances) - np.min(distances) < 20:  # 距离变化太小
            return False

        # 异常值比例检查
        Q1, Q3 = np.percentile(distances, [25, 75])
        IQR = Q3 - Q1
        outlier_mask = (distances < Q1 - 1.5 * IQR) | (distances > Q3 + 1.5 * IQR)
        outlier_ratio = np.sum(outlier_mask) / len(distances)

        if outlier_ratio > 0.3:  # 异常值太多
            return False

        return True

    def _assess_sector_data_quality(self, sector_x, sector_y):
        """评估扇区数据质量"""
        try:
            distances = np.sqrt(sector_x ** 2 + sector_y ** 2)

            quality_score = 0.0

            # 1. 数据点数量 (40%)
            point_count_score = min(1.0, len(distances) / 20.0)
            quality_score += point_count_score * 0.4

            # 2. 数据分布均匀性 (30%)
            if len(distances) > 1:
                cv = np.std(distances) / np.mean(distances)  # 变异系数
                uniformity_score = max(0, 1.0 - cv)
                quality_score += uniformity_score * 0.3

            # 3. 距离范围合理性 (30%)
            distance_range = np.max(distances) - np.min(distances)
            range_score = min(1.0, distance_range / 200.0)  # 200米为理想范围
            quality_score += range_score * 0.3

            return {
                'overall_score': quality_score,
                'point_count': len(distances),
                'distance_range': distance_range,
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances)
            }

        except Exception as e:
            if self.debug:
                print(f"数据质量评估失败: {e}")
            return {'overall_score': 0.0}

    def _find_sector_boundary_by_subregions(self, sector_x, sector_y, start_angle, end_angle, scenario_type):
        """基于累积子区域密度变化率的边界寻找方法"""
        try:
            if len(sector_x) < 5:
                return None

            # 计算距离统计信息（保留作为备选）
            distances = np.sqrt(sector_x ** 2 + sector_y ** 2)

            # 1. 将扇区按距离划分为子区域（交叉小于100m：50， 追越小于100m：22, 追越\交叉100-200m：30）
            subregions = self._divide_sector_into_subregions(
                sector_x, sector_y, start_angle, end_angle, distance_interval=22
            )

            if not subregions or len(subregions) < 3:
                # 如果子区域太少，返回None
                return None

            # 2. 计算累积子区域的密度变化
            cumulative_densities = self._calculate_cumulative_subregion_densities(subregions)

            # 3. 寻找累积密度变化率最大的子区域边界
            boundary_subregion = self._find_max_cumulative_density_change_subregion(cumulative_densities)

            if boundary_subregion is None:
                # 如果找不到边界，返回None
                return None

            # 4. 计算边界点（子区域弧形中间的点）
            boundary_point = self._calculate_boundary_point_from_subregion(
                boundary_subregion, start_angle, end_angle
            )

            return {
                'subregion_boundary': boundary_point,
                'subregions': subregions,
                'cumulative_densities': cumulative_densities,
                'boundary_subregion': boundary_subregion,
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances),
                'max_distance': np.max(distances),
                'method': 'cumulative_density_change',
                'scenario_type': scenario_type
            }

        except Exception as e:
            if self.debug:
                print(f"累积子区域边界计算失败: {e}")
            return None

    def _divide_sector_into_subregions(self, sector_x, sector_y, start_angle, end_angle, distance_interval=10):
        """将扇区按距离划分为子区域"""
        try:
            # 计算扇区内所有点的距离
            distances = np.sqrt(sector_x ** 2 + sector_y ** 2)

            if len(distances) == 0:
                return []

            # 确定距离范围
            min_distance = max(distance_interval, np.min(distances))  # 最小从10m开始
            max_distance = np.max(distances)

            # 按距离间隔划分子区域
            subregions = []
            current_distance = min_distance

            while current_distance < max_distance:
                next_distance = current_distance + distance_interval

                # 找到该距离范围内的点
                distance_mask = (distances >= current_distance) & (distances < next_distance)
                subregion_x = sector_x[distance_mask]
                subregion_y = sector_y[distance_mask]

                if len(subregion_x) > 0:  # 只保留有数据点的子区域
                    # 计算子区域的几何信息
                    subregion_info = {
                        'distance_range': (current_distance, next_distance),
                        'center_distance': (current_distance + next_distance) / 2,
                        'x_coords': subregion_x,
                        'y_coords': subregion_y,
                        'point_count': len(subregion_x),
                        'angle_range': (start_angle, end_angle),
                        'area': self._calculate_subregion_area(
                            current_distance, next_distance, start_angle, end_angle
                        )
                    }
                    subregions.append(subregion_info)

                current_distance = next_distance

            return subregions

        except Exception as e:
            if self.debug:
                print(f"子区域划分失败: {e}")
            return []

    def _calculate_subregion_area(self, inner_radius, outer_radius, start_angle, end_angle):
        """计算子区域（扇环）的面积"""
        try:
            # 处理角度跨越边界的情况
            if start_angle > end_angle:
                angle_span = (2 * np.pi - start_angle) + end_angle
            else:
                angle_span = end_angle - start_angle

            # 扇环面积 = (外圆面积 - 内圆面积) * (角度跨度 / 2π)
            outer_area = np.pi * outer_radius ** 2
            inner_area = np.pi * inner_radius ** 2
            sector_area = (outer_area - inner_area) * (angle_span / (2 * np.pi))

            return max(sector_area, 1.0)  # 避免除零错误

        except Exception as e:
            if self.debug:
                print(f"子区域面积计算失败: {e}")
            return 1.0

    def _calculate_cumulative_subregion_densities(self, subregions):
        """计算累积子区域的密度变化（新方法）"""
        try:
            if not subregions:
                return []

            # 按距离排序子区域（从近到远）
            sorted_subregions = sorted(subregions, key=lambda x: x['center_distance'])

            cumulative_densities = []
            cumulative_points = 0
            cumulative_area = 0

            for i, subregion in enumerate(sorted_subregions):
                # 累积加入当前子区域的点和面积
                cumulative_points += subregion['point_count']
                cumulative_area += subregion['area']

                # 计算累积密度
                cumulative_density = cumulative_points / cumulative_area if cumulative_area > 0 else 0

                # 计算密度变化率（与前一个累积状态比较）
                density_change_rate = 0
                if i > 0:
                    prev_density = cumulative_densities[i - 1]['cumulative_density']
                    if prev_density > 0:
                        # density_change_rate = abs(cumulative_density - prev_density) / prev_density
                        density_change_rate = abs(cumulative_density - prev_density)

                density_info = {
                    'subregion_index': i,
                    'cumulative_density': cumulative_density,
                    'density_change_rate': density_change_rate,
                    'cumulative_points': cumulative_points,
                    'cumulative_area': cumulative_area,
                    'boundary_distance': subregion['distance_range'][1],  # 使用外边界作为边界距离
                    'center_distance': subregion['center_distance'],
                    'distance_range': subregion['distance_range'],
                    'current_subregion': subregion
                }
                cumulative_densities.append(density_info)

            return cumulative_densities

        except Exception as e:
            if self.debug:
                print(f"累积子区域密度计算失败: {e}")
            return []

    def _find_max_cumulative_density_change_subregion(self, cumulative_densities):
        """寻找累积密度变化率最大的子区域边界（新方法）"""
        try:
            if len(cumulative_densities) < 2:
                return None

            max_change_rate = 0
            boundary_subregion = None

            # 寻找累积密度变化率最大的点
            for i, density_info in enumerate(cumulative_densities):
                change_rate = density_info['density_change_rate']

                if change_rate > max_change_rate:
                    max_change_rate = change_rate
                    boundary_subregion = density_info.copy()
                    boundary_subregion['change_type'] = 'cumulative_gradient'

            # # 如果没有找到明显的变化，寻找累积密度下降最明显的点sector_analysis
            # if boundary_subregion is None and len(cumulative_densities) > 1:
            #     max_drop_rate = 0
            #     for i in range(1, len(cumulative_densities)):
            #         prev_density = cumulative_densities[i - 1]['cumulative_density']
            #         curr_density = cumulative_densities[i]['cumulative_density']
            #
            #         if prev_density > 0:
            #             drop_rate = (prev_density - curr_density) / prev_density
            #             if drop_rate > max_drop_rate:
            #                 max_drop_rate = drop_rate
            #                 boundary_subregion = cumulative_densities[i].copy()
            #                 boundary_subregion['change_rate'] = drop_rate
            #                 boundary_subregion['change_type'] = 'cumulative_drop'
            #
            # # 如果仍然没有找到，使用中位数位置作为边界
            # if boundary_subregion is None:
            #     mid_index = len(cumulative_densities) // 2
            #     boundary_subregion = cumulative_densities[mid_index].copy()
            #     boundary_subregion['change_rate'] = 0.1  # 给一个较低的置信度
            #     boundary_subregion['change_type'] = 'median_fallback'

            return boundary_subregion

        except Exception as e:
            if self.debug:
                print(f"累积密度变化率计算失败: {e}")
            return None

    def _calculate_boundary_point_from_subregion(self, boundary_subregion, start_angle, end_angle):
        """从边界子区域计算边界点（弧形中间的点）- 支持累积密度方法"""
        try:
            # 获取边界距离 - 优先使用boundary_distance，回退到center_distance
            if 'boundary_distance' in boundary_subregion:
                boundary_distance = boundary_subregion['boundary_distance']
            else:
                boundary_distance = boundary_subregion.get('center_distance', 0)

            # 使用扇区的角度范围计算中心角度
            if start_angle <= end_angle:
                center_angle = (start_angle + end_angle) / 2
            else:  # 跨越-π/π边界的情况
                center_angle = (start_angle + end_angle + 2 * np.pi) / 2
                if center_angle > np.pi:
                    center_angle -= 2 * np.pi

            # 如果是累积密度方法，边界点应该在子区域的外边界
            # 这样更准确地表示密度变化的边界位置

            # 计算边界点坐标
            boundary_x = boundary_distance * np.cos(center_angle)
            boundary_y = boundary_distance * np.sin(center_angle)

            # 获取置信度和方法信息
            confidence = boundary_subregion.get('change_rate', boundary_subregion.get('density_change_rate', 0))
            change_type = boundary_subregion.get('change_type', 'unknown')

            # 根据方法类型调整方法名称
            if 'cumulative' in change_type:
                method_name = f"cumulative_{change_type}"
            else:
                method_name = f"subregion_{change_type}"

            return {
                'boundary_distance': boundary_distance,
                'boundary_angle': center_angle,
                'boundary_x': boundary_x,
                'boundary_y': boundary_y,
                'confidence': confidence,
                'method': method_name,
                'cumulative_points': boundary_subregion.get('cumulative_points', 0),
                'cumulative_area': boundary_subregion.get('cumulative_area', 0),
                'cumulative_density': boundary_subregion.get('cumulative_density', 0)
            }

        except Exception as e:
            if self.debug:
                print(f"边界点计算失败: {e}")
            return None

    def _extract_sector_boundaries(self, sector_analysis, scenario_type):
        """提取扇区边界信息 - 支持新的子区域边界方法"""
        boundaries = {}

        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and sector_data['boundary_info'] is not None:
                boundary_info = sector_data['boundary_info']

                # 使用累积密度边界方法
                if 'subregion_boundary' in boundary_info and boundary_info['subregion_boundary'] is not None:
                    subregion_boundary = boundary_info['subregion_boundary']

                    # 追越避让的特殊处理：只保留Y轴上半部分的边界点
                    if scenario_type == "追越":
                        boundary_y = subregion_boundary['boundary_y']
                        if boundary_y < 0:  # 过滤掉Y轴下半部分的边界点
                            if self.debug:
                                print(f"   追越场景过滤掉下半部分边界点: {sector_name} "
                                      f"({subregion_boundary['boundary_x']:.1f}, {boundary_y:.1f})")
                            continue

                    boundaries[sector_name] = {
                        'boundary_distance': subregion_boundary['boundary_distance'],
                        'boundary_angle': subregion_boundary['boundary_angle'],
                        'boundary_x': subregion_boundary['boundary_x'],
                        'boundary_y': subregion_boundary['boundary_y'],
                        'angle_range': sector_data['angle_range'],
                        'point_count': sector_data['point_count'],
                        'confidence': subregion_boundary['confidence'],
                        'method': subregion_boundary['method'],
                        'cumulative_points': subregion_boundary.get('cumulative_points', 0),
                        'cumulative_density': subregion_boundary.get('cumulative_density', 0)
                    }

        return boundaries

    def visualize_results(self):
        """可视化概率密度分析结果"""
        print("\n=== 生成可视化结果 ===")

        vis_dir = Path("vis/test/probability_density")
        vis_dir.mkdir(parents=True, exist_ok=True)

        # for key, result in self.density_results.items():
        #     scenario_type = result['scenario_type']
        #     length_interval = result['length_interval']
        #
        #     # 创建综合分析图
        #     self._create_comprehensive_plot(result, key, vis_dir)

        print(f"✅ 可视化结果保存至: {vis_dir}")

    def _create_comprehensive_plot(self, result, key, vis_dir):
        """创建综合分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'{result["scenario_type"]}避让 - {result["length_interval"]} 概率密度分析',
                     fontsize=16, fontweight='bold')

        # 1. 原始数据散点图
        ax1 = axes[0, 0]
        ax1.scatter(result['x_coords'], result['y_coords'], alpha=0.5, s=1, c='steelblue')
        ax1.set_title(f'原始数据分布 (n={len(result["x_coords"])})')
        ax1.set_xlabel('横向距离 (m)')
        ax1.set_ylabel('纵向距离 (m)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')

        # 2. 概率密度热力图
        ax2 = axes[0, 1]
        density_data = result['density_result']
        im = ax2.contourf(density_data['X'], density_data['Y'],
                          density_data['density'], levels=20, cmap='viridis')
        plt.colorbar(im, ax=ax2, label='概率密度')
        ax2.set_title('概率密度分布')
        ax2.set_xlabel('横向距离 (m)')
        ax2.set_ylabel('纵向距离 (m)')

        # 3. 扇区划分
        ax3 = axes[1, 0]
        self._plot_sectors(ax3, result)

        # 4. 扇区边界
        ax4 = axes[1, 1]
        self._plot_boundaries(ax4, result, key)

        plt.tight_layout()

        # 保存图片
        filename = f"{result['scenario_type']}_{result['length_interval']}_density_analysis.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {key} 分析图保存至: {save_path}")

    def _plot_sectors(self, ax, result):
        """绘制扇区划分"""
        sector_analysis = result['sector_analysis']
        colors = plt.cm.Set3(np.linspace(0, 1, self.num_sectors))

        for i, (sector_name, sector_data) in enumerate(sector_analysis.items()):
            if sector_data is not None:
                ax.scatter(sector_data['x_coords'], sector_data['y_coords'],
                           c=[colors[i]], alpha=0.6, s=2, label=f"{sector_name}({sector_data['point_count']})")

        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.set_title(f'扇区划分 ({self.num_sectors}个扇区)')
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

    def _plot_boundaries(self, ax, result, key):
        """绘制扇区边界 - 支持子区域边界可视化"""
        # 绘制原始数据
        ax.scatter(result['x_coords'], result['y_coords'],
                   alpha=0.3, s=1, c='lightgray', label='原始数据')

        # 绘制边界
        if key in self.sector_boundaries:
            boundaries = self.sector_boundaries[key]
            boundary_count = 0

            for sector_name, boundary_info in boundaries.items():
                distance = boundary_info['boundary_distance']
                method = boundary_info['method']

                # 使用新的边界坐标（如果可用）
                if 'boundary_x' in boundary_info and 'boundary_y' in boundary_info:
                    bx = boundary_info['boundary_x']
                    by = boundary_info['boundary_y']
                else:
                    # 回退到角度计算
                    angle = boundary_info['boundary_angle']
                    bx = distance * np.cos(angle)
                    by = distance * np.sin(angle)

                # 根据方法选择颜色和标记
                if 'cumulative' in method:
                    color = 'red'
                    marker = 'D'  # 菱形标记表示累积密度方法
                    alpha = 0.9
                else:
                    color = 'blue'
                    marker = 'o'  # 圆形标记表示其他方法
                    alpha = 0.8

                ax.plot(bx, by, marker, color=color, markersize=8, alpha=alpha)

                # 添加置信度信息（如果可用）
                confidence_text = ""
                if 'confidence' in boundary_info:
                    confidence_text = f"\n置信度:{boundary_info['confidence']:.2f}"
                #
                # ax.text(bx * 1.1, by * 1.1,
                #         f'{sector_name}\n{distance:.0f}m\n{method[:10]}{confidence_text}',
                #         ha='center', va='center', fontsize=6,
                #         bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

                boundary_count += 1

        ax.set_title(f'扇区边界检测 (找到{boundary_count}个边界)')
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

        # 添加图例
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='D', color='red', linestyle='None',
                   markersize=8, alpha=0.9, label='累积密度方法'),
            Line2D([0], [0], marker='o', color='blue', linestyle='None',
                   markersize=8, alpha=0.8, label='其他方法')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=8)

    def save_results(self):
        """保存分析结果"""
        print("\n=== 保存分析结果 ===")

        result_dir = Path("result/probability_density")
        result_dir.mkdir(parents=True, exist_ok=True)

        # 保存主结果
        results = {
            'density_results': self.density_results,
            'sector_boundaries': self.sector_boundaries,
            'length_intervals': self.length_intervals,
            'num_sectors': self.num_sectors,
            'data_months': self.data_months
        }

        result_file = result_dir / "sector_boundaries_results.pkl"
        with open(result_file, 'wb') as f:
            pickle.dump(results, f)

        print(f"✅ 扇区边界结果保存至: {result_file}")

        return result_file

    def run_full_analysis(self):
        """运行完整的概率密度分析"""
        print("🔍 开始概率密度分析...")
        print("=" * 60)

        try:
            # 执行分析流程
            self.load_maneuvering_moments()
            self.extract_relative_positions()
            self.analyze_probability_density()
            self.visualize_results()
            result_file = self.save_results()

            print("\n" + "=" * 60)
            print("🎉 概率密度分析完成！")
            print(f"📁 主要输出:")
            print(f"   扇区边界结果: {result_file}")
            print(f"   可视化图片: vis/probability_density/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 概率密度分析失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数"""
    data_months = ['2024_1', '2024_2', '2024_3']

    print(f"🔍 概率密度分析系统")
    print(f"   数据月份: {data_months}")
    print(f"   扇区数量: 8")
    print(f"   输入文件: crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl")
    print(f"   输出文件: sector_boundaries_results.pkl")

    # 创建分析器
    analyzer = ProbabilityDensityAnalyzer(
        data_months=data_months,
        num_sectors=36,
        debug=False
    )

    # 运行完整分析
    success = analyzer.run_full_analysis()

    if success:
        print(f"\n📁 主要输出文件:")
        print(f"   扇区边界: result/probability_density/sector_boundaries_results.pkl")
        print(f"   可视化图: vis/probability_density/")
    else:
        print(f"\n❌ 分析失败")


if __name__ == '__main__':
    main()
